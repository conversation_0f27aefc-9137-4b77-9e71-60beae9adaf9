# -*- coding: utf-8 -*-
"""
اختبار نظام تحسين الصور المحسن
"""

import os
import sys
from PIL import Image
from io import BytesIO

def create_test_image(width=400, height=300, format='JPEG'):
    """إنشاء صورة اختبار"""
    # إنشاء صورة ملونة للاختبار
    img = Image.new('RGB', (width, height), color='blue')
    
    # إضافة بعض التفاصيل
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # رسم مربعات ملونة
    draw.rectangle([50, 50, 150, 150], fill='red')
    draw.rectangle([200, 100, 300, 200], fill='green')
    draw.rectangle([100, 180, 200, 280], fill='yellow')
    
    # رسم خطوط
    for i in range(0, width, 20):
        draw.line([(i, 0), (i, height)], fill='white', width=1)
    for i in range(0, height, 20):
        draw.line([(0, i), (width, i)], fill='white', width=1)
    
    # حفظ في buffer
    buffer = BytesIO()
    img.save(buffer, format=format, quality=85)
    return buffer.getvalue()

def test_advanced_enhancer():
    """اختبار نظام التحسين المتقدم"""
    print("🔄 اختبار نظام التحسين المتقدم...")
    
    try:
        from advanced_image_enhancer import enhance_image_advanced, get_available_enhancements
        
        # إنشاء صورة اختبار
        test_image_bytes = create_test_image()
        original_size = len(test_image_bytes)
        print(f"📏 حجم الصورة الأصلية: {original_size} بايت")
        
        # الحصول على قائمة التحسينات المتاحة
        enhancements = get_available_enhancements()
        print(f"🎯 التحسينات المتاحة: {list(enhancements.keys())}")
        
        # اختبار كل نوع تحسين
        for enhancement_type, description in enhancements.items():
            print(f"\n🔄 اختبار {enhancement_type}: {description}")
            
            try:
                enhanced_bytes = enhance_image_advanced(test_image_bytes, enhancement_type, "image/jpeg")
                
                if enhanced_bytes and len(enhanced_bytes) > 0:
                    enhanced_size = len(enhanced_bytes)
                    size_change = ((enhanced_size - original_size) / original_size) * 100
                    print(f"✅ نجح التحسين. الحجم الجديد: {enhanced_size} بايت ({size_change:+.1f}%)")
                    
                    # حفظ الصورة المحسنة للمراجعة
                    filename = f"test_enhanced_{enhancement_type}.jpg"
                    with open(filename, 'wb') as f:
                        f.write(enhanced_bytes)
                    print(f"💾 تم حفظ الصورة في: {filename}")
                else:
                    print(f"❌ فشل التحسين - لا توجد بيانات")
                    
            except Exception as e:
                print(f"❌ خطأ في التحسين: {e}")
        
        # حفظ الصورة الأصلية للمقارنة
        with open('test_original.jpg', 'wb') as f:
            f.write(test_image_bytes)
        print("💾 تم حفظ الصورة الأصلية في: test_original.jpg")
        
        return True
        
    except ImportError:
        print("❌ نظام التحسين المتقدم غير متاح")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام المتقدم: {e}")
        return False

def test_compression_toggle():
    """اختبار زر تعطيل الضغط"""
    print("\n🔄 اختبار نظام تعطيل الضغط...")
    
    try:
        # محاكاة تغيير إعداد الضغط التلقائي
        import mod_processor_broken_final
        
        # اختبار تفعيل الضغط
        mod_processor_broken_final.auto_compression_enabled = True
        print("✅ تم تفعيل الضغط التلقائي")
        
        # اختبار تعطيل الضغط
        mod_processor_broken_final.auto_compression_enabled = False
        print("✅ تم تعطيل الضغط التلقائي")
        
        # إعادة تفعيل الضغط
        mod_processor_broken_final.auto_compression_enabled = True
        print("✅ تم إعادة تفعيل الضغط التلقائي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تعطيل الضغط: {e}")
        return False

def test_basic_enhancement():
    """اختبار النظام الأساسي للتحسين"""
    print("\n🔄 اختبار النظام الأساسي للتحسين...")
    
    try:
        from mod_processor_broken_final import local_image_enhancement
        
        # إنشاء صورة اختبار
        test_image_bytes = create_test_image()
        original_size = len(test_image_bytes)
        print(f"📏 حجم الصورة الأصلية: {original_size} بايت")
        
        # اختبار التحسين التلقائي
        print("🔄 اختبار التحسين التلقائي...")
        enhanced_auto = local_image_enhancement("auto_enhance", test_image_bytes, "image/jpeg")
        if enhanced_auto and len(enhanced_auto) > 0:
            enhanced_size = len(enhanced_auto)
            print(f"✅ نجح التحسين التلقائي. الحجم الجديد: {enhanced_size} بايت")
            
            with open('test_basic_auto_enhanced.jpg', 'wb') as f:
                f.write(enhanced_auto)
            print("💾 تم حفظ الصورة في: test_basic_auto_enhanced.jpg")
        else:
            print("❌ فشل التحسين التلقائي")
        
        # اختبار الدقة الفائقة
        print("🔄 اختبار الدقة الفائقة...")
        enhanced_super = local_image_enhancement("super_resolution", test_image_bytes, "image/jpeg")
        if enhanced_super and len(enhanced_super) > 0:
            enhanced_size = len(enhanced_super)
            print(f"✅ نجح تحسين الدقة الفائقة. الحجم الجديد: {enhanced_size} بايت")
            
            with open('test_basic_super_resolution.jpg', 'wb') as f:
                f.write(enhanced_super)
            print("💾 تم حفظ الصورة في: test_basic_super_resolution.jpg")
        else:
            print("❌ فشل تحسين الدقة الفائقة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الأساسي: {e}")
        return False

def test_opencv_availability():
    """اختبار توفر OpenCV"""
    print("\n🔄 اختبار توفر OpenCV...")
    
    try:
        import cv2
        import numpy as np
        
        print(f"✅ OpenCV متوفر - الإصدار: {cv2.__version__}")
        
        # اختبار بسيط لـ OpenCV
        test_array = np.zeros((100, 100, 3), dtype=np.uint8)
        blurred = cv2.GaussianBlur(test_array, (5, 5), 0)
        print("✅ اختبار OpenCV نجح")
        
        return True
        
    except ImportError:
        print("❌ OpenCV غير متوفر")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار OpenCV: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام تحسين الصور المحسن")
    print("=" * 60)
    
    # اختبار توفر المكتبات
    opencv_available = test_opencv_availability()
    
    # اختبار النظام المتقدم
    advanced_test = test_advanced_enhancer()
    
    # اختبار النظام الأساسي
    basic_test = test_basic_enhancement()
    
    # اختبار تعطيل الضغط
    compression_test = test_compression_toggle()
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print("=" * 60)
    
    print(f"🔧 OpenCV: {'✅ متوفر' if opencv_available else '❌ غير متوفر'}")
    print(f"🚀 النظام المتقدم: {'✅ يعمل' if advanced_test else '❌ لا يعمل'}")
    print(f"🔧 النظام الأساسي: {'✅ يعمل' if basic_test else '❌ لا يعمل'}")
    print(f"⚙️ تعطيل الضغط: {'✅ يعمل' if compression_test else '❌ لا يعمل'}")
    
    if advanced_test and basic_test and compression_test:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("\n📋 الميزات الجديدة:")
        print("   • نظام تحسين صور متقدم باستخدام OpenCV")
        print("   • خيارات تحسين متعددة (إزالة ضوضاء، تحسين حدة، تكبير)")
        print("   • زر تعطيل الضغط التلقائي")
        print("   • تحسين جودة الصور بخوارزميات متطورة")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("\n📁 ملفات الاختبار المحفوظة:")
    test_files = [
        'test_original.jpg',
        'test_enhanced_auto_enhance.jpg',
        'test_enhanced_super_resolution.jpg',
        'test_enhanced_denoise.jpg',
        'test_enhanced_sharpen.jpg',
        'test_enhanced_contrast_enhance.jpg',
        'test_enhanced_color_enhance.jpg',
        'test_enhanced_upscale_2x.jpg',
        'test_enhanced_upscale_4x.jpg',
        'test_basic_auto_enhanced.jpg',
        'test_basic_super_resolution.jpg'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            print(f"   • {file}")

if __name__ == "__main__":
    main()
