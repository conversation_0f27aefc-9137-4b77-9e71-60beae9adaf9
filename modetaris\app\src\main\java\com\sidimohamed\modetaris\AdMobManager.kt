package com.sidimohamed.modetaris

import android.app.Activity
import android.content.Context
import android.util.Log
import android.view.ViewGroup
import android.widget.LinearLayout
import com.google.android.gms.ads.*
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAd
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAdLoadCallback

/**
 * AdMob Manager class to handle Google AdMob SDK initialization and ads
 */
class AdMobManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AdMobManager"
        
        // AdMob Ad Unit IDs
        private const val BANNER_AD_UNIT_ID = "ca-app-pub-4373910379376809/6035179861"
        private const val REWARDED_INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-4373910379376809/8741234256"
        
        // Test Ad Unit IDs (for development)
        private const val TEST_BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111"
        private const val TEST_REWARDED_INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/5354046379"
        
        // Test mode - set to false for production
        private const val TEST_MODE = false
    }
    
    private var isInitialized = false
    private var bannerAd: AdView? = null
    private var rewardedInterstitialAd: RewardedInterstitialAd? = null
    private var isRewardedInterstitialAdLoaded = false
    
    // Callbacks
    private var onRewardedAdCompleted: (() -> Unit)? = null
    private var onRewardedAdFailed: ((String) -> Unit)? = null
    
    /**
     * Initialize AdMob SDK
     */
    fun initialize(onInitializationComplete: ((Boolean) -> Unit)? = null) {
        if (isInitialized) {
            Log.d(TAG, "AdMob already initialized")
            onInitializationComplete?.invoke(true)
            return
        }

        Log.i(TAG, "Initializing AdMob SDK...")

        MobileAds.initialize(context) { initializationStatus ->
            Log.i(TAG, "✅ AdMob initialization completed!")
            Log.d(TAG, "Initialization status: ${initializationStatus.adapterStatusMap}")
            isInitialized = true
            
            // Load rewarded interstitial ad after initialization
            loadRewardedInterstitialAd()
            
            onInitializationComplete?.invoke(true)
        }
    }
    
    /**
     * Create and load banner ad
     */
    fun createBannerAd(container: ViewGroup): AdView? {
        if (!isInitialized) {
            Log.w(TAG, "AdMob not initialized yet")
            return null
        }
        
        try {
            val bannerAdUnitId = if (TEST_MODE) TEST_BANNER_AD_UNIT_ID else BANNER_AD_UNIT_ID

            bannerAd = AdView(context).apply {
                setAdSize(AdSize.BANNER)
                adUnitId = bannerAdUnitId
                
                adListener = object : AdListener() {
                    override fun onAdClicked() {
                        Log.d(TAG, "Banner ad clicked")
                    }
                    
                    override fun onAdClosed() {
                        Log.d(TAG, "Banner ad closed")
                    }
                    
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        Log.e(TAG, "Banner ad failed to load: ${adError.message}")
                    }
                    
                    override fun onAdImpression() {
                        Log.d(TAG, "Banner ad impression recorded")
                    }
                    
                    override fun onAdLoaded() {
                        Log.d(TAG, "✅ Banner ad loaded successfully")
                    }
                    
                    override fun onAdOpened() {
                        Log.d(TAG, "Banner ad opened")
                    }
                }
            }
            
            // Add banner to container
            val layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            container.addView(bannerAd, layoutParams)
            
            // Load the ad
            val adRequest = AdRequest.Builder().build()
            bannerAd?.loadAd(adRequest)
            
            Log.i(TAG, "Banner ad created and loading...")
            return bannerAd
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating banner ad", e)
            return null
        }
    }
    
    /**
     * Load rewarded interstitial ad
     */
    private fun loadRewardedInterstitialAd() {
        if (!isInitialized) {
            Log.w(TAG, "AdMob not initialized, cannot load rewarded interstitial ad")
            return
        }
        
        if (isRewardedInterstitialAdLoaded) {
            Log.d(TAG, "Rewarded interstitial ad already loaded")
            return
        }
        
        val adUnitId = if (TEST_MODE) TEST_REWARDED_INTERSTITIAL_AD_UNIT_ID else REWARDED_INTERSTITIAL_AD_UNIT_ID
        val adRequest = AdRequest.Builder().build()
        
        Log.i(TAG, "Loading rewarded interstitial ad...")
        
        RewardedInterstitialAd.load(
            context,
            adUnitId,
            adRequest,
            object : RewardedInterstitialAdLoadCallback() {
                override fun onAdFailedToLoad(adError: LoadAdError) {
                    Log.e(TAG, "❌ Rewarded interstitial ad failed to load: ${adError.message}")
                    rewardedInterstitialAd = null
                    isRewardedInterstitialAdLoaded = false
                }
                
                override fun onAdLoaded(ad: RewardedInterstitialAd) {
                    Log.i(TAG, "✅ Rewarded interstitial ad loaded successfully")
                    rewardedInterstitialAd = ad
                    isRewardedInterstitialAdLoaded = true
                }
            }
        )
    }
    
    /**
     * Show rewarded interstitial ad
     */
    fun showRewardedInterstitialAd(
        activity: Activity,
        onCompleted: () -> Unit,
        onFailed: (String) -> Unit
    ) {
        if (!isInitialized) {
            Log.w(TAG, "AdMob not initialized")
            onFailed("AdMob not initialized")
            return
        }
        
        if (!isRewardedInterstitialAdLoaded || rewardedInterstitialAd == null) {
            Log.w(TAG, "Rewarded interstitial ad not ready")
            onFailed("Ad not ready")
            // Try to load a new ad
            loadRewardedInterstitialAd()
            return
        }
        
        // Store callbacks
        onRewardedAdCompleted = onCompleted
        onRewardedAdFailed = onFailed
        
        rewardedInterstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdClicked() {
                Log.d(TAG, "Rewarded interstitial ad clicked")
            }
            
            override fun onAdDismissedFullScreenContent() {
                Log.d(TAG, "Rewarded interstitial ad dismissed")
                rewardedInterstitialAd = null
                isRewardedInterstitialAdLoaded = false
                // Load next ad
                loadRewardedInterstitialAd()
            }
            
            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                Log.e(TAG, "❌ Rewarded interstitial ad failed to show: ${adError.message}")
                rewardedInterstitialAd = null
                isRewardedInterstitialAdLoaded = false
                onRewardedAdFailed?.invoke(adError.message)
                // Load next ad
                loadRewardedInterstitialAd()
            }
            
            override fun onAdImpression() {
                Log.d(TAG, "Rewarded interstitial ad impression recorded")
            }
            
            override fun onAdShowedFullScreenContent() {
                Log.d(TAG, "Rewarded interstitial ad showed full screen content")
            }
        }
        
        Log.i(TAG, "🎬 Showing rewarded interstitial ad...")
        rewardedInterstitialAd?.show(activity) { rewardItem ->
            Log.i(TAG, "✅ User earned reward: ${rewardItem.amount} ${rewardItem.type}")
            onRewardedAdCompleted?.invoke()
        }
    }
    
    /**
     * Check if rewarded interstitial ad is ready
     */
    fun isRewardedInterstitialAdReady(): Boolean {
        return isRewardedInterstitialAdLoaded && rewardedInterstitialAd != null
    }
    
    /**
     * Force reload rewarded interstitial ad
     */
    fun forceReloadRewardedInterstitialAd() {
        Log.i(TAG, "🔄 Force reloading rewarded interstitial ad...")
        isRewardedInterstitialAdLoaded = false
        rewardedInterstitialAd = null
        loadRewardedInterstitialAd()
    }
    
    /**
     * Destroy banner ad
     */
    fun destroyBannerAd() {
        bannerAd?.destroy()
        bannerAd = null
        Log.d(TAG, "Banner ad destroyed")
    }
    
    /**
     * Get current status for debugging
     */
    fun getStatus(): String {
        return "AdMob Status: Initialized=$isInitialized, " +
                "BannerLoaded=${bannerAd != null}, " +
                "RewardedInterstitialLoaded=$isRewardedInterstitialAdLoaded, " +
                "TestMode=$TEST_MODE"
    }
    
    /**
     * Get detailed diagnostic information
     */
    fun getDiagnosticInfo(): String {
        val sb = StringBuilder()
        sb.appendLine("=== AdMob Diagnostic Information ===")
        sb.appendLine("SDK Version: 23.5.0")
        sb.appendLine("Test Mode: $TEST_MODE")
        sb.appendLine("Initialized: $isInitialized")
        sb.appendLine("Banner Ad Loaded: ${bannerAd != null}")
        sb.appendLine("Rewarded Interstitial Ad Loaded: $isRewardedInterstitialAdLoaded")
        sb.appendLine("Ad Unit IDs:")
        sb.appendLine("  - Banner: ${if (TEST_MODE) TEST_BANNER_AD_UNIT_ID else BANNER_AD_UNIT_ID}")
        sb.appendLine("  - Rewarded Interstitial: ${if (TEST_MODE) TEST_REWARDED_INTERSTITIAL_AD_UNIT_ID else REWARDED_INTERSTITIAL_AD_UNIT_ID}")
        sb.appendLine("=========================================")
        return sb.toString()
    }
    
    /**
     * Log detailed diagnostic information
     */
    fun logDiagnosticInfo() {
        Log.d(TAG, getDiagnosticInfo())
    }
}
