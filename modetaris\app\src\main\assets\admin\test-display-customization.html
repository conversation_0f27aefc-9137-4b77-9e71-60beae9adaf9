<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تخصيص العرض - المنشئ الذكي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid #ffd700;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #ffd700;
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .test-header p {
            color: #ccc;
            font-size: 1.1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #ffd700;
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.2);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: #ffd700;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            color: #ffd700;
        }

        .feature-description {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .feature-list i {
            color: #22c55e;
            width: 16px;
        }

        .test-button {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: #000;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .demo-section h3 {
            color: #ffd700;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .demo-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 215, 0, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-item:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .demo-item i {
            font-size: 1.5rem;
            color: #ffd700;
            margin-bottom: 8px;
        }

        .demo-item h5 {
            color: #fff;
            margin-bottom: 5px;
        }

        .demo-item p {
            color: #ccc;
            font-size: 0.8rem;
        }

        .info-banner {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .comparison-table th {
            background: rgba(255, 215, 0, 0.2);
            color: #ffd700;
            font-weight: bold;
        }

        .comparison-table .feature-col {
            background: rgba(255, 255, 255, 0.05);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-mobile-alt"></i> تخصيص العرض في التطبيق</h1>
            <p>اختبر جميع ميزات تخصيص كيفية ظهور الاشتراك المجاني في التطبيق</p>
        </div>

        <div class="info-banner">
            <i class="fas fa-star"></i>
            🎉 ميزة محدثة! الآن مع 6 قوالب جاهزة + إعدادات متقدمة + تفاعل المستخدم! 🚀
        </div>

        <div class="features-grid">
            <!-- مكان الظهور -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="feature-title">أماكن الظهور</div>
                <div class="feature-description">
                    تحكم في أماكن ظهور الاشتراك المجاني داخل التطبيق
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> نافذة منبثقة عند فتح التطبيق</li>
                    <li><i class="fas fa-check"></i> بانر في أعلى الشاشة الرئيسية</li>
                    <li><i class="fas fa-check"></i> عنصر في القائمة الجانبية</li>
                    <li><i class="fas fa-check"></i> قبل تحميل المودات</li>
                    <li><i class="fas fa-check"></i> إشعار دوري</li>
                    <li><i class="fas fa-check"></i> أيقونة عائمة</li>
                </ul>
                <button class="test-button" onclick="testFeature('locations')">اختبار أماكن الظهور</button>
            </div>

            <!-- المظهر البصري -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="feature-title">المظهر البصري</div>
                <div class="feature-description">
                    تخصيص الألوان والأنماط والأيقونات
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 5 أنماط مختلفة (عصري، كلاسيكي، نيون، بسيط، ألعاب)</li>
                    <li><i class="fas fa-check"></i> اختيار الألوان الأساسية والثانوية</li>
                    <li><i class="fas fa-check"></i> 8 أيقونات مختلفة للاختيار</li>
                    <li><i class="fas fa-check"></i> معاينة مباشرة للتغييرات</li>
                </ul>
                <button class="test-button" onclick="testFeature('appearance')">اختبار المظهر البصري</button>
            </div>

            <!-- الحركة والتأثيرات -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-play"></i>
                </div>
                <div class="feature-title">الحركة والتأثيرات</div>
                <div class="feature-description">
                    إضافة حركات وتأثيرات جذابة للعرض
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 8 أنواع حركة مختلفة</li>
                    <li><i class="fas fa-check"></i> 4 مستويات سرعة</li>
                    <li><i class="fas fa-check"></i> تأثيرات الجسيمات</li>
                    <li><i class="fas fa-check"></i> تأثيرات صوتية</li>
                </ul>
                <button class="test-button" onclick="testFeature('animations')">اختبار الحركة والتأثيرات</button>
            </div>

            <!-- التوقيت والتكرار -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="feature-title">التوقيت والتكرار</div>
                <div class="feature-description">
                    تحكم في توقيت وتكرار ظهور الاشتراك
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 5 خيارات تكرار مختلفة</li>
                    <li><i class="fas fa-check"></i> تحكم في تأخير الظهور</li>
                    <li><i class="fas fa-check"></i> تحكم في مدة البقاء</li>
                    <li><i class="fas fa-check"></i> إعدادات مخصصة</li>
                </ul>
                <button class="test-button" onclick="testFeature('timing')">اختبار التوقيت والتكرار</button>
            </div>

            <!-- قوالب العرض الجاهزة -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="feature-title">قوالب العرض الجاهزة</div>
                <div class="feature-description">
                    6 قوالب جاهزة للاستخدام الفوري
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> قالب بسيط ونظيف</li>
                    <li><i class="fas fa-check"></i> قالب مميز ذهبي</li>
                    <li><i class="fas fa-check"></i> قالب ألعاب متحرك</li>
                    <li><i class="fas fa-check"></i> قالب احترافي أنيق</li>
                    <li><i class="fas fa-check"></i> قالب ملون زاهي</li>
                    <li><i class="fas fa-check"></i> قالب داكن عصري</li>
                </ul>
                <button class="test-button" onclick="testFeature('templates')">اختبار القوالب الجاهزة</button>
            </div>

            <!-- الإعدادات المتقدمة -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div class="feature-title">الإعدادات المتقدمة</div>
                <div class="feature-description">
                    تحكم دقيق في جميع جوانب العرض
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> أولوية وحجم وموضع العرض</li>
                    <li><i class="fas fa-check"></i> ضبابية الخلفية والشفافية</li>
                    <li><i class="fas fa-check"></i> زر الإغلاق والسحب</li>
                    <li><i class="fas fa-check"></i> CSS مخصص للتحكم الكامل</li>
                </ul>
                <button class="test-button" onclick="testFeature('advanced')">اختبار الإعدادات المتقدمة</button>
            </div>

            <!-- تفاعل المستخدم -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-hand-pointer"></i>
                </div>
                <div class="feature-title">تفاعل المستخدم</div>
                <div class="feature-description">
                    إعدادات التفاعل والإيماءات
                </div>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> إجراءات النقر والتمرير</li>
                    <li><i class="fas fa-check"></i> تأثيرات الاهتزاز</li>
                    <li><i class="fas fa-check"></i> إيماءات السحب</li>
                    <li><i class="fas fa-check"></i> توجيه لصفحات مخصصة</li>
                </ul>
                <button class="test-button" onclick="testFeature('interaction')">اختبار تفاعل المستخدم</button>
            </div>
        </div>

        <!-- عرض توضيحي -->
        <div class="demo-section">
            <h3><i class="fas fa-eye"></i> عرض توضيحي - أنماط العرض المختلفة</h3>
            <div class="demo-grid">
                <div class="demo-item" onclick="showStyleDemo('modern')">
                    <i class="fas fa-gem"></i>
                    <h5>عصري</h5>
                    <p>تدرجات ملونة وتأثيرات حديثة</p>
                </div>
                <div class="demo-item" onclick="showStyleDemo('classic')">
                    <i class="fas fa-crown"></i>
                    <h5>كلاسيكي</h5>
                    <p>تصميم تقليدي أنيق</p>
                </div>
                <div class="demo-item" onclick="showStyleDemo('neon')">
                    <i class="fas fa-bolt"></i>
                    <h5>نيون</h5>
                    <p>توهج مضيء وألوان زاهية</p>
                </div>
                <div class="demo-item" onclick="showStyleDemo('minimal')">
                    <i class="fas fa-circle"></i>
                    <h5>بسيط</h5>
                    <p>تصميم نظيف ومبسط</p>
                </div>
                <div class="demo-item" onclick="showStyleDemo('gaming')">
                    <i class="fas fa-gamepad"></i>
                    <h5>ألعاب</h5>
                    <p>تأثيرات متحركة للألعاب</p>
                </div>
            </div>
        </div>

        <!-- جدول المقارنة -->
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>الميزة</th>
                    <th>قبل التحديث</th>
                    <th>بعد التحديث</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="feature-col">أماكن الظهور</td>
                    <td>نافذة منبثقة فقط</td>
                    <td>6 أماكن مختلفة</td>
                </tr>
                <tr>
                    <td class="feature-col">الأنماط البصرية</td>
                    <td>نمط واحد ثابت</td>
                    <td>5 أنماط مختلفة</td>
                </tr>
                <tr>
                    <td class="feature-col">الألوان</td>
                    <td>ألوان ثابتة</td>
                    <td>ألوان قابلة للتخصيص</td>
                </tr>
                <tr>
                    <td class="feature-col">الحركات</td>
                    <td>حركة واحدة</td>
                    <td>8 حركات مختلفة</td>
                </tr>
                <tr>
                    <td class="feature-col">التكرار</td>
                    <td>مرة واحدة فقط</td>
                    <td>5 خيارات تكرار</td>
                </tr>
                <tr>
                    <td class="feature-col">المعاينة</td>
                    <td>غير متاحة</td>
                    <td>معاينة مباشرة</td>
                </tr>
                <tr>
                    <td class="feature-col">القوالب الجاهزة</td>
                    <td>غير متاحة</td>
                    <td>6 قوالب جاهزة</td>
                </tr>
                <tr>
                    <td class="feature-col">الإعدادات المتقدمة</td>
                    <td>محدودة</td>
                    <td>تحكم كامل ودقيق</td>
                </tr>
                <tr>
                    <td class="feature-col">تفاعل المستخدم</td>
                    <td>نقر بسيط</td>
                    <td>إيماءات وتأثيرات متقدمة</td>
                </tr>
            </tbody>
        </table>

        <!-- معلومات إضافية -->
        <div class="demo-section">
            <h3><i class="fas fa-info-circle"></i> كيفية الاستخدام</h3>
            <p style="color: #ccc; line-height: 1.6; margin-bottom: 15px;">
                <strong>1.</strong> افتح المنشئ الذكي واختر أي إعداد (سريع، متقدم، أو خبير)<br>
                <strong>2.</strong> انتقل إلى قسم "تخصيص العرض في التطبيق"<br>
                <strong>3.</strong> اختر أماكن الظهور المناسبة<br>
                <strong>4.</strong> خصص المظهر البصري والألوان<br>
                <strong>5.</strong> اختر الحركات والتأثيرات<br>
                <strong>6.</strong> حدد التوقيت والتكرار<br>
                <strong>7.</strong> استخدم المعاينة المباشرة لرؤية النتيجة<br>
                <strong>8.</strong> انشر الحملة مع إعدادات العرض المخصصة
            </p>
            
            <button class="test-button" onclick="openSmartCreator()" style="max-width: 300px; margin: 20px auto 0;">
                <i class="fas fa-rocket"></i> فتح المنشئ الذكي الآن
            </button>
        </div>
    </div>

    <script>
        function testFeature(feature) {
            let message = '';
            
            switch(feature) {
                case 'locations':
                    message = '📍 سيتم فتح المنشئ الذكي. انتقل لقسم "تخصيص العرض" واختبر أماكن الظهور المختلفة.';
                    break;
                case 'appearance':
                    message = '🎨 سيتم فتح المنشئ الذكي. جرب الأنماط والألوان المختلفة في قسم "المظهر البصري".';
                    break;
                case 'animations':
                    message = '🎬 سيتم فتح المنشئ الذكي. اختبر الحركات والتأثيرات في قسم "الحركة والتأثيرات".';
                    break;
                case 'timing':
                    message = '⏰ سيتم فتح المنشئ الذكي. جرب إعدادات التوقيت والتكرار المختلفة.';
                    break;
                case 'templates':
                    message = '📋 سيتم فتح المنشئ الذكي. جرب القوالب الجاهزة الـ6 في قسم "قوالب العرض الجاهزة".';
                    break;
                case 'advanced':
                    message = '⚙️ سيتم فتح المنشئ الذكي. اختبر الإعدادات المتقدمة مثل الأولوية والحجم والموضع.';
                    break;
                case 'interaction':
                    message = '👆 سيتم فتح المنشئ الذكي. جرب إعدادات التفاعل والإيماءات في قسم "إعدادات التفاعل".';
                    break;
            }
            
            alert(message);
            openSmartCreator();
        }

        function showStyleDemo(style) {
            const styles = {
                'modern': 'نمط عصري مع تدرجات ملونة وتأثيرات حديثة',
                'classic': 'نمط كلاسيكي أنيق مع ألوان ثابتة',
                'neon': 'نمط نيون مع توهج مضيء وألوان زاهية',
                'minimal': 'نمط بسيط ونظيف مع تصميم مبسط',
                'gaming': 'نمط ألعاب مع تأثيرات متحركة ومثيرة'
            };
            
            alert(`🎨 ${styles[style]}\n\nافتح المنشئ الذكي لتجربة هذا النمط!`);
        }

        function openSmartCreator() {
            window.open('smart-subscription-creator.html', '_blank');
        }

        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 مرحباً بك في اختبار تخصيص العرض!');
            console.log('✨ ميزة جديدة: تخصيص كامل لعرض الاشتراك المجاني');
            console.log('🚀 جرب جميع الميزات الجديدة');
        }, 1000);
    </script>
</body>
</html>
