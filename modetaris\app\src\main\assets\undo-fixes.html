<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التراجع عن الإصلاحات - Mod Etaris</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            color: #856404;
        }

        .undo-button {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .undo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .safe-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .safe-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .back-link {
            display: inline-block;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 20px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .step {
            background: #e9ecef;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .step h3 {
            color: #e74c3c;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>↩️ التراجع عن الإصلاحات</h1>
            <p>إلغاء جميع التغييرات التي تم تطبيقها</p>
        </div>

        <div class="content">
            <div class="warning-box">
                <strong>⚠️ تنبيه:</strong> هذه الأداة ستقوم بالتراجع عن جميع الإصلاحات التي تم تطبيقها وإعادة النظام لحالته الأصلية.
            </div>

            <div class="controls">
                <button class="undo-button" onclick="undoAllFixes()">↩️ تراجع كامل عن جميع الإصلاحات</button>
                <button class="undo-button" onclick="undoFirebaseFixes()">🔥 تراجع عن إصلاحات Firebase فقط</button>
                <button class="safe-button" onclick="reloadPage()">🔄 إعادة تحميل الصفحة</button>
                <button class="safe-button" onclick="clearCache()">🧹 مسح الذاكرة المؤقتة</button>
                <a href="index.html" class="back-link">← العودة للتطبيق</a>
                <a href="firebase-fix-page.html" class="back-link">🔥 صفحة Firebase</a>
                <a href="remove-backup-system.html" class="back-link" style="background: linear-gradient(135deg, #dc3545, #c82333);">🗑️ إزالة نظام النسخ الاحتياطي</a>
            </div>

            <div class="step">
                <h3>🔍 ما سيتم التراجع عنه</h3>
                <p><strong>1. إصلاحات Firebase:</strong> إزالة firebase.firestore المؤقت</p>
                <p><strong>2. تعديلات قاعدة البيانات:</strong> إعادة تعيين الاتصالات</p>
                <p><strong>3. التغييرات المؤقتة:</strong> مسح جميع التعديلات المحلية</p>
                <p><strong>4. الذاكرة المؤقتة:</strong> مسح localStorage و sessionStorage</p>
            </div>

            <div id="statusContainer"></div>

            <div class="log-container" id="logContainer">
                <div>↩️ أداة التراجع جاهزة...</div>
                <div>⚠️ اختر نوع التراجع المطلوب من الأزرار أعلاه</div>
            </div>
        </div>
    </div>

    <script>
        class UndoManager {
            constructor() {
                this.isRunning = false;
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                const logEntry = document.createElement('div');
                
                let color = '#00ff00';
                if (type === 'error') color = '#ff4444';
                if (type === 'warning') color = '#ffaa00';
                if (type === 'success') color = '#44ff44';
                
                logEntry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> <span style="color: ${color};">${message}</span>`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            showStatus(message, type) {
                const statusContainer = document.getElementById('statusContainer');
                const statusBox = document.createElement('div');
                statusBox.className = `status-box status-${type}`;
                statusBox.textContent = message;
                statusContainer.appendChild(statusBox);
            }

            async undoAllFixes() {
                if (this.isRunning) return;
                this.isRunning = true;

                this.log('↩️ بدء التراجع الكامل عن جميع الإصلاحات...', 'warning');

                try {
                    // Step 1: Remove Firebase fixes
                    await this.undoFirebaseFixesInternal();

                    // Step 2: Reset global variables
                    await this.resetGlobalVariables();

                    // Step 3: Clear storage
                    await this.clearAllStorage();

                    // Step 4: Reset DOM modifications
                    await this.resetDOMModifications();

                    // Step 5: Remove event listeners
                    await this.removeEventListeners();

                    this.showStatus('✅ تم التراجع عن جميع الإصلاحات بنجاح!', 'success');
                    this.log('🎉 التراجع الكامل اكتمل بنجاح!', 'success');
                    this.log('💡 يُنصح بإعادة تحميل الصفحة الآن', 'info');

                } catch (error) {
                    this.log(`❌ فشل التراجع: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في التراجع الكامل', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            async undoFirebaseFixes() {
                this.log('🔥 بدء التراجع عن إصلاحات Firebase...', 'warning');

                try {
                    await this.undoFirebaseFixesInternal();
                    this.showStatus('✅ تم التراجع عن إصلاحات Firebase بنجاح!', 'success');
                    this.log('✅ تم التراجع عن إصلاحات Firebase!', 'success');
                } catch (error) {
                    this.log(`❌ فشل التراجع عن Firebase: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في التراجع عن Firebase', 'error');
                }
            }

            async undoFirebaseFixesInternal() {
                this.log('🔧 إزالة إصلاحات Firebase...', 'info');

                // Remove Firebase mock functions
                if (typeof window.firebase !== 'undefined') {
                    // Check if firestore was mocked
                    if (window.firebase.firestore && window.firebase.firestore.toString().includes('Mock Firestore')) {
                        this.log('🗑️ إزالة firebase.firestore المؤقت...', 'info');
                        delete window.firebase.firestore;
                    }

                    // Remove entire firebase object if it was created by fixes
                    if (window.firebase._createdByFixes) {
                        this.log('🗑️ إزالة Firebase object المؤقت...', 'info');
                        delete window.firebase;
                    }
                }

                // Reset DatabaseBackupSystem
                if (window.DatabaseBackupSystem) {
                    this.log('🔧 إعادة تعيين DatabaseBackupSystem...', 'info');
                    
                    // Remove the modified constructor
                    if (window.DatabaseBackupSystem._originalConstructor) {
                        window.DatabaseBackupSystem = window.DatabaseBackupSystem._originalConstructor;
                    }
                }

                // Reset database backup system instance
                if (window.databaseBackupSystem) {
                    this.log('🔧 إعادة تعيين مثيل databaseBackupSystem...', 'info');
                    
                    if (window.databaseBackupSystem._original) {
                        window.databaseBackupSystem = window.databaseBackupSystem._original;
                    } else {
                        delete window.databaseBackupSystem;
                    }
                }

                this.log('✅ تم التراجع عن إصلاحات Firebase', 'success');
            }

            async resetGlobalVariables() {
                this.log('🔧 إعادة تعيين المتغيرات العامة...', 'info');

                // Reset fix-related global variables
                const fixVariables = [
                    'additionalFixes',
                    'emergencyFix',
                    'finalFixExecutor',
                    'sqlFixesRunner',
                    'criticalFixes',
                    'autoFixRunner',
                    'noDatabaseFix'
                ];

                fixVariables.forEach(varName => {
                    if (window[varName]) {
                        this.log(`🗑️ إزالة ${varName}...`, 'info');
                        delete window[varName];
                    }
                });

                // Reset modified functions
                if (window.originalFetch) {
                    this.log('🔄 إعادة تعيين fetch الأصلي...', 'info');
                    window.fetch = window.originalFetch;
                    delete window.originalFetch;
                }

                if (window.originalConsoleError) {
                    this.log('🔄 إعادة تعيين console.error الأصلي...', 'info');
                    console.error = window.originalConsoleError;
                    delete window.originalConsoleError;
                }

                this.log('✅ تم إعادة تعيين المتغيرات العامة', 'success');
            }

            async clearAllStorage() {
                this.log('🧹 مسح جميع البيانات المحفوظة...', 'info');

                try {
                    // Clear localStorage
                    const localStorageKeys = [
                        'autoFixReports',
                        'finalFixReport',
                        'databaseBackupState',
                        'firebaseFixReport'
                    ];

                    localStorageKeys.forEach(key => {
                        if (localStorage.getItem(key)) {
                            localStorage.removeItem(key);
                            this.log(`🗑️ تم مسح ${key} من localStorage`, 'info');
                        }
                    });

                    // Clear sessionStorage
                    sessionStorage.clear();
                    this.log('🗑️ تم مسح sessionStorage', 'info');

                    this.log('✅ تم مسح جميع البيانات المحفوظة', 'success');

                } catch (error) {
                    this.log(`⚠️ خطأ في مسح البيانات: ${error.message}`, 'warning');
                }
            }

            async resetDOMModifications() {
                this.log('🔧 إعادة تعيين تعديلات DOM...', 'info');

                // Remove any dynamically added elements by fixes
                const elementsToRemove = [
                    '#floating-subscription-icon',
                    '.fix-notification',
                    '.status-notification'
                ];

                elementsToRemove.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        element.remove();
                        this.log(`🗑️ تم إزالة عنصر: ${selector}`, 'info');
                    });
                });

                this.log('✅ تم إعادة تعيين تعديلات DOM', 'success');
            }

            async removeEventListeners() {
                this.log('🔧 إزالة مستمعي الأحداث...', 'info');

                // Remove global error handlers added by fixes
                if (window.globalErrorHandlerInstalled) {
                    this.log('🗑️ إزالة معالج الأخطاء العام...', 'info');
                    window.globalErrorHandlerInstalled = false;
                }

                if (window.enhancedFetchInstalled) {
                    this.log('🗑️ إزالة معالج fetch المحسن...', 'info');
                    window.enhancedFetchInstalled = false;
                }

                this.log('✅ تم إزالة مستمعي الأحداث', 'success');
            }

            reloadPage() {
                this.log('🔄 إعادة تحميل الصفحة...', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }

            clearCache() {
                this.log('🧹 مسح الذاكرة المؤقتة...', 'info');

                try {
                    // Clear all storage
                    localStorage.clear();
                    sessionStorage.clear();

                    // Clear any cached data
                    if ('caches' in window) {
                        caches.keys().then(names => {
                            names.forEach(name => {
                                caches.delete(name);
                            });
                        });
                    }

                    this.showStatus('✅ تم مسح الذاكرة المؤقتة بنجاح!', 'success');
                    this.log('✅ تم مسح الذاكرة المؤقتة', 'success');

                } catch (error) {
                    this.log(`❌ فشل مسح الذاكرة المؤقتة: ${error.message}`, 'error');
                }
            }
        }

        // Create global instance
        const undoManager = new UndoManager();

        // Global functions
        function undoAllFixes() {
            undoManager.undoAllFixes();
        }

        function undoFirebaseFixes() {
            undoManager.undoFirebaseFixes();
        }

        function reloadPage() {
            undoManager.reloadPage();
        }

        function clearCache() {
            undoManager.clearCache();
        }

        // Welcome message
        setTimeout(() => {
            undoManager.log('↩️ أداة التراجع جاهزة للاستخدام', 'success');
            undoManager.log('⚠️ تأكد من اختيار النوع المناسب للتراجع', 'warning');
        }, 500);
    </script>
</body>
</html>
