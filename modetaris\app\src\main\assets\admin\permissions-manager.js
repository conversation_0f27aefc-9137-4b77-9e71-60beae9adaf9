// Permissions and Roles Management System
// نظام إدارة الأذونات والأدوار

// Global variables
let currentUserRole = 'admin';
let userPermissions = {};
let roleDefinitions = {};

// Initialize permissions system
document.addEventListener('DOMContentLoaded', function() {
    console.log('Permissions Manager loaded');
    initializePermissionsSystem();
});

// Initialize permissions system
async function initializePermissionsSystem() {
    try {
        // Load role definitions
        await loadRoleDefinitions();
        
        // Load user permissions
        await loadUserPermissions();
        
        // Apply permissions to UI
        applyPermissionsToUI();
        
        // Setup permission checks
        setupPermissionChecks();
        
        console.log('✅ Permissions system initialized');
    } catch (error) {
        console.error('Error initializing permissions system:', error);
    }
}

// Load role definitions
async function loadRoleDefinitions() {
    try {
        // Define default roles and permissions
        roleDefinitions = {
            'super_admin': {
                name: 'مدير عام',
                permissions: [
                    'users.view', 'users.edit', 'users.delete', 'users.ban',
                    'content.view', 'content.edit', 'content.delete', 'content.approve',
                    'analytics.view', 'analytics.export',
                    'notifications.send', 'notifications.manage',
                    'system.maintenance', 'system.settings', 'system.backup',
                    'permissions.manage', 'roles.manage'
                ],
                color: '#ff0000',
                icon: 'crown'
            },
            'admin': {
                name: 'مدير',
                permissions: [
                    'users.view', 'users.edit', 'users.ban',
                    'content.view', 'content.edit', 'content.approve',
                    'analytics.view',
                    'notifications.send',
                    'system.maintenance'
                ],
                color: '#ffcc00',
                icon: 'user-shield'
            },
            'moderator': {
                name: 'مشرف',
                permissions: [
                    'users.view', 'users.edit',
                    'content.view', 'content.edit',
                    'analytics.view',
                    'notifications.send'
                ],
                color: '#00ff00',
                icon: 'user-check'
            },
            'editor': {
                name: 'محرر',
                permissions: [
                    'content.view', 'content.edit',
                    'analytics.view'
                ],
                color: '#0099ff',
                icon: 'edit'
            },
            'viewer': {
                name: 'مشاهد',
                permissions: [
                    'analytics.view'
                ],
                color: '#888888',
                icon: 'eye'
            }
        };
        
        console.log('Role definitions loaded:', roleDefinitions);
    } catch (error) {
        console.error('Error loading role definitions:', error);
    }
}

// Load user permissions
async function loadUserPermissions() {
    try {
        // Get current user role (would come from authentication)
        const userRole = getCurrentUserRole();
        
        // Load permissions for current role
        if (roleDefinitions[userRole]) {
            userPermissions = {
                role: userRole,
                permissions: roleDefinitions[userRole].permissions,
                roleInfo: roleDefinitions[userRole]
            };
        } else {
            // Default to viewer role
            userPermissions = {
                role: 'viewer',
                permissions: roleDefinitions.viewer.permissions,
                roleInfo: roleDefinitions.viewer
            };
        }
        
        console.log('User permissions loaded:', userPermissions);
    } catch (error) {
        console.error('Error loading user permissions:', error);
    }
}

// Get current user role
function getCurrentUserRole() {
    // This would typically come from authentication system
    // For now, return admin role
    return localStorage.getItem('userRole') || 'admin';
}

// Check if user has permission
function hasPermission(permission) {
    if (!userPermissions.permissions) return false;
    return userPermissions.permissions.includes(permission);
}

// Check if user has any of the permissions
function hasAnyPermission(permissions) {
    return permissions.some(permission => hasPermission(permission));
}

// Check if user has all permissions
function hasAllPermissions(permissions) {
    return permissions.every(permission => hasPermission(permission));
}

// Apply permissions to UI
function applyPermissionsToUI() {
    try {
        // Hide/show tabs based on permissions
        applyTabPermissions();
        
        // Hide/show buttons based on permissions
        applyButtonPermissions();
        
        // Hide/show sections based on permissions
        applySectionPermissions();
        
        // Update user role display
        updateUserRoleDisplay();
        
        console.log('Permissions applied to UI');
    } catch (error) {
        console.error('Error applying permissions to UI:', error);
    }
}

// Apply tab permissions
function applyTabPermissions() {
    const tabPermissions = {
        'users': ['users.view'],
        'analytics': ['analytics.view'],
        'content': ['content.view'],
        'notifications': ['notifications.send'],
        'maintenance': ['system.maintenance'],
        'settings': ['system.settings']
    };
    
    Object.entries(tabPermissions).forEach(([tabName, requiredPermissions]) => {
        const tabButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (tabButton) {
            if (hasAnyPermission(requiredPermissions)) {
                tabButton.style.display = 'flex';
            } else {
                tabButton.style.display = 'none';
            }
        }
    });
}

// Apply button permissions
function applyButtonPermissions() {
    const buttonPermissions = {
        'ban-user': ['users.ban'],
        'delete-user': ['users.delete'],
        'edit-content': ['content.edit'],
        'delete-content': ['content.delete'],
        'approve-content': ['content.approve'],
        'send-notification': ['notifications.send'],
        'manage-notifications': ['notifications.manage'],
        'system-backup': ['system.backup'],
        'manage-permissions': ['permissions.manage']
    };
    
    Object.entries(buttonPermissions).forEach(([buttonClass, requiredPermissions]) => {
        const buttons = document.querySelectorAll(`.${buttonClass}`);
        buttons.forEach(button => {
            if (hasAnyPermission(requiredPermissions)) {
                button.style.display = 'inline-flex';
                button.disabled = false;
            } else {
                button.style.display = 'none';
                button.disabled = true;
            }
        });
    });
}

// Apply section permissions
function applySectionPermissions() {
    const sectionPermissions = {
        'user-management-section': ['users.view'],
        'content-management-section': ['content.view'],
        'analytics-section': ['analytics.view'],
        'system-settings-section': ['system.settings']
    };
    
    Object.entries(sectionPermissions).forEach(([sectionId, requiredPermissions]) => {
        const section = document.getElementById(sectionId);
        if (section) {
            if (hasAnyPermission(requiredPermissions)) {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }
    });
}

// Update user role display
function updateUserRoleDisplay() {
    const roleElement = document.getElementById('current-user-role');
    if (roleElement && userPermissions.roleInfo) {
        roleElement.innerHTML = `
            <i class="fas fa-${userPermissions.roleInfo.icon}"></i>
            <span style="color: ${userPermissions.roleInfo.color}">${userPermissions.roleInfo.name}</span>
        `;
    }
    
    // Update role badge
    const roleBadge = document.getElementById('user-role-badge');
    if (roleBadge && userPermissions.roleInfo) {
        roleBadge.textContent = userPermissions.roleInfo.name;
        roleBadge.style.backgroundColor = userPermissions.roleInfo.color;
    }
}

// Setup permission checks
function setupPermissionChecks() {
    // Add click handlers with permission checks
    document.addEventListener('click', function(event) {
        const target = event.target.closest('[data-permission]');
        if (target) {
            const requiredPermission = target.getAttribute('data-permission');
            if (!hasPermission(requiredPermission)) {
                event.preventDefault();
                event.stopPropagation();
                showPermissionDeniedMessage(requiredPermission);
                return false;
            }
        }
    });
}

// Show permission denied message
function showPermissionDeniedMessage(permission) {
    const message = `ليس لديك صلاحية للقيام بهذا الإجراء.\nالصلاحية المطلوبة: ${permission}`;
    
    // Create permission denied modal
    const modal = createPermissionDeniedModal(message);
    document.body.appendChild(modal);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (modal.parentNode) {
            modal.remove();
        }
    }, 3000);
}

// Create permission denied modal
function createPermissionDeniedModal(message) {
    const modal = document.createElement('div');
    modal.className = 'permission-denied-modal';
    modal.innerHTML = `
        <div class="permission-denied-overlay"></div>
        <div class="permission-denied-content">
            <div class="permission-denied-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3>تم رفض الوصول</h3>
            <p>${message}</p>
            <button class="permission-denied-close" onclick="this.closest('.permission-denied-modal').remove()">
                حسناً
            </button>
        </div>
    `;
    return modal;
}

// Change user role (for testing/demo)
function changeUserRole(newRole) {
    if (roleDefinitions[newRole]) {
        localStorage.setItem('userRole', newRole);
        currentUserRole = newRole;
        
        // Reload permissions
        loadUserPermissions().then(() => {
            applyPermissionsToUI();
            showRoleChangeSuccess(newRole);
        });
    }
}

// Show role change success
function showRoleChangeSuccess(newRole) {
    const roleInfo = roleDefinitions[newRole];
    const message = `تم تغيير الدور إلى: ${roleInfo.name}`;
    
    // Show success notification
    console.log('✅ ' + message);
    
    // Update UI immediately
    setTimeout(() => {
        location.reload(); // Reload to apply all changes
    }, 1000);
}

// Get user role info
function getUserRoleInfo() {
    return userPermissions.roleInfo || {};
}

// Get user permissions list
function getUserPermissions() {
    return userPermissions.permissions || [];
}

// Check if user is admin or higher
function isAdminOrHigher() {
    return hasAnyPermission(['permissions.manage', 'roles.manage']);
}

// Check if user can manage users
function canManageUsers() {
    return hasAnyPermission(['users.edit', 'users.delete', 'users.ban']);
}

// Check if user can manage content
function canManageContent() {
    return hasAnyPermission(['content.edit', 'content.delete', 'content.approve']);
}

// Export permissions data
function exportPermissionsData() {
    const data = {
        currentUser: {
            role: userPermissions.role,
            permissions: userPermissions.permissions,
            roleInfo: userPermissions.roleInfo
        },
        roleDefinitions: roleDefinitions,
        timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'permissions-data.json';
    a.click();
}

// Create role management interface
function createRoleManagementInterface() {
    if (!hasPermission('roles.manage')) {
        showPermissionDeniedMessage('roles.manage');
        return;
    }
    
    const modal = document.createElement('div');
    modal.className = 'role-management-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-users-cog"></i> إدارة الأدوار والصلاحيات</h3>
                <button class="close-btn" onclick="this.closest('.role-management-modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="role-list">
                    ${Object.entries(roleDefinitions).map(([roleKey, roleInfo]) => `
                        <div class="role-item">
                            <div class="role-header">
                                <i class="fas fa-${roleInfo.icon}" style="color: ${roleInfo.color}"></i>
                                <span class="role-name">${roleInfo.name}</span>
                                <span class="role-key">(${roleKey})</span>
                            </div>
                            <div class="role-permissions">
                                ${roleInfo.permissions.map(permission => `
                                    <span class="permission-tag">${permission}</span>
                                `).join('')}
                            </div>
                            <div class="role-actions">
                                <button class="role-action-btn" onclick="changeUserRole('${roleKey}')">
                                    تطبيق هذا الدور
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Make functions globally available
window.hasPermission = hasPermission;
window.hasAnyPermission = hasAnyPermission;
window.hasAllPermissions = hasAllPermissions;
window.changeUserRole = changeUserRole;
window.getUserRoleInfo = getUserRoleInfo;
window.getUserPermissions = getUserPermissions;
window.isAdminOrHigher = isAdminOrHigher;
window.canManageUsers = canManageUsers;
window.canManageContent = canManageContent;
window.exportPermissionsData = exportPermissionsData;
window.createRoleManagementInterface = createRoleManagementInterface;
