<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة حقوق الطبع والنشر المخصصة</title>
    <link rel="stylesheet" href="admin_style.css">
    <style>
        .copyright-info {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ff6b6b;
        }

        .copyright-info h2 {
            color: #ff6b6b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .copyright-text {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .copyright-text h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .copyright-text p {
            color: white;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .search-section {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ffcc00;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #444;
            border-radius: 8px;
            background: #1a1a2e;
            color: white;
            font-size: 1rem;
            margin-bottom: 15px;
        }

        .search-box:focus {
            outline: none;
            border-color: #ffcc00;
        }

        .mods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .mod-item {
            background: #1a1a2e;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #444;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mod-item:hover {
            border-color: #ffcc00;
            transform: translateY(-2px);
        }

        .mod-item.selected {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .mod-item.has-custom-copyright {
            border-color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
        }

        .mod-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .mod-name {
            color: #ffcc00;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .mod-stats {
            color: #888;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .mod-category {
            display: inline-block;
            background: #ffcc00;
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .mod-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .status-normal {
            background: rgba(136, 136, 136, 0.2);
            color: #888;
        }

        .status-custom {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: #000;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ef4444);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .selected-mods {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ff6b6b;
        }

        .selected-mod-item {
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .selected-mod-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
        }

        .selected-mod-info {
            flex: 1;
        }

        .selected-mod-name {
            color: #ffcc00;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .remove-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .remove-btn:hover {
            background: #dc2626;
        }

        .loading {
            text-align: center;
            color: #ffcc00;
            font-size: 1.2rem;
            padding: 40px 20px;
            grid-column: 1 / -1;
            background: rgba(255, 204, 0, 0.1);
            border: 2px dashed #ffcc00;
            border-radius: 10px;
            margin: 20px 0;
        }

        .loading::before {
            content: "⏳ ";
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .error-message {
            background: #ef4444;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success-message {
            background: #22c55e;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #444;
            background: #1a1a2e;
            color: white;
            border-radius: 6px;
            cursor: pointer;
        }

        .pagination button:hover {
            background: #ffcc00;
            color: #000;
        }

        .pagination button.active {
            background: #ffcc00;
            color: #000;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>إدارة حقوق الطبع والنشر المخصصة</h1>
            <p>تحديد المودات التي تتطلب وصف حقوق طبع ونشر خاص للمودات التي تحتوي على إعلانات أو اختصار روابط</p>
        </div>

        <div id="messageContainer"></div>

        <!-- معلومات حقوق الطبع والنشر المخصصة -->
        <div class="copyright-info">
            <h2>
                <i class="fa-solid fa-exclamation-triangle"></i>
                وصف حقوق الطبع والنشر المخصص
            </h2>

            <div class="copyright-text">
                <h3>🇸🇦 النص العربي:</h3>
                <p>هذا النوع من المودات يتطلب من المالك فرض اختصار روابط أو مشاهدة إعلانات، وأرباحها تذهب إلى المالك مباشرة من خلال الإعلانات التي يشاهدها المستخدم. يمكن للمالك طلب إزالة المود من التطبيق عبر التواصل معنا.</p>
            </div>

            <div class="copyright-text">
                <h3>🇺🇸 النص الإنجليزي:</h3>
                <p>This type of mod requires the owner to impose link shortening or ad viewing, and the profits go directly to the owner through the ads viewed by the user. The owner can request removal of the mod from the app by contacting us.</p>
            </div>
        </div>

        <!-- المودات المحددة للحقوق المخصصة -->
        <div class="selected-mods">
            <h2>المودات المحددة للحقوق المخصصة (<span id="selectedCount">0</span>)</h2>
            <div id="selectedModsList">
                <p style="color: #888; text-align: center;">لم يتم تحديد أي مودات بعد</p>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-success" onclick="saveCustomCopyrightMods()">حفظ التغييرات</button>
            </div>
        </div>

        <!-- البحث والتصفية -->
        <div class="search-section">
            <h3>البحث عن المودات</h3>
            <input type="text" id="searchBox" class="search-box" placeholder="ابحث عن المودات بالاسم...">
            <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                <select id="categoryFilter" style="padding: 8px; border-radius: 6px; background: #1a1a2e; color: white; border: 1px solid #444;">
                    <option value="">جميع الفئات</option>
                    <option value="Addons">Addons</option>
                    <option value="Shaders">Shaders</option>
                    <option value="Texture">Texture Pack</option>
                    <option value="Maps">Maps</option>
                    <option value="Seeds">Seeds</option>
                </select>
                <button class="btn btn-primary" onclick="loadMods()">تحديث</button>
            </div>
        </div>

        <!-- قائمة المودات -->
        <div class="mods-grid" id="modsGrid">
            <div class="loading">جاري تحميل المودات... يرجى الانتظار</div>
        </div>

        <!-- ترقيم الصفحات -->
        <div class="pagination" id="pagination"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../supabase-manager.js"></script>
    <script src="custom_copyright.js"></script>
</body>
</html>
