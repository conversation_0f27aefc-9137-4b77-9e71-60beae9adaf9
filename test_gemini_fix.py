#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات Gemini API
"""

import os
import sys
import json

# إضافة مجلد الأداة إلى المسار
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

def test_gemini_imports():
    """اختبار استيراد مكتبات Gemini"""
    print("🔍 اختبار استيراد مكتبات Gemini...")
    
    try:
        import google.generativeai as genai
        print("✅ تم استيراد google.generativeai بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد google.generativeai: {e}")
        print("💡 قم بتثبيت المكتبة: pip install google-generativeai")
        return False

def test_api_keys():
    """اختبار تحميل مفاتيح API"""
    print("\n🔍 اختبار تحميل مفاتيح API...")
    
    try:
        if os.path.exists("api_keys.json"):
            with open("api_keys.json", 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            gemini_keys = config.get("gemini_api_keys", [])
            single_key = config.get("GEMINI_API_KEY", "")
            
            total_keys = len(gemini_keys)
            if single_key and single_key not in gemini_keys:
                total_keys += 1
                
            print(f"📊 عدد مفاتيح Gemini الموجودة: {total_keys}")
            
            if total_keys > 0:
                print("✅ تم العثور على مفاتيح API")
                return True
            else:
                print("❌ لا توجد مفاتيح API")
                return False
        else:
            print("❌ ملف api_keys.json غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تحميل مفاتيح API: {e}")
        return False

def test_gemini_model():
    """اختبار نموذج Gemini"""
    print("\n🔍 اختبار نموذج Gemini...")
    
    try:
        import google.generativeai as genai
        
        # تحميل مفاتيح API
        with open("api_keys.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # جرب أول مفتاح
        api_keys = config.get("gemini_api_keys", [])
        if not api_keys:
            single_key = config.get("GEMINI_API_KEY", "")
            if single_key:
                api_keys = [single_key]
        
        if not api_keys:
            print("❌ لا توجد مفاتيح API للاختبار")
            return False
        
        # اختبار النماذج المختلفة
        models_to_test = [
            "gemini-1.5-flash",
            "gemini-pro",
            "gemini-1.5-pro"
        ]
        
        for model_name in models_to_test:
            print(f"\n🤖 اختبار النموذج: {model_name}")
            
            for i, api_key in enumerate(api_keys[:3]):  # اختبار أول 3 مفاتيح فقط
                try:
                    print(f"🔑 اختبار المفتاح {i+1}...")
                    genai.configure(api_key=api_key)
                    model = genai.GenerativeModel(model_name)
                    
                    # اختبار بسيط
                    response = model.generate_content("Hello")
                    if response and response.text:
                        print(f"✅ النموذج {model_name} يعمل مع المفتاح {i+1}")
                        print(f"📝 استجابة الاختبار: {response.text[:50]}...")
                        return True
                    else:
                        print(f"⚠️ لا توجد استجابة من النموذج {model_name}")
                        
                except Exception as e:
                    error_msg = str(e).lower()
                    if "quota" in error_msg or "limit" in error_msg:
                        print(f"⚠️ المفتاح {i+1} وصل للحد الأقصى")
                    elif "invalid" in error_msg or "api_key" in error_msg:
                        print(f"❌ المفتاح {i+1} غير صالح")
                    elif "not found" in error_msg or "model" in error_msg:
                        print(f"⚠️ النموذج {model_name} غير متاح")
                        break  # جرب النموذج التالي
                    else:
                        print(f"❌ خطأ مع المفتاح {i+1}: {e}")
        
        print("❌ فشل في العثور على نموذج ومفتاح يعملان")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        return False

def test_telegram_description_generation():
    """اختبار إنشاء أوصاف التليجرام"""
    print("\n🔍 اختبار إنشاء أوصاف التليجرام...")
    
    try:
        import google.generativeai as genai
        
        # تحميل مفاتيح API
        with open("api_keys.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_keys = config.get("gemini_api_keys", [])
        if not api_keys:
            single_key = config.get("GEMINI_API_KEY", "")
            if single_key:
                api_keys = [single_key]
        
        if not api_keys:
            print("❌ لا توجد مفاتيح API")
            return False
        
        # تكوين Gemini
        genai.configure(api_key=api_keys[0])
        model = genai.GenerativeModel("gemini-1.5-flash")
        
        # برومت اختبار
        test_prompt = """
اكتب وصفين قصيرين لمود ماين كرافت باللغتين العربية والإنجليزية.

اسم المود: XP Crystals
نوع المود: Addons
الوصف الأصلي: This mod adds XP crystals to your world that you can find and collect for experience.

المطلوب:
- وصف عربي بسيط (150-300 حرف)
- وصف إنجليزي بسيط (150-300 حرف)
- استخدم إيموجيات مناسبة
- ركز على ما يفعله المود

أجب بتنسيق JSON فقط:
{
  "ar": "الوصف العربي هنا",
  "en": "الوصف الإنجليزي هنا"
}
        """
        
        print("📤 إرسال طلب إنشاء أوصاف التليجرام...")
        response = model.generate_content(test_prompt)
        
        if response and response.text:
            print("✅ تم الحصول على استجابة")
            print(f"📝 الاستجابة: {response.text}")
            
            # محاولة تحليل JSON
            try:
                import re
                json_match = re.search(r'\{[\s\S]*?\}', response.text)
                if json_match:
                    result = json.loads(json_match.group(0))
                    ar_desc = result.get('ar', '')
                    en_desc = result.get('en', '')
                    
                    if ar_desc and en_desc:
                        print(f"✅ تم استخراج الأوصاف بنجاح")
                        print(f"📝 العربي ({len(ar_desc)} حرف): {ar_desc}")
                        print(f"📝 الإنجليزي ({len(en_desc)} حرف): {en_desc}")
                        return True
                    else:
                        print("⚠️ الأوصاف فارغة")
                        return False
                else:
                    print("⚠️ لم يتم العثور على JSON في الاستجابة")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
                return False
        else:
            print("❌ لا توجد استجابة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الأوصاف: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إصلاحات Gemini API")
    print("=" * 50)
    
    tests = [
        ("استيراد المكتبات", test_gemini_imports),
        ("تحميل مفاتيح API", test_api_keys),
        ("اختبار نموذج Gemini", test_gemini_model),
        ("إنشاء أوصاف التليجرام", test_telegram_description_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"💥 {test_name}: خطأ غير متوقع - {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! Gemini يعمل بشكل صحيح")
    elif passed > 0:
        print("⚠️ بعض الاختبارات نجحت. قد تحتاج لمراجعة المشاكل المتبقية")
    else:
        print("❌ جميع الاختبارات فشلت. يرجى مراجعة الإعدادات")

if __name__ == "__main__":
    main()
