-- Add columns to banner_ads table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'banner_ads' AND column_name = 'display_limit_per_user') THEN
        ALTER TABLE banner_ads ADD COLUMN display_limit_per_user INTEGER DEFAULT 1;
        RAISE NOTICE 'Column display_limit_per_user added to banner_ads.';
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'banner_ads' AND column_name = 'display_type') THEN
        ALTER TABLE banner_ads ADD COLUMN display_type TEXT DEFAULT 'banner';
        RAISE NOTICE 'Column display_type added to banner_ads.';
    END IF;
END $$;

-- Create entry_subscription_ads table
CREATE TABLE IF NOT EXISTS entry_subscription_ads (
    id SERIAL PRIMARY KEY,
    title_ar TEXT,
    title_en TEXT,
    description_ar TEXT,
    description_en TEXT,
    image_url TEXT,
    campaign_id UUID REFERENCES free_subscription_campaigns(id),
    is_active BOOLEAN DEFAULT TRUE,
    display_frequency TEXT DEFAULT 'once_per_session', -- 'once_per_session', 'always', 'once_per_user'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add RLS policy for entry_subscription_ads
ALTER TABLE entry_subscription_ads ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Enable read access for all users" ON entry_subscription_ads;
CREATE POLICY "Enable read access for all users" ON entry_subscription_ads
FOR SELECT USING (TRUE);

-- Optional: Add a default entry subscription ad for testing
INSERT INTO entry_subscription_ads (title_ar, title_en, description_ar, description_en, image_url, campaign_id, is_active, display_frequency)
VALUES (
    'احصل على اشتراك مجاني!',
    'Get a Free Subscription!',
    'أكمل بعض المهام البسيطة واحصل على وصول مجاني لجميع الميزات المميزة في تطبيقنا.',
    'Complete a few simple tasks and get free access to all premium features in our app.',
    'https://example.com/free_sub_ad_image.png', -- Replace with a real image URL
    NULL, -- Replace with an actual campaign_id from free_subscription_campaigns if you have one, otherwise leave NULL for now
    TRUE,
    'once_per_session'
)
ON CONFLICT (id) DO NOTHING; -- Prevents inserting if ID already exists (for SERIAL, this is usually not needed unless you manually set ID)
