/* ========================================
   إدارة البانرات الموحدة - مع دعم ربط المودات
   Unified Banner Manager - With Mod Linking Support
   ======================================== */

// Global Variables
let supabaseClient;
let currentBannerType = '';
let allMods = [];
let allCampaigns = [];
let allBanners = [];
let isEditing = false;
let currentEditId = null;

// Initialize Banner Manager
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 تهيئة إدارة البانرات الموحدة...');
    
    try {
        // Initialize Supabase
        await initializeSupabase();
        
        // Setup event listeners
        setupEventListeners();
        
        // Load initial data
        await loadInitialData();
        
        console.log('✅ تم تهيئة إدارة البانرات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة إدارة البانرات:', error);
        showNotification('فشل في تهيئة إدارة البانرات', 'error');
    }
});

// Initialize Supabase Connection
async function initializeSupabase() {
    try {
        if (typeof SUPABASE_URL === 'undefined' || typeof SUPABASE_ANON_KEY === 'undefined') {
            throw new Error('إعدادات Supabase غير موجودة');
        }
        
        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('✅ تم الاتصال بـ Supabase بنجاح');
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في الاتصال بـ Supabase:', error);
        throw error;
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Form submission
    document.getElementById('banner-form').addEventListener('submit', handleFormSubmit);
    
    // Image upload
    document.getElementById('banner-image-file').addEventListener('change', handleImageUpload);
    
    // Mod selection
    document.getElementById('target-mod').addEventListener('change', handleModSelection);
    
    // Campaign selection
    document.getElementById('target-campaign').addEventListener('change', handleCampaignSelection);
    
    console.log('✅ تم إعداد مستمعي الأحداث');
}

// Load Initial Data
async function loadInitialData() {
    showLoading('جاري تحميل البيانات الأولية...');
    
    try {
        // Load mods for mod banners
        await loadMods();
        
        // Load campaigns for subscription banners
        await loadCampaigns();
        
        // Load existing banners
        await loadBanners();
        
        hideLoading();
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات الأولية:', error);
        hideLoading();
        showNotification('فشل في تحميل البيانات الأولية', 'error');
    }
}

// Load Mods
async function loadMods() {
    try {
        const { data: mods, error } = await supabaseClient
            .from('mods')
            .select('id, title, description, description_ar, image_url, category')
            .order('title', { ascending: true });
        
        if (error) throw error;
        
        allMods = mods || [];
        populateModSelect();
        
        console.log(`✅ تم تحميل ${allMods.length} مود`);
    } catch (error) {
        console.error('❌ خطأ في تحميل المودات:', error);
        allMods = [];
    }
}

// Populate Mod Select
function populateModSelect() {
    const select = document.getElementById('target-mod');
    select.innerHTML = '<option value="">اختر مود...</option>';
    
    allMods.forEach(mod => {
        const option = document.createElement('option');
        option.value = mod.id;
        option.textContent = mod.title;
        option.dataset.mod = JSON.stringify(mod);
        select.appendChild(option);
    });
}

// Load Campaigns
async function loadCampaigns() {
    try {
        const { data: campaigns, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false });
        
        if (error) throw error;
        
        allCampaigns = campaigns || [];
        populateCampaignSelect();
        
        console.log(`✅ تم تحميل ${allCampaigns.length} حملة`);
    } catch (error) {
        console.error('❌ خطأ في تحميل الحملات:', error);
        allCampaigns = [];
    }
}

// Populate Campaign Select
function populateCampaignSelect() {
    const select = document.getElementById('target-campaign');
    select.innerHTML = '<option value="">اختر حملة...</option>';
    
    allCampaigns.forEach(campaign => {
        const option = document.createElement('option');
        option.value = campaign.id;
        option.textContent = campaign.title_ar || campaign.title_en;
        option.dataset.campaign = JSON.stringify(campaign);
        select.appendChild(option);
    });
}

// Load Banners
async function loadBanners() {
    try {
        const { data: banners, error } = await supabaseClient
            .from('banner_ads')
            .select('*')
            .order('display_order', { ascending: true });
        
        if (error) throw error;
        
        allBanners = banners || [];
        displayBanners();
        
        console.log(`✅ تم تحميل ${allBanners.length} بانر`);
    } catch (error) {
        console.error('❌ خطأ في تحميل البانرات:', error);
        allBanners = [];
        document.getElementById('banners-list').innerHTML = '<div class="error">فشل في تحميل البانرات</div>';
    }
}

// Display Banners
function displayBanners() {
    const container = document.getElementById('banners-list');
    
    if (allBanners.length === 0) {
        container.innerHTML = '<div class="empty-state">لا توجد بانرات حالياً</div>';
        return;
    }
    
    let html = '';
    allBanners.forEach(banner => {
        html += createBannerCard(banner);
    });
    
    container.innerHTML = html;
}

// Create Banner Card HTML
function createBannerCard(banner) {
    const statusClass = banner.is_active ? 'success' : 'error';
    const statusText = banner.is_active ? 'نشط' : 'غير نشط';
    const typeText = getBannerTypeText(banner.banner_type || 'regular');
    
    return `
        <div class="banner-card" data-id="${banner.id}">
            <div class="banner-image">
                <img src="${banner.image_url}" alt="${banner.title}" loading="lazy">
            </div>
            <div class="banner-info">
                <h4>${banner.title || 'بدون عنوان'}</h4>
                <p>${banner.description || 'بدون وصف'}</p>
                <div class="banner-meta">
                    <span class="banner-type">${typeText}</span>
                    <span class="banner-status ${statusClass}">${statusText}</span>
                    <span class="banner-order">ترتيب: ${banner.display_order}</span>
                </div>
                ${getBannerTargetInfo(banner)}
            </div>
            <div class="banner-actions">
                <button onclick="editBanner('${banner.id}')" class="action-btn edit" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="duplicateBanner('${banner.id}')" class="action-btn duplicate" title="نسخ">
                    <i class="fas fa-copy"></i>
                </button>
                <button onclick="deleteBanner('${banner.id}')" class="action-btn delete" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
                <button onclick="toggleBannerStatus('${banner.id}')" class="action-btn toggle" title="تفعيل/إلغاء">
                    <i class="fas fa-power-off"></i>
                </button>
            </div>
        </div>
    `;
}

// Get Banner Type Text
function getBannerTypeText(type) {
    const types = {
        'regular': 'عادي',
        'mod': 'مود',
        'subscription': 'اشتراك'
    };
    return types[type] || 'غير محدد';
}

// Get Banner Target Info
function getBannerTargetInfo(banner) {
    let info = '';
    
    if (banner.banner_type === 'mod' && banner.target_mod_id) {
        const mod = allMods.find(m => m.id === banner.target_mod_id);
        if (mod) {
            info = `<div class="target-info"><i class="fas fa-gamepad"></i> المود: ${mod.title}</div>`;
        }
    } else if (banner.banner_type === 'subscription' && banner.campaign_id) {
        const campaign = allCampaigns.find(c => c.id === banner.campaign_id);
        if (campaign) {
            info = `<div class="target-info"><i class="fas fa-crown"></i> الحملة: ${campaign.title_ar || campaign.title_en}</div>`;
        }
    } else if (banner.banner_type === 'regular' && banner.click_url) {
        info = `<div class="target-info"><i class="fas fa-link"></i> الرابط: ${banner.click_url}</div>`;
    }
    
    return info;
}

// Select Banner Type
function selectBannerType(type) {
    currentBannerType = type;
    
    // Update UI
    document.querySelectorAll('.banner-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.target.closest('.banner-type-card').classList.add('selected');
    
    // Show form
    document.getElementById('banner-form-container').style.display = 'block';
    
    // Update form title
    const titles = {
        'regular': 'إنشاء بانر عادي',
        'mod': 'إنشاء بانر مود',
        'subscription': 'إنشاء بانر اشتراك'
    };
    document.getElementById('form-title').innerHTML = `<i class="fas fa-plus"></i> ${titles[type]}`;
    
    // Show/hide relevant fields
    showRelevantFields(type);
    
    // Set banner type
    document.getElementById('banner-type').value = type;
    
    console.log(`📋 تم اختيار نوع البانر: ${type}`);
}

// Show Relevant Fields
function showRelevantFields(type) {
    // Hide all type-specific fields
    document.getElementById('regular-fields').style.display = 'none';
    document.getElementById('mod-fields').style.display = 'none';
    document.getElementById('subscription-fields').style.display = 'none';
    
    // Show relevant fields
    document.getElementById(`${type}-fields`).style.display = 'block';
}

// Handle Image Upload
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // Validate file
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صالح', 'error');
        return;
    }
    
    if (file.size > 2 * 1024 * 1024) { // 2MB
        showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
        return;
    }
    
    // Preview image
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = document.getElementById('preview-image');
        img.src = e.target.result;
        
        // Show preview
        document.getElementById('image-preview').style.display = 'block';
        
        // Get image dimensions
        img.onload = function() {
            document.getElementById('image-dimensions').textContent = `${this.naturalWidth}x${this.naturalHeight}`;
            document.getElementById('image-size').textContent = formatFileSize(file.size);
        };
    };
    reader.readAsDataURL(file);
}

// Handle Mod Selection
function handleModSelection(event) {
    const selectedOption = event.target.selectedOptions[0];
    if (!selectedOption || !selectedOption.dataset.mod) {
        document.getElementById('mod-preview').style.display = 'none';
        return;
    }
    
    const mod = JSON.parse(selectedOption.dataset.mod);
    
    // Update preview
    document.getElementById('mod-preview-image').src = mod.image_url;
    document.getElementById('mod-preview-title').textContent = mod.title;
    document.getElementById('mod-preview-description').textContent = mod.description || mod.description_ar || 'بدون وصف';
    document.getElementById('mod-preview-category').textContent = mod.category || 'غير محدد';
    
    document.getElementById('mod-preview').style.display = 'block';
}

// Handle Campaign Selection
function handleCampaignSelection(event) {
    const selectedOption = event.target.selectedOptions[0];
    if (!selectedOption || !selectedOption.dataset.campaign) {
        document.getElementById('campaign-preview').style.display = 'none';
        return;
    }
    
    const campaign = JSON.parse(selectedOption.dataset.campaign);
    
    // Update preview
    document.getElementById('campaign-preview-title').textContent = campaign.title_ar || campaign.title_en;
    document.getElementById('campaign-preview-description').textContent = campaign.description_ar || campaign.description_en || 'بدون وصف';
    document.getElementById('campaign-preview-duration').textContent = `${campaign.subscription_duration_days} يوم`;
    
    document.getElementById('campaign-preview').style.display = 'block';
}

// Handle Form Submit
async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!validateForm()) return;
    
    showLoading('جاري حفظ البانر...');
    
    try {
        // Upload image first
        const imageFile = document.getElementById('banner-image-file').files[0];
        let imageUrl = '';
        
        if (imageFile) {
            imageUrl = await uploadImage(imageFile);
        }
        
        // Prepare banner data
        const bannerData = {
            title: document.getElementById('banner-title').value.trim(),
            description: document.getElementById('banner-description').value.trim(),
            image_url: imageUrl,
            display_order: parseInt(document.getElementById('banner-display-order').value) || 1,
            is_active: document.getElementById('banner-is-active').checked,
            banner_type: currentBannerType,
            created_at: new Date().toISOString()
        };
        
        // Add type-specific data
        if (currentBannerType === 'regular') {
            bannerData.click_url = document.getElementById('target-url').value.trim();
        } else if (currentBannerType === 'mod') {
            bannerData.target_mod_id = document.getElementById('target-mod').value;
        } else if (currentBannerType === 'subscription') {
            bannerData.campaign_id = document.getElementById('target-campaign').value;
        }
        
        // Save to database
        let result;
        if (isEditing && currentEditId) {
            // Update existing banner
            result = await supabaseClient
                .from('banner_ads')
                .update(bannerData)
                .eq('id', currentEditId);
        } else {
            // Create new banner
            result = await supabaseClient
                .from('banner_ads')
                .insert([bannerData]);
        }
        
        if (result.error) throw result.error;
        
        hideLoading();
        showNotification(isEditing ? 'تم تحديث البانر بنجاح' : 'تم إنشاء البانر بنجاح', 'success');
        
        // Reset form and reload banners
        resetForm();
        await loadBanners();
        
    } catch (error) {
        console.error('❌ خطأ في حفظ البانر:', error);
        hideLoading();
        showNotification('فشل في حفظ البانر: ' + error.message, 'error');
    }
}

// Validate Form
function validateForm() {
    const title = document.getElementById('banner-title').value.trim();
    const imageFile = document.getElementById('banner-image-file').files[0];
    
    if (!title) {
        showNotification('يرجى إدخال عنوان البانر', 'error');
        return false;
    }
    
    if (!imageFile && !isEditing) {
        showNotification('يرجى اختيار صورة للبانر', 'error');
        return false;
    }
    
    // Type-specific validation
    if (currentBannerType === 'regular') {
        const url = document.getElementById('target-url').value.trim();
        if (!url) {
            showNotification('يرجى إدخال رابط الوجهة', 'error');
            return false;
        }
    } else if (currentBannerType === 'mod') {
        const modId = document.getElementById('target-mod').value;
        if (!modId) {
            showNotification('يرجى اختيار مود', 'error');
            return false;
        }
    } else if (currentBannerType === 'subscription') {
        const campaignId = document.getElementById('target-campaign').value;
        if (!campaignId) {
            showNotification('يرجى اختيار حملة اشتراك', 'error');
            return false;
        }
    }
    
    return true;
}

// Upload Image
async function uploadImage(file) {
    try {
        const fileName = `banner_${Date.now()}_${file.name}`;
        const { data, error } = await supabaseClient.storage
            .from('banner-images')
            .upload(fileName, file);
        
        if (error) throw error;
        
        // Get public URL
        const { data: urlData } = supabaseClient.storage
            .from('banner-images')
            .getPublicUrl(fileName);
        
        return urlData.publicUrl;
    } catch (error) {
        console.error('❌ خطأ في رفع الصورة:', error);
        throw new Error('فشل في رفع الصورة');
    }
}

// Preview Banner
function previewBanner() {
    if (!validateForm()) return;
    
    const title = document.getElementById('banner-title').value.trim();
    const description = document.getElementById('banner-description').value.trim();
    const imageFile = document.getElementById('banner-image-file').files[0];
    
    let previewHtml = `
        <div class="banner-preview">
            <div class="preview-banner">
                ${imageFile ? `<img src="${URL.createObjectURL(imageFile)}" alt="${title}">` : '<div class="no-image">لا توجد صورة</div>'}
                <div class="banner-overlay">
                    <h3>${title}</h3>
                    ${description ? `<p>${description}</p>` : ''}
                </div>
            </div>
            <div class="preview-info">
                <h4>معلومات البانر:</h4>
                <ul>
                    <li><strong>النوع:</strong> ${getBannerTypeText(currentBannerType)}</li>
                    <li><strong>الحالة:</strong> ${document.getElementById('banner-is-active').checked ? 'نشط' : 'غير نشط'}</li>
                    <li><strong>الترتيب:</strong> ${document.getElementById('banner-display-order').value}</li>
                </ul>
            </div>
        </div>
    `;
    
    document.getElementById('banner-preview-content').innerHTML = previewHtml;
    document.getElementById('preview-modal').style.display = 'flex';
}

// Close Preview
function closePreview() {
    document.getElementById('preview-modal').style.display = 'none';
}

// Reset Form
function resetForm() {
    document.getElementById('banner-form').reset();
    document.getElementById('banner-form-container').style.display = 'none';
    document.getElementById('image-preview').style.display = 'none';
    document.getElementById('mod-preview').style.display = 'none';
    document.getElementById('campaign-preview').style.display = 'none';
    
    // Reset editing state
    isEditing = false;
    currentEditId = null;
    currentBannerType = '';
    
    // Remove selection from banner type cards
    document.querySelectorAll('.banner-type-card').forEach(card => {
        card.classList.remove('selected');
    });
}

// Cancel Form
function cancelForm() {
    if (confirm('هل أنت متأكد من إلغاء التعديلات؟')) {
        resetForm();
    }
}

// Edit Banner
async function editBanner(id) {
    const banner = allBanners.find(b => b.id === id);
    if (!banner) return;
    
    isEditing = true;
    currentEditId = id;
    currentBannerType = banner.banner_type || 'regular';
    
    // Select banner type
    selectBannerType(currentBannerType);
    
    // Fill form
    document.getElementById('banner-title').value = banner.title || '';
    document.getElementById('banner-description').value = banner.description || '';
    document.getElementById('banner-display-order').value = banner.display_order || 1;
    document.getElementById('banner-is-active').checked = banner.is_active;
    
    // Fill type-specific fields
    if (currentBannerType === 'regular') {
        document.getElementById('target-url').value = banner.click_url || '';
    } else if (currentBannerType === 'mod') {
        document.getElementById('target-mod').value = banner.target_mod_id || '';
        if (banner.target_mod_id) {
            handleModSelection({ target: document.getElementById('target-mod') });
        }
    } else if (currentBannerType === 'subscription') {
        document.getElementById('target-campaign').value = banner.campaign_id || '';
        if (banner.campaign_id) {
            handleCampaignSelection({ target: document.getElementById('target-campaign') });
        }
    }
    
    // Update form title
    document.getElementById('form-title').innerHTML = `<i class="fas fa-edit"></i> تعديل البانر`;
    
    // Scroll to form
    document.getElementById('banner-form-container').scrollIntoView({ behavior: 'smooth' });
}

// Duplicate Banner
async function duplicateBanner(id) {
    const banner = allBanners.find(b => b.id === id);
    if (!banner) return;
    
    if (!confirm('هل تريد نسخ هذا البانر؟')) return;
    
    showLoading('جاري نسخ البانر...');
    
    try {
        const newBanner = { ...banner };
        delete newBanner.id;
        newBanner.title = `نسخة من ${newBanner.title}`;
        newBanner.display_order = (newBanner.display_order || 0) + 1;
        newBanner.created_at = new Date().toISOString();
        
        const { error } = await supabaseClient
            .from('banner_ads')
            .insert([newBanner]);
        
        if (error) throw error;
        
        hideLoading();
        showNotification('تم نسخ البانر بنجاح', 'success');
        await loadBanners();
        
    } catch (error) {
        console.error('❌ خطأ في نسخ البانر:', error);
        hideLoading();
        showNotification('فشل في نسخ البانر', 'error');
    }
}

// Delete Banner
async function deleteBanner(id) {
    if (!confirm('هل أنت متأكد من حذف هذا البانر؟')) return;
    
    showLoading('جاري حذف البانر...');
    
    try {
        const { error } = await supabaseClient
            .from('banner_ads')
            .delete()
            .eq('id', id);
        
        if (error) throw error;
        
        hideLoading();
        showNotification('تم حذف البانر بنجاح', 'success');
        await loadBanners();
        
    } catch (error) {
        console.error('❌ خطأ في حذف البانر:', error);
        hideLoading();
        showNotification('فشل في حذف البانر', 'error');
    }
}

// Toggle Banner Status
async function toggleBannerStatus(id) {
    const banner = allBanners.find(b => b.id === id);
    if (!banner) return;
    
    showLoading('جاري تحديث حالة البانر...');
    
    try {
        const { error } = await supabaseClient
            .from('banner_ads')
            .update({ is_active: !banner.is_active })
            .eq('id', id);
        
        if (error) throw error;
        
        hideLoading();
        showNotification(`تم ${banner.is_active ? 'إلغاء تفعيل' : 'تفعيل'} البانر`, 'success');
        await loadBanners();
        
    } catch (error) {
        console.error('❌ خطأ في تحديث حالة البانر:', error);
        hideLoading();
        showNotification('فشل في تحديث حالة البانر', 'error');
    }
}

// Filter Banners
function filterBanners() {
    const typeFilter = document.getElementById('filter-type').value;
    const statusFilter = document.getElementById('filter-status').value;
    
    let filteredBanners = allBanners;
    
    if (typeFilter) {
        filteredBanners = filteredBanners.filter(banner => 
            (banner.banner_type || 'regular') === typeFilter
        );
    }
    
    if (statusFilter) {
        const isActive = statusFilter === 'active';
        filteredBanners = filteredBanners.filter(banner => banner.is_active === isActive);
    }
    
    // Display filtered banners
    const container = document.getElementById('banners-list');
    
    if (filteredBanners.length === 0) {
        container.innerHTML = '<div class="empty-state">لا توجد بانرات تطابق المرشحات المحددة</div>';
        return;
    }
    
    let html = '';
    filteredBanners.forEach(banner => {
        html += createBannerCard(banner);
    });
    
    container.innerHTML = html;
}

// Utility Functions
function showLoading(message = 'جاري التحميل...') {
    const overlay = document.getElementById('loading-overlay');
    const text = document.getElementById('loading-text');
    text.textContent = message;
    overlay.style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loading-overlay').style.display = 'none';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
