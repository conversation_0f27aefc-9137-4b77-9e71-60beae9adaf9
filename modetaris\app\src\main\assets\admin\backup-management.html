<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطية - نظام احترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
        }
        
        .status-healthy { border-left-color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-danger { border-left-color: #dc3545; }
        .status-info { border-left-color: #17a2b8; }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .backup-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
        }
        
        .progress-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .database-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        
        .db-healthy { background: #d4edda; color: #155724; }
        .db-warning { background: #fff3cd; color: #856404; }
        .db-danger { background: #f8d7da; color: #721c24; }
        .db-unknown { background: #e2e3e5; color: #383d41; }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            margin: 10px 0;
        }
        
        .spinner-custom {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .tab-content {
            padding: 20px 0;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            margin-left: 5px;
            border: none;
            background: #f8f9fa;
            color: #495057;
        }
        
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-database"></i> نظام إدارة النسخ الاحتياطية الاحترافي</h1>
                <p class="mb-0">مراقبة وإدارة قواعد البيانات مع النسخ الاحتياطي التلقائي والتبديل الذكي</p>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                        <i class="fas fa-save"></i> النسخ الاحتياطية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="transfer-tab" data-bs-toggle="tab" data-bs-target="#transfer" type="button" role="tab">
                        <i class="fas fa-exchange-alt"></i> نقل البيانات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button" role="tab">
                        <i class="fas fa-heartbeat"></i> المراقبة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="fas fa-cog"></i> الإعدادات
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Dashboard Tab -->
                <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                    <!-- System Status -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="totalBackups">-</div>
                                <div class="metric-label">إجمالي النسخ الاحتياطية</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="lastBackupTime">-</div>
                                <div class="metric-label">آخر نسخة احتياطية</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="totalSize">-</div>
                                <div class="metric-label">الحجم الإجمالي (MB)</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="systemStatus">-</div>
                                <div class="metric-label">حالة النظام</div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Status -->
                    <div class="status-card status-info">
                        <h5><i class="fas fa-server"></i> حالة قواعد البيانات</h5>
                        <div id="databaseStatusContainer">
                            <div class="text-center">
                                <div class="spinner-custom"></div>
                                <p>جاري تحميل حالة قواعد البيانات...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="status-card status-healthy">
                        <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
                        <button class="action-btn" onclick="createQuickBackup()">
                            <i class="fas fa-save"></i> نسخة احتياطية سريعة
                        </button>
                        <button class="action-btn" onclick="checkAllDatabases()">
                            <i class="fas fa-check-circle"></i> فحص جميع قواعد البيانات
                        </button>
                        <button class="action-btn" onclick="startMonitoring()">
                            <i class="fas fa-play"></i> بدء المراقبة التلقائية
                        </button>
                        <button class="action-btn" onclick="downloadLogs()">
                            <i class="fas fa-download"></i> تحميل السجلات
                        </button>
                    </div>

                    <!-- Recent Activity -->
                    <div class="status-card status-warning">
                        <h5><i class="fas fa-history"></i> النشاط الأخير</h5>
                        <div id="recentActivity">
                            <p class="text-muted">لا توجد أنشطة حديثة</p>
                        </div>
                    </div>
                </div>

                <!-- Backup Tab -->
                <div class="tab-pane fade" id="backup" role="tabpanel">
                    <!-- Create Backup -->
                    <div class="status-card status-info">
                        <h5><i class="fas fa-plus-circle"></i> إنشاء نسخة احتياطية جديدة</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">قاعدة البيانات المصدر:</label>
                                <select class="form-select" id="sourceDatabase">
                                    <option value="main">الرئيسية (main)</option>
                                    <option value="backup1">الاحتياطية 1</option>
                                    <option value="backup2">الاحتياطية 2</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">خيارات النسخ:</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeImages" checked>
                                    <label class="form-check-label" for="includeImages">تضمين الصور</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="compressBackup" checked>
                                    <label class="form-check-label" for="compressBackup">ضغط النسخة الاحتياطية</label>
                                </div>
                            </div>
                        </div>
                        <button class="action-btn mt-3" onclick="createBackup()">
                            <i class="fas fa-save"></i> إنشاء نسخة احتياطية
                        </button>
                    </div>

                    <!-- Backup Progress -->
                    <div class="progress-container" id="backupProgress" style="display: none;">
                        <h6><i class="fas fa-spinner fa-spin"></i> جاري إنشاء النسخة الاحتياطية...</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="backupProgressBar"></div>
                        </div>
                        <div id="backupStatus">بدء العملية...</div>
                    </div>

                    <!-- Backup List -->
                    <div class="status-card status-healthy">
                        <h5><i class="fas fa-list"></i> قائمة النسخ الاحتياطية</h5>
                        <div id="backupList">
                            <div class="text-center">
                                <div class="spinner-custom"></div>
                                <p>جاري تحميل قائمة النسخ الاحتياطية...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transfer Tab -->
                <div class="tab-pane fade" id="transfer" role="tabpanel">
                    <!-- Transfer Form -->
                    <div class="status-card status-warning">
                        <h5><i class="fas fa-exchange-alt"></i> نقل البيانات بين قواعد البيانات</h5>
                        <div class="alert alert-warning alert-custom">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تحذير:</strong> عملية نقل البيانات ستحل محل البيانات الموجودة في قاعدة البيانات المستهدفة.
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">من قاعدة البيانات:</label>
                                <select class="form-select" id="transferSource">
                                    <option value="main">الرئيسية (main)</option>
                                    <option value="backup1">الاحتياطية 1</option>
                                    <option value="backup2">الاحتياطية 2</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">إلى قاعدة البيانات:</label>
                                <select class="form-select" id="transferTarget">
                                    <option value="backup1">الاحتياطية 1</option>
                                    <option value="backup2">الاحتياطية 2</option>
                                    <option value="main">الرئيسية (main)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">أو استخدام نسخة احتياطية محددة:</label>
                            <select class="form-select" id="backupToRestore">
                                <option value="">اختر نسخة احتياطية (اختياري)</option>
                            </select>
                        </div>

                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="activateNewDb">
                                <label class="form-check-label" for="activateNewDb">
                                    <strong>تفعيل قاعدة البيانات الجديدة كقاعدة رئيسية للتطبيق</strong>
                                    <br><small class="text-muted">سيتم تحديث جميع ملفات التطبيق لاستخدام قاعدة البيانات الجديدة</small>
                                </label>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button class="action-btn" onclick="transferData()">
                                <i class="fas fa-exchange-alt"></i> بدء النقل
                            </button>
                            <button class="action-btn" onclick="backupManager.switchDatabase()">
                                <i class="fas fa-sync-alt"></i> تبديل قاعدة البيانات
                            </button>
                            <button class="action-btn" onclick="backupManager.compareDatabases()">
                                <i class="fas fa-balance-scale"></i> مقارنة قواعد البيانات
                            </button>
                        </div>
                    </div>

                    <!-- Transfer Progress -->
                    <div class="progress-container" id="transferProgress" style="display: none;">
                        <h6><i class="fas fa-spinner fa-spin"></i> جاري نقل البيانات...</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" 
                                 role="progressbar" style="width: 0%" id="transferProgressBar"></div>
                        </div>
                        <div id="transferStatus">بدء عملية النقل...</div>
                    </div>
                </div>

                <!-- Monitor Tab -->
                <div class="tab-pane fade" id="monitor" role="tabpanel">
                    <!-- Monitoring Controls -->
                    <div class="status-card status-info">
                        <h5><i class="fas fa-heartbeat"></i> مراقبة قواعد البيانات</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <button class="action-btn" id="monitorToggle" onclick="toggleMonitoring()">
                                    <i class="fas fa-play"></i> بدء المراقبة
                                </button>
                                <button class="action-btn" onclick="refreshMonitoringData()">
                                    <i class="fas fa-sync"></i> تحديث البيانات
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoFailover" checked>
                                    <label class="form-check-label" for="autoFailover">التبديل التلقائي عند الأعطال</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monitoring Status -->
                    <div class="status-card status-healthy">
                        <h5><i class="fas fa-chart-line"></i> حالة المراقبة</h5>
                        <div id="monitoringStatus">
                            <p class="text-muted">المراقبة غير نشطة</p>
                        </div>
                    </div>

                    <!-- Live Logs -->
                    <div class="status-card status-danger">
                        <h5><i class="fas fa-terminal"></i> سجلات النظام المباشرة</h5>
                        <div class="log-container" id="liveLogs">
                            <div>جاري تحميل السجلات...</div>
                        </div>
                        <button class="action-btn mt-2" onclick="clearLogs()">
                            <i class="fas fa-trash"></i> مسح السجلات
                        </button>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <!-- Configuration -->
                    <div class="config-section">
                        <h5><i class="fas fa-cog"></i> إعدادات النظام</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">فترة النسخ الاحتياطي التلقائي (ساعات):</label>
                                <input type="number" class="form-control" id="autoBackupInterval" value="6" min="1" max="168">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الحد الأقصى لعدد النسخ الاحتياطية:</label>
                                <input type="number" class="form-control" id="maxBackupFiles" value="50" min="5" max="200">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">فترة فحص الحالة (ثواني):</label>
                                <input type="number" class="form-control" id="healthCheckInterval" value="300" min="60" max="3600">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">عدد الفحوصات الفاشلة قبل التبديل:</label>
                                <input type="number" class="form-control" id="maxFailedChecks" value="3" min="1" max="10">
                            </div>
                        </div>
                        
                        <button class="action-btn mt-3" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>

                    <!-- Database Configuration -->
                    <div class="config-section">
                        <h5><i class="fas fa-database"></i> إعدادات قواعد البيانات</h5>
                        <div id="databaseConfig">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                        <button class="action-btn" onclick="addNewDatabase()">
                            <i class="fas fa-plus"></i> إضافة قاعدة بيانات جديدة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="backup-management.js"></script>
</body>
</html>
