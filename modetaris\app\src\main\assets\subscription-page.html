<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاشتراك المجاني - <PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 215, 0, 0.4);
            box-shadow: 0 0 40px rgba(255, 215, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 3rem;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            position: relative;
            z-index: 2;
        }

        .header p {
            font-size: 1.3rem;
            color: #fff;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .crown-icon {
            font-size: 4rem;
            color: #ffd700;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .benefits-section {
            margin-bottom: 40px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .benefit-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            border: 2px solid rgba(255, 215, 0, 0.3);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .benefit-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .benefit-card:hover::before {
            left: 100%;
        }

        .benefit-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(255, 215, 0, 0.4);
            border-color: #ffd700;
        }

        .benefit-icon {
            font-size: 3.5rem;
            color: #ffd700;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .benefit-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffd700;
            position: relative;
            z-index: 2;
        }

        .benefit-description {
            color: #ccc;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        .campaign-info {
            background: rgba(255, 215, 0, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px solid rgba(255, 215, 0, 0.4);
            text-align: center;
        }

        .duration-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(255, 215, 0, 0.4);
        }

        .tasks-section {
            margin-bottom: 40px;
        }

        .section-title {
            text-align: center;
            color: #ffd700;
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .tasks-container {
            display: grid;
            gap: 20px;
        }

        .task-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .task-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(255, 215, 0, 0.3);
            border-color: #ffd700;
        }

        .task-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .task-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .task-icon {
            font-size: 2rem;
            color: #ffd700;
        }

        .task-details h3 {
            color: #ffd700;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .task-details p {
            color: #ccc;
            font-size: 0.9rem;
        }

        .task-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .status-completed {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .task-action {
            margin-top: 15px;
            text-align: center;
        }

        .task-btn {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .task-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
        }

        .task-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            height: 20px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            height: 100%;
            border-radius: 50px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progressShimmer 2s infinite;
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            color: #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .activate-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .activate-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 1.3rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
        }

        .activate-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(40, 167, 69, 0.6);
        }

        .activate-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: rgba(255, 215, 0, 0.2);
            border-color: #ffd700;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #ffd700;
            font-size: 1.2rem;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #86efac;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .benefits-grid {
                grid-template-columns: 1fr;
            }
            
            .task-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }

        /* Floating particles animation */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <button class="back-btn" onclick="goBack()">
        <i class="fas fa-arrow-right"></i> العودة
    </button>

    <div class="container">
        <div class="header">
            <div class="crown-icon">
                <i class="fas fa-crown"></i>
            </div>
            <h1 id="campaignTitle">احصل على اشتراك مجاني!</h1>
            <p id="campaignDescription">أكمل المهام البسيطة واحصل على ميزات مجانية رائعة</p>
        </div>

        <div class="benefits-section">
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="benefit-title">تحميل بدون إعلانات</div>
                    <div class="benefit-description">احصل على تجربة تحميل سلسة بدون انتظار أو إعلانات مزعجة</div>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="benefit-title">محتوى حصري</div>
                    <div class="benefit-description">وصول مبكر للمودات الجديدة والمحتوى الحصري للمشتركين</div>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="benefit-title">سرعة فائقة</div>
                    <div class="benefit-description">تحميل أسرع وأولوية في الخوادم للحصول على أفضل أداء</div>
                </div>
            </div>
        </div>

        <div class="campaign-info">
            <div class="duration-badge">
                <i class="fas fa-clock"></i> <span id="subscriptionDuration">30</span> يوم مجاناً
            </div>
            <p>استمتع بجميع الميزات المميزة لمدة كاملة بدون أي تكلفة!</p>
        </div>

        <div class="progress-section">
            <h3 class="section-title">تقدم المهام</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div class="progress-text" id="progressText">0 من 0 مهام مكتملة</div>
        </div>

        <div class="tasks-section">
            <h3 class="section-title">المهام المطلوبة</h3>
            <div class="tasks-container" id="tasksContainer">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل المهام...
                </div>
            </div>
        </div>

        <div class="activate-section">
            <button class="activate-btn" id="activateBtn" onclick="activateSubscription()" disabled>
                <i class="fas fa-rocket"></i> تفعيل الاشتراك المجاني
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-manager.js"></script>
    <script src="translations.js"></script>
    <script src="smart-verification.js"></script>
    <script src="subscription-page.js"></script>
</body>
</html>
