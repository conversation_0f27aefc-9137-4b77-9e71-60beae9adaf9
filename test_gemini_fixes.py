#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات نظام Gemini
Test script for Gemini system fixes
"""

import os
import sys
import json
import time
from datetime import datetime

def test_forbidden_words_list():
    """اختبار قائمة الكلمات المحظورة المحدثة"""
    print("🔍 اختبار قائمة الكلمات المحظورة المحدثة")
    print("=" * 60)
    
    # القائمة القديمة (المشكلة)
    old_forbidden_words = [
        "تخيل",
        "تجربة لعب",
        "اضافة",
        "عش تجربة",
        "عش مغامرة",
        "مرحبا شباب",
        "اليوم سوق اقدم لكم",
        "imagine",
        "gaming experience",
        "addition",
        "experience",
        "adventure"  # هذه كانت المشكلة!
    ]
    
    # القائمة الجديدة (المحسنة)
    new_forbidden_words = [
        "تخيل",
        "عش تجربة",
        "عش مغامرة", 
        "مرحبا شباب",
        "اليوم سوق اقدم لكم",
        "اليوم سأقدم لكم",
        "مرحباً شباب",
        "imagine",
        "gaming experience",
        "ultimate experience",
        "revolutionary experience",
        "transform your experience",
        "amazing adventure",
        "incredible adventure",
        "epic adventure"
    ]
    
    print("❌ القائمة القديمة كانت تحظر:")
    for word in old_forbidden_words:
        print(f"   - '{word}'")
    
    print(f"\n✅ القائمة الجديدة تحظر:")
    for word in new_forbidden_words:
        print(f"   - '{word}'")
    
    # اختبار الأوصاف
    test_descriptions = [
        "XP Crystals mod lets you find XP crystals during your exploration",  # كان يُرفض
        "This mod adds amazing adventure features",  # يُرفض بحق
        "Find crystals while exploring the world",  # يُقبل
        "Ultimate experience with incredible adventure"  # يُرفض بحق
    ]
    
    print(f"\n🧪 اختبار الأوصاف:")
    for desc in test_descriptions:
        old_rejected = any(word.lower() in desc.lower() for word in old_forbidden_words)
        new_rejected = any(word.lower() in desc.lower() for word in new_forbidden_words)
        
        print(f"\n   الوصف: '{desc[:50]}...'")
        print(f"   القائمة القديمة: {'❌ مرفوض' if old_rejected else '✅ مقبول'}")
        print(f"   القائمة الجديدة: {'❌ مرفوض' if new_rejected else '✅ مقبول'}")
        
        if old_rejected and not new_rejected:
            print(f"   🎯 تحسن: كان مرفوضاً خطأً، الآن مقبول!")
    
    return True

def test_gemini_key_switching():
    """اختبار نظام تبديل مفاتيح Gemini المحسن"""
    print(f"\n🔑 اختبار نظام تبديل مفاتيح Gemini المحسن")
    print("=" * 60)
    
    improvements = [
        "إضافة فحص GEMINI_CLIENT_OK في smart_gemini_request",
        "تحسين رسائل الخطأ في configure_gemini_client",
        "إضافة فحص حدود المصفوفة في try_next_gemini_key",
        "تحسين معالجة الأخطاء مع أنواع مختلفة من الأخطاء",
        "إضافة رسائل تشخيصية أكثر وضوحاً",
        "منع التكرار اللانهائي في التبديل بين المفاتيح"
    ]
    
    print("✅ التحسينات المطبقة:")
    for i, improvement in enumerate(improvements, 1):
        print(f"   {i}. {improvement}")
    
    # محاكاة سيناريوهات مختلفة
    scenarios = [
        {
            "name": "مفتاح واحد فقط",
            "keys_count": 1,
            "expected": "لا يحاول التبديل"
        },
        {
            "name": "عدة مفاتيح مع فشل الأول",
            "keys_count": 3,
            "expected": "يجرب المفاتيح بالترتيب"
        },
        {
            "name": "جميع المفاتيح فاشلة",
            "keys_count": 2,
            "expected": "يعيد False ويوقف GEMINI_CLIENT_OK"
        }
    ]
    
    print(f"\n🧪 سيناريوهات الاختبار:")
    for scenario in scenarios:
        print(f"   📋 {scenario['name']}")
        print(f"      عدد المفاتيح: {scenario['keys_count']}")
        print(f"      النتيجة المتوقعة: {scenario['expected']}")
    
    return True

def test_error_handling_improvements():
    """اختبار تحسينات معالجة الأخطاء"""
    print(f"\n🛠️ اختبار تحسينات معالجة الأخطاء")
    print("=" * 60)
    
    error_types = [
        {
            "type": "Rate Limit",
            "keywords": ["quota", "rate_limit", "resource_exhausted", "429"],
            "action": "التبديل إلى المفتاح التالي"
        },
        {
            "type": "Invalid API Key", 
            "keywords": ["api_key", "invalid", "permission", "authentication"],
            "action": "التبديل إلى المفتاح التالي"
        },
        {
            "type": "Network Error",
            "keywords": ["connection", "timeout", "network"],
            "action": "انتظار وإعادة المحاولة"
        }
    ]
    
    print("✅ أنواع الأخطاء المدعومة:")
    for error_type in error_types:
        print(f"   🔸 {error_type['type']}")
        print(f"      الكلمات المفتاحية: {', '.join(error_type['keywords'])}")
        print(f"      الإجراء: {error_type['action']}")
        print()
    
    return True

def test_smart_gemini_request_improvements():
    """اختبار تحسينات دالة smart_gemini_request"""
    print(f"\n🧠 اختبار تحسينات دالة smart_gemini_request")
    print("=" * 60)
    
    improvements = [
        "فحص GEMINI_CLIENT_OK قبل البدء",
        "إعادة تهيئة النموذج عند الحاجة",
        "تتبع المفاتيح المُجربة لتجنب التكرار",
        "رسائل تشخيصية مفصلة",
        "إحصائيات عن المحاولات والمفاتيح",
        "معالجة أفضل للأخطاء المختلفة"
    ]
    
    print("✅ التحسينات المطبقة:")
    for i, improvement in enumerate(improvements, 1):
        print(f"   {i}. {improvement}")
    
    # مثال على التدفق المحسن
    flow_steps = [
        "1. فحص حالة GEMINI_CLIENT_OK",
        "2. إعادة تهيئة النموذج إذا لزم الأمر", 
        "3. إرسال الطلب مع رسائل تشخيصية",
        "4. معالجة الأخطاء حسب النوع",
        "5. تبديل المفاتيح مع تجنب التكرار",
        "6. إرجاع إحصائيات مفصلة عند الفشل"
    ]
    
    print(f"\n🔄 تدفق العمل المحسن:")
    for step in flow_steps:
        print(f"   {step}")
    
    return True

def generate_fixes_report():
    """إنشاء تقرير الإصلاحات"""
    print(f"\n📊 تقرير إصلاحات نظام Gemini")
    print("=" * 60)
    
    fixes_report = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "fixes_applied": {
            "forbidden_words_list": {
                "problem": "كلمة 'adventure' كانت محظورة بشكل عام",
                "solution": "تحديث القائمة لتحظر فقط العبارات التسويقية المحددة",
                "impact": "تقليل الرفض الخاطئ للأوصاف الصحيحة"
            },
            "key_switching_system": {
                "problem": "نظام تبديل المفاتيح غير مستقر",
                "solution": "تحسين منطق التبديل مع فحص الحدود",
                "impact": "تبديل أكثر استقراراً وموثوقية"
            },
            "error_handling": {
                "problem": "معالجة أخطاء غير دقيقة",
                "solution": "تصنيف الأخطاء وإجراءات مناسبة لكل نوع",
                "impact": "استجابة أفضل للأخطاء المختلفة"
            },
            "smart_request_function": {
                "problem": "عدم فحص حالة النموذج قبل الاستخدام",
                "solution": "إضافة فحوصات شاملة وإعادة تهيئة تلقائية",
                "impact": "موثوقية أعلى في الطلبات"
            }
        },
        "expected_improvements": {
            "description_generation": "تقليل فشل إنشاء الأوصاف",
            "key_utilization": "استخدام أفضل لجميع المفاتيح المتاحة",
            "error_recovery": "تعافي أسرع من الأخطاء",
            "user_experience": "رسائل خطأ أوضح وأكثر فائدة"
        },
        "status": "FIXES_APPLIED_SUCCESSFULLY"
    }
    
    # حفظ التقرير
    with open('gemini_fixes_report.json', 'w', encoding='utf-8') as f:
        json.dump(fixes_report, f, indent=2, ensure_ascii=False)
    
    print("✅ تم تطبيق جميع الإصلاحات بنجاح")
    print("📁 تم حفظ التقرير: gemini_fixes_report.json")
    
    return fixes_report

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔧 اختبار إصلاحات نظام Gemini")
    print("=" * 60)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل الاختبارات
    tests = [
        ("اختبار قائمة الكلمات المحظورة", test_forbidden_words_list),
        ("اختبار نظام تبديل المفاتيح", test_gemini_key_switching),
        ("اختبار معالجة الأخطاء", test_error_handling_improvements),
        ("اختبار دالة smart_gemini_request", test_smart_gemini_request_improvements)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔄 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    # إنشاء التقرير النهائي
    print(f"\n" + "=" * 60)
    print(f"📈 النتائج النهائية: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print(f"🎉 تم إصلاح جميع مشاكل نظام Gemini!")
        generate_fixes_report()
        
        print(f"\n💡 الخطوات التالية:")
        print(f"   1. شغل الأداة الرئيسية واختبر إنشاء أوصاف التليجرام")
        print(f"   2. تأكد من عدم رفض الأوصاف الصحيحة خطأً")
        print(f"   3. اختبر نظام تبديل المفاتيح عند حدوث أخطاء")
        print(f"   4. راجع رسائل الخطأ للتأكد من وضوحها")
    else:
        print(f"⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
