/* ========================================
   Smart Subscription Creator Styles
   ======================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    direction: rtl;
}

/* ========================================
   Header Styles
   ======================================== */
.smart-header {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-bottom: 2px solid #ffd700;
    padding: 20px 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left h1 {
    color: #ffd700;
    font-size: 2rem;
    margin-bottom: 5px;
}

.header-left p {
    color: #ccc;
    font-size: 1rem;
}

.header-right {
    display: flex;
    gap: 15px;
}

/* ========================================
   Progress Bar
   ======================================== */
.progress-container {
    background: rgba(26, 26, 46, 0.8);
    padding: 15px 30px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffcc00);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #ccc;
}

.validation-status {
    color: #ffd700;
    font-weight: bold;
}

/* ========================================
   Main Container
   ======================================== */
.smart-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    gap: 30px;
    padding: 30px;
    min-height: calc(100vh - 200px);
}

/* ========================================
   Sidebar Styles
   ======================================== */
.smart-sidebar {
    width: 350px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.assistant-card,
.validation-card,
.analytics-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: none;
}

.assistant-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #ffd700;
}

.assistant-header h3 {
    font-size: 1.2rem;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 8px;
    border: 1px solid transparent;
}

.suggestion-item:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.3);
}

.suggestion-item.active {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    color: #ffd700;
}

.suggestion-item i {
    font-size: 1.1rem;
    width: 20px;
}

/* ========================================
   Validation Styles
   ======================================== */
.validation-card h4,
.analytics-card h4 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.validation-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    font-size: 0.9rem;
}

.validation-item.pending i {
    color: #6b7280;
}

.validation-item.valid i {
    color: #22c55e;
}

.validation-item.invalid i {
    color: #ef4444;
}

/* ========================================
   Analytics Styles
   ======================================== */
.analytics-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.analytics-item:last-child {
    border-bottom: none;
}

.analytics-value {
    font-weight: bold;
    color: #ffd700;
}

/* ========================================
   Main Content Styles
   ======================================== */
.smart-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-section {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: none;
    display: none;
    flex: 1;
}

.form-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.section-header h2 {
    color: #ffd700;
    font-size: 1.5rem;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* ========================================
   Form Styles
   ======================================== */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    color: #ffd700;
    margin-bottom: 8px;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.field-hint {
    font-size: 0.8rem;
    color: #999;
    font-weight: normal;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 12px 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.field-feedback {
    margin-top: 5px;
    font-size: 0.85rem;
    min-height: 20px;
}

.field-feedback.valid {
    color: #22c55e;
}

.field-feedback.invalid {
    color: #ef4444;
}

.char-counter {
    text-align: left;
    font-size: 0.8rem;
    color: #999;
    margin-top: 5px;
}

/* ========================================
   Settings Grid
   ======================================== */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.setting-card {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.setting-card h4 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* ========================================
   Input with Slider
   ======================================== */
.input-with-slider {
    display: flex;
    align-items: center;
    gap: 15px;
}

.input-with-slider input[type="range"] {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    outline: none;
}

.input-with-slider input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: #ffd700;
    border-radius: 50%;
    cursor: pointer;
}

.input-with-slider input[type="number"] {
    width: 80px;
    text-align: center;
}

/* ========================================
   Preset Buttons
   ======================================== */
.duration-presets,
.limit-presets {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.preset-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.preset-btn:hover,
.preset-btn.active {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ffd700;
    color: #ffd700;
}

/* ========================================
   Verification Info
   ======================================== */
.verification-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 6px;
    font-size: 0.85rem;
    color: #ffd700;
}

/* ========================================
   Button Styles
   ======================================== */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-ghost {
    background: transparent;
    color: #ffd700;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

/* ========================================
   Navigation
   ======================================== */
.section-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 215, 0, 0.3);
}

/* ========================================
   Tasks Management Styles
   ======================================== */
.tasks-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.task-templates h4,
.current-tasks h4 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.template-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.template-card:hover {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-2px);
}

.template-card i {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 10px;
}

.template-card h5 {
    color: #fff;
    margin-bottom: 8px;
    font-size: 1rem;
}

.template-card p {
    color: #ccc;
    font-size: 0.85rem;
    margin-bottom: 10px;
}

.template-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffd700;
    color: #000;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
}

.tasks-list {
    min-height: 200px;
    border: 2px dashed rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.empty-tasks {
    text-align: center;
    color: #999;
    padding: 40px 20px;
}

.empty-tasks i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #666;
}

.task-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
}

.task-details h5 {
    color: #fff;
    margin-bottom: 4px;
}

.task-details p {
    color: #ccc;
    font-size: 0.85rem;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.task-form {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    padding: 20px;
}

.task-form h4 {
    color: #ffd700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* ========================================
   Preview Styles
   ======================================== */
.preview-container {
    display: grid;
    grid-template-columns: 1fr 300px 1fr;
    gap: 30px;
    align-items: start;
}

.campaign-summary,
.final-validation {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.campaign-summary h4,
.final-validation h4 {
    color: #ffd700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    color: #ccc;
    font-size: 0.9rem;
}

.summary-value {
    color: #ffd700;
    font-weight: bold;
}

.live-preview h4 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.phone-mockup {
    background: #000;
    border-radius: 25px;
    padding: 20px 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.phone-screen {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    padding: 20px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.preview-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.preview-header h3 {
    color: #ffd700;
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.preview-header p {
    color: #ccc;
    font-size: 0.85rem;
    line-height: 1.4;
}

.preview-tasks {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.preview-task {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-task i {
    color: #ffd700;
    width: 20px;
}

.preview-task span {
    color: #fff;
    font-size: 0.85rem;
}

.preview-footer {
    margin-top: 20px;
    text-align: center;
}

.preview-btn {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
}

.publish-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 215, 0, 0.3);
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
}

.btn-success {
    background: linear-gradient(45deg, #22c55e, #16a34a);
    color: #fff;
}

.btn-success:disabled {
    background: #6b7280;
    cursor: not-allowed;
    transform: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* ========================================
   Additional Utility Styles
   ======================================== */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 25px;
    border-radius: 8px;
    z-index: 10000;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    from {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
    to {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
}

/* ========================================
   Loading States
   ======================================== */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffd700;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========================================
   Validation Results
   ======================================== */
.validation-results {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.validation-results .validation-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.validation-results .validation-item.valid {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.validation-results .validation-item.invalid {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* ========================================
   Enhanced Form Elements
   ======================================== */
.form-group input:invalid {
    border-color: #ef4444;
}

.form-group input:valid {
    border-color: #22c55e;
}

.form-group select:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

/* ========================================
   Task Item Enhancements
   ======================================== */
.task-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 215, 0, 0.4);
}

.task-actions .btn {
    padding: 6px 10px;
    font-size: 0.8rem;
}

.task-actions .btn:hover {
    transform: scale(1.05);
}

/* ========================================
   Preview Enhancements
   ======================================== */
.phone-mockup {
    position: relative;
    margin: 0 auto;
}

.phone-mockup::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #333;
    border-radius: 2px;
}

.preview-task:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.4);
}

/* ========================================
   Smart Suggestions Enhancements
   ======================================== */
.suggestion-item:hover {
    transform: translateX(-3px);
}

.suggestion-item.active {
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

/* Expert Setup Styling */
.suggestion-item[data-suggestion="expert-setup"] {
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 193, 7, 0.1));
    border: 1px solid rgba(255, 215, 0, 0.4);
}

.suggestion-item[data-suggestion="expert-setup"]:hover {
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
    border-color: #ffd700;
}

.suggestion-item[data-suggestion="expert-setup"] i {
    color: #ffd700;
}

.suggestion-item[data-suggestion="expert-setup"].active {
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.3));
    box-shadow: 0 0 0 2px #ffd700;
}

/* ========================================
   Accessibility Improvements
   ======================================== */
.btn:focus,
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ========================================
   Display Customization Styles
   ======================================== */
.display-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.display-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.display-section h4 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.location-option {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.location-option:hover {
    border-color: rgba(255, 215, 0, 0.4);
    background: rgba(255, 215, 0, 0.1);
}

.location-option input[type="checkbox"] {
    display: none;
}

.location-option label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    color: #fff;
    font-weight: 500;
}

.location-option label i {
    color: #ffd700;
    font-size: 1.2rem;
    width: 20px;
}

.location-option input[type="checkbox"]:checked + label {
    color: #ffd700;
}

.location-option input[type="checkbox"]:checked ~ * {
    background: rgba(255, 215, 0, 0.1);
}

.appearance-grid,
.effects-grid,
.timing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.color-picker-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.color-picker-group input[type="color"] {
    width: 100%;
    height: 40px;
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
}

.color-presets {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.color-preset {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #ffd700;
}

/* Live Preview Styles */
.live-display-preview {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.phone-mockup-display {
    width: 300px;
    height: 500px;
    background: #000;
    border-radius: 25px;
    padding: 20px 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
}

.phone-screen-display {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.app-header {
    background: rgba(255, 215, 0, 0.1);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ffd700;
    font-weight: bold;
}

.banner-preview {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000;
    padding: 10px;
    text-align: center;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    animation: slideDown 0.5s ease-out;
}

.popup-preview {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    z-index: 10;
    animation: zoomIn 0.5s ease-out;
}

.popup-content {
    color: #fff;
}

.popup-icon {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 10px;
}

.popup-content h3 {
    color: #ffd700;
    margin-bottom: 8px;
}

.popup-content p {
    color: #ccc;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.popup-btn {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
}

.floating-preview {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 1.2rem;
    animation: pulse 2s infinite;
    cursor: pointer;
}

.app-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mod-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    color: #fff;
    text-align: center;
}

/* Animations */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px #ffd700;
    }
    50% {
        box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700;
    }
}

/* Display Templates Styles */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.display-template {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.display-template:hover {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-3px);
}

.template-preview {
    width: 60px;
    height: 40px;
    margin: 0 auto 10px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.minimal-preview {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    color: #333;
}

.premium-preview {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: #000;
}

.gaming-preview {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    animation: pulse 2s infinite;
}

.professional-preview {
    background: linear-gradient(45deg, #2c3e50, #34495e);
}

.colorful-preview {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

.dark-preview {
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
}

.display-template h5 {
    color: #ffd700;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.display-template p {
    color: #ccc;
    font-size: 0.8rem;
    line-height: 1.3;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Advanced Settings Styles */
.advanced-settings-grid,
.interaction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.form-group input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

.form-group input[type="range"]::-webkit-slider-track {
    background: rgba(255, 215, 0, 0.2);
    height: 6px;
    border-radius: 3px;
}

.form-group input[type="range"]::-webkit-slider-thumb {
    background: #ffd700;
    height: 18px;
    width: 18px;
    border-radius: 50%;
    cursor: pointer;
}

.form-group textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 6px;
    color: #fff;
    padding: 8px;
    resize: vertical;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.form-group textarea:focus {
    border-color: #ffd700;
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

/* ========================================
   Responsive Design
   ======================================== */
@media (max-width: 1200px) {
    .smart-container {
        flex-direction: column;
    }

    .smart-sidebar {
        width: 100%;
        flex-direction: row;
        overflow-x: auto;
    }

    .assistant-card,
    .validation-card,
    .analytics-card {
        min-width: 300px;
    }

    .preview-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .smart-container {
        padding: 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .smart-sidebar {
        flex-direction: column;
    }
    
    .section-navigation {
        flex-direction: column;
        gap: 15px;
    }
}
