<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطي لقاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .database-status {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .status-card.active {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }
        
        .status-card.inactive {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        }
        
        .status-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .status-icon.active {
            color: #28a745;
        }
        
        .status-icon.inactive {
            color: #dc3545;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            color: white;
        }
        
        .action-btn.btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .action-btn.btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }
        
        .action-btn.btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .backup-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        
        .backup-card.completed {
            border-left-color: #28a745;
        }
        
        .backup-card.failed {
            border-left-color: #dc3545;
        }
        
        .backup-card.in-progress {
            border-left-color: #ffc107;
        }
        
        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .progress-container.active {
            display: block;
        }
        
        .switch-database-alert {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .database-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .sync-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .sync-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .sync-indicator.active {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .table-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .table-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .emergency-controls {
            background: linear-gradient(135deg, #ff6b6b, #dc3545);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .emergency-controls h4 {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-database"></i> إدارة النسخ الاحتياطي لقاعدة البيانات</h1>
                <p class="mb-0">نظام شامل لحماية البيانات والتبديل بين قواعد البيانات</p>
            </div>

            <!-- Database Status -->
            <div class="database-status">
                <div class="status-card" id="primaryStatus">
                    <div class="status-icon" id="primaryIcon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h4>قاعدة البيانات الأساسية</h4>
                    <p id="primaryStatusText">جاري الفحص...</p>
                    <button class="action-btn" id="switchToPrimary" onclick="switchToPrimaryDatabase()">
                        <i class="fas fa-arrow-right"></i> التبديل للأساسية
                    </button>
                </div>
                
                <div class="status-card" id="backupStatus">
                    <div class="status-icon" id="backupIcon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <h4>قاعدة البيانات الاحتياطية</h4>
                    <p id="backupStatusText">جاري الفحص...</p>
                    <button class="action-btn btn-warning" id="switchToBackup" onclick="switchToBackupDatabase()">
                        <i class="fas fa-shield-alt"></i> التبديل للاحتياطية
                    </button>
                </div>
            </div>

            <!-- Current Database Info -->
            <div class="database-info">
                <div class="sync-status">
                    <div class="sync-indicator" id="syncIndicator"></div>
                    <strong>قاعدة البيانات النشطة:</strong>
                    <span id="currentDatabaseName">جاري التحديد...</span>
                </div>
                <div>
                    <strong>آخر نسخ احتياطي:</strong>
                    <span id="lastBackupTime">لم يتم إنشاء نسخ احتياطي بعد</span>
                </div>
            </div>

            <!-- Emergency Controls -->
            <div class="emergency-controls" id="emergencyControls" style="display: none;">
                <h4><i class="fas fa-exclamation-triangle"></i> تحكم طوارئ</h4>
                <p>قاعدة البيانات الأساسية غير متاحة. يمكنك التبديل للقاعدة الاحتياطية للمتابعة.</p>
                <button class="action-btn btn-warning" onclick="forceBackupSwitch()">
                    <i class="fas fa-shield-alt"></i> التبديل الإجباري للاحتياطية
                </button>
            </div>

            <!-- Backup Controls -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h3><i class="fas fa-copy"></i> إنشاء نسخة احتياطية</h3>
                    <div class="d-grid gap-2">
                        <button class="action-btn btn-success" onclick="createFullBackup()">
                            <i class="fas fa-database"></i> نسخة احتياطية كاملة
                        </button>
                        <button class="action-btn" onclick="createIncrementalBackup()">
                            <i class="fas fa-plus-circle"></i> نسخة احتياطية تزايدية
                        </button>
                        <button class="action-btn btn-warning" onclick="scheduleAutoBackup()">
                            <i class="fas fa-clock"></i> جدولة نسخ تلقائي
                        </button>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h3><i class="fas fa-tools"></i> أدوات الإدارة</h3>
                    <div class="d-grid gap-2">
                        <button class="action-btn" onclick="testDatabaseConnections()">
                            <i class="fas fa-stethoscope"></i> فحص الاتصالات
                        </button>
                        <button class="action-btn" onclick="syncDatabases()">
                            <i class="fas fa-sync"></i> مزامنة قواعد البيانات
                        </button>
                        <button class="action-btn btn-danger" onclick="showCleanupModal()">
                            <i class="fas fa-trash"></i> تنظيف النسخ القديمة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Progress Container -->
            <div class="progress-container" id="progressContainer">
                <h4><i class="fas fa-spinner fa-spin"></i> جاري إنشاء النسخة الاحتياطية...</h4>
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         id="backupProgress" role="progressbar" style="width: 0%"></div>
                </div>
                <p id="backupProgressText">بدء العملية...</p>
                <button class="action-btn btn-danger" onclick="cancelBackup()">
                    <i class="fas fa-stop"></i> إيقاف العملية
                </button>
            </div>

            <!-- Backup History -->
            <div class="mt-4">
                <h3><i class="fas fa-history"></i> سجل النسخ الاحتياطية</h3>
                <div id="backupHistory">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل سجل النسخ الاحتياطية...</p>
                    </div>
                </div>
            </div>

            <!-- Table Sync Status -->
            <div class="mt-4">
                <h3><i class="fas fa-table"></i> حالة مزامنة الجداول</h3>
                <div class="table-status" id="tableStatus">
                    <!-- سيتم ملؤها ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <!-- Cleanup Modal -->
    <div class="modal fade" id="cleanupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تنظيف النسخ الاحتياطية القديمة</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        تحذير: هذا الإجراء سيحذف النسخ الاحتياطية القديمة نهائياً ولا يمكن التراجع عنه.
                    </div>
                    <div class="mb-3">
                        <label class="form-label">حذف النسخ الأقدم من:</label>
                        <select class="form-select" id="cleanupDays">
                            <option value="7">7 أيام</option>
                            <option value="30" selected>30 يوم</option>
                            <option value="90">90 يوم</option>
                            <option value="180">180 يوم</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="keepSuccessfulOnly">
                        <label class="form-check-label" for="keepSuccessfulOnly">
                            الاحتفاظ بالنسخ الناجحة فقط
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" onclick="executeCleanup()">
                        <i class="fas fa-trash"></i> تنفيذ التنظيف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Modal -->
    <div class="modal fade" id="restoreModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">استعادة من نسخة احتياطية</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        اختر النسخة الاحتياطية التي تريد الاستعادة منها.
                    </div>
                    <div id="restoreBackupList">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="executeRestore()" disabled>
                        <i class="fas fa-undo"></i> استعادة البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="firebase-backup-config.js"></script>
    <script src="database-backup-system.js"></script>
    <script src="database-backup-manager.js"></script>
</body>
</html>
