// Custom Sections Manager
// إدارة الأقسام المخصصة في التطبيق الرئيسي

// Global variables for custom sections
let customSectionsData = [];
let customSectionsLoaded = false;

// Load custom sections from database
async function loadCustomSections() {
    try {
        if (!supabaseClient) {
            console.warn('Supabase client not available for custom sections');
            return [];
        }

        const { data: sections, error } = await supabaseClient
            .from('custom_sections')
            .select('*')
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error loading custom sections:', error);
            return [];
        }

        customSectionsData = sections || [];
        customSectionsLoaded = true;
        
        console.log(`Loaded ${customSectionsData.length} custom sections`);
        return customSectionsData;

    } catch (error) {
        console.error('Error in loadCustomSections:', error);
        return [];
    }
}

// Get mods for a specific custom section
async function getCustomSectionMods(section) {
    try {
        if (!section.selected_mods) {
            return [];
        }

        const modIds = typeof section.selected_mods === 'string' 
            ? JSON.parse(section.selected_mods) 
            : section.selected_mods;

        if (!modIds || modIds.length === 0) {
            return [];
        }

        // Get mods from the global mods data or fetch from database
        let sectionMods = [];
        
        if (window.allModsData && window.allModsData.length > 0) {
            // Use cached mods data
            sectionMods = window.allModsData.filter(mod => modIds.includes(mod.id));
        } else {
            // Fetch from database
            const { data: mods, error } = await supabaseClient
                .from('mods')
                .select('*')
                .in('id', modIds);

            if (error) {
                console.error('Error fetching section mods:', error);
                return [];
            }

            sectionMods = mods || [];
        }

        // Sort mods according to the order in selected_mods array
        const sortedMods = modIds.map(id => sectionMods.find(mod => mod.id === id)).filter(Boolean);
        
        // Limit to max_mods
        return sortedMods.slice(0, section.max_mods || 10);

    } catch (error) {
        console.error('Error getting custom section mods:', error);
        return [];
    }
}

// Render custom section HTML
function renderCustomSection(section, mods) {
    const currentLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    const isArabic = currentLanguage === 'ar';
    const sectionName = isArabic ? section.name_ar : section.name_en;
    
    if (!mods || mods.length === 0) {
        return '';
    }

    let sectionHtml = `
        <div class="section-container custom-section" data-section-id="${section.id}">
            <div class="section-header">
                <div class="section-title-container">
                    <div class="section-icon">
                        <i class="${section.icon_class}"></i>
                    </div>
                    <h2 class="section-title">${sectionName}</h2>
                </div>
                <a href="#" class="see-all-link" onclick="showCustomSectionAll(${section.id}); return false;">
                    ${isArabic ? 'عرض الكل' : 'See All'}
                </a>
            </div>
            
            ${section.description ? `<p class="section-description">${section.description}</p>` : ''}
            
            <div class="mods-grid">
    `;

    // Render mods
    mods.forEach(mod => {
        sectionHtml += createModCard(mod);
    });

    sectionHtml += `
            </div>
        </div>
    `;

    return sectionHtml;
}

// Show all mods for a custom section
function showCustomSectionAll(sectionId) {
    const section = customSectionsData.find(s => s.id === sectionId);
    if (!section) return;

    const currentLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    const isArabic = currentLanguage === 'ar';
    const sectionName = isArabic ? section.name_ar : section.name_en;

    // Update page title and show loading
    updatePageTitle(sectionName);
    showLoadingIndicator();

    // Get all mods for this section
    getCustomSectionMods(section).then(mods => {
        hideLoadingIndicator();
        
        if (!mods || mods.length === 0) {
            showNoModsMessage(sectionName);
            return;
        }

        // Display mods in single category view
        displayCustomSectionMods(section, mods);
        
        // Update active category
        updateActiveCategoryButton('custom-' + sectionId);
    }).catch(error => {
        console.error('Error showing custom section all:', error);
        hideLoadingIndicator();
        showErrorMessage('حدث خطأ أثناء تحميل المودات');
    });
}

// Display custom section mods in single view
function displayCustomSectionMods(section, mods) {
    const currentLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    const isArabic = currentLanguage === 'ar';
    const sectionName = isArabic ? section.name_ar : section.name_en;

    // Hide all main content containers
    const containers = [
        'addons-section', 'texture-pack-section', 'shaders-section',
        'maps-section', 'seeds-section', 'news-section', 'suggested-section'
    ];
    
    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.display = 'none';
        }
    });

    // Show or create custom section container
    let customContainer = document.getElementById('custom-section-view');
    if (!customContainer) {
        customContainer = document.createElement('div');
        customContainer.id = 'custom-section-view';
        customContainer.className = 'main-content-container';
        
        const mainContent = document.querySelector('.main-content') || document.body;
        mainContent.appendChild(customContainer);
    }

    customContainer.style.display = 'block';
    
    // Render mods
    let modsHtml = `
        <div class="section-header">
            <div class="section-title-container">
                <div class="section-icon">
                    <i class="${section.icon_class}"></i>
                </div>
                <h2 class="section-title">${sectionName}</h2>
            </div>
        </div>
        
        ${section.description ? `<p class="section-description">${section.description}</p>` : ''}
        
        <div class="mods-grid">
    `;

    mods.forEach(mod => {
        modsHtml += createModCard(mod);
    });

    modsHtml += '</div>';
    
    customContainer.innerHTML = modsHtml;

    // Update page title
    updatePageTitle(sectionName);
}

// Insert custom sections into the main "All" view
async function insertCustomSectionsIntoAll() {
    try {
        if (!customSectionsLoaded) {
            await loadCustomSections();
        }

        if (!customSectionsData || customSectionsData.length === 0) {
            console.log('No custom sections to display');
            return;
        }

        const allContainer = document.getElementById('all-sections-container');
        if (!allContainer) {
            console.warn('All sections container not found');
            return;
        }

        // Process each custom section
        for (const section of customSectionsData) {
            const mods = await getCustomSectionMods(section);
            
            if (mods && mods.length > 0) {
                const sectionHtml = renderCustomSection(section, mods);
                
                if (sectionHtml) {
                    // Create a temporary container to parse HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = sectionHtml;
                    const sectionElement = tempDiv.firstElementChild;
                    
                    // Insert section based on display_order
                    insertSectionAtOrder(allContainer, sectionElement, section.display_order);
                }
            }
        }

        console.log(`Inserted ${customSectionsData.length} custom sections into All view`);

    } catch (error) {
        console.error('Error inserting custom sections:', error);
    }
}

// Insert section at specific order
function insertSectionAtOrder(container, sectionElement, displayOrder) {
    const existingSections = container.children;
    let insertIndex = existingSections.length;

    // Find the correct position based on display order
    for (let i = 0; i < existingSections.length; i++) {
        const existingSection = existingSections[i];
        const existingOrder = parseInt(existingSection.dataset.displayOrder || '999');
        
        if (displayOrder < existingOrder) {
            insertIndex = i;
            break;
        }
    }

    // Set display order for the new section
    sectionElement.dataset.displayOrder = displayOrder;

    // Insert at the calculated position
    if (insertIndex >= existingSections.length) {
        container.appendChild(sectionElement);
    } else {
        container.insertBefore(sectionElement, existingSections[insertIndex]);
    }
}

// Update existing sections with display order
function updateExistingSectionsOrder() {
    const defaultSectionsOrder = {
        'free-addons-section': 1,
        'addons-section': 2,
        'shaders-section': 3,
        'texture-pack-section': 4,
        'seeds-section': 5,
        'maps-section': 6,
        'news-section': 7,
        'suggested-section': 8
    };

    Object.entries(defaultSectionsOrder).forEach(([sectionId, order]) => {
        const section = document.getElementById(sectionId);
        if (section) {
            section.dataset.displayOrder = order;
        }
    });
}

// Initialize custom sections system
async function initializeCustomSections() {
    try {
        console.log('Initializing custom sections system...');
        
        // Load custom sections data
        await loadCustomSections();
        
        // Update existing sections with display order
        updateExistingSectionsOrder();
        
        console.log('Custom sections system initialized');

    } catch (error) {
        console.error('Error initializing custom sections:', error);
    }
}

// Export functions for global use
if (typeof window !== 'undefined') {
    window.customSectionsManager = {
        loadCustomSections,
        getCustomSectionMods,
        renderCustomSection,
        showCustomSectionAll,
        insertCustomSectionsIntoAll,
        initializeCustomSections,
        customSectionsData: () => customSectionsData
    };
}
