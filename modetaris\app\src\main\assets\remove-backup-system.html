<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إزالة نظام النسخ الاحتياطي - Mod Etaris</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .warning-box {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            color: #721c24;
        }

        .remove-button {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .remove-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .safe-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .safe-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .back-link {
            display: inline-block;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 20px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .step {
            background: #e9ecef;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .step h3 {
            color: #dc3545;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #dc3545, #c82333);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ إزالة نظام النسخ الاحتياطي</h1>
            <p>حذف نهائي لنظام DatabaseBackupSystem</p>
        </div>

        <div class="content">
            <div class="warning-box">
                <strong>⚠️ تحذير:</strong> هذا سيقوم بإزالة نظام النسخ الاحتياطي نهائياً من التطبيق. هذا الإجراء آمن ولن يؤثر على وظائف التطبيق الأساسية.
            </div>

            <div class="controls">
                <button class="remove-button" onclick="removeBackupSystemCompletely()">🗑️ إزالة نهائية لنظام النسخ الاحتياطي</button>
                <button class="remove-button" onclick="disableBackupSystemOnly()">🚫 تعطيل النظام فقط</button>
                <button class="safe-button" onclick="checkBackupSystemStatus()">🔍 فحص حالة النظام</button>
                <a href="index.html" class="back-link">← العودة للتطبيق</a>
                <a href="undo-fixes.html" class="back-link">↩️ التراجع</a>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressBar">0%</div>
            </div>

            <div class="step">
                <h3>🔍 ما سيتم إزالته</h3>
                <p><strong>1. ملف database-backup-system.js:</strong> إزالة أو تعطيل الملف</p>
                <p><strong>2. DatabaseBackupSystem class:</strong> حذف الكلاس نهائياً</p>
                <p><strong>3. مثيل databaseBackupSystem:</strong> إزالة المثيل العام</p>
                <p><strong>4. Firebase backup connections:</strong> قطع الاتصالات</p>
                <p><strong>5. تحميل السكريبت:</strong> منع تحميل الملف</p>
            </div>

            <div id="statusContainer"></div>

            <div class="log-container" id="logContainer">
                <div>🗑️ أداة إزالة نظام النسخ الاحتياطي جاهزة...</div>
                <div>⚠️ اختر نوع الإزالة المطلوب من الأزرار أعلاه</div>
            </div>
        </div>
    </div>

    <script>
        class BackupSystemRemover {
            constructor() {
                this.progress = 0;
                this.isRunning = false;
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                const logEntry = document.createElement('div');
                
                let color = '#00ff00';
                if (type === 'error') color = '#ff4444';
                if (type === 'warning') color = '#ffaa00';
                if (type === 'success') color = '#44ff44';
                
                logEntry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> <span style="color: ${color};">${message}</span>`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            updateProgress(percentage, text = '') {
                this.progress = percentage;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${percentage}%`;
                progressBar.textContent = text || `${percentage}%`;
            }

            showStatus(message, type) {
                const statusContainer = document.getElementById('statusContainer');
                const statusBox = document.createElement('div');
                statusBox.className = `status-box status-${type}`;
                statusBox.textContent = message;
                statusContainer.appendChild(statusBox);
            }

            async removeBackupSystemCompletely() {
                if (this.isRunning) return;
                this.isRunning = true;

                this.log('🗑️ بدء الإزالة النهائية لنظام النسخ الاحتياطي...', 'warning');
                this.updateProgress(0, 'بدء الإزالة...');

                try {
                    // Step 1: Remove DatabaseBackupSystem class
                    await this.removeBackupSystemClass();
                    this.updateProgress(20, 'تم حذف الكلاس');

                    // Step 2: Remove global instance
                    await this.removeGlobalInstance();
                    this.updateProgress(40, 'تم حذف المثيل العام');

                    // Step 3: Remove script tags
                    await this.removeScriptTags();
                    this.updateProgress(60, 'تم إزالة السكريبتات');

                    // Step 4: Clear Firebase connections
                    await this.clearFirebaseConnections();
                    this.updateProgress(80, 'تم قطع اتصالات Firebase');

                    // Step 5: Create permanent blocker
                    await this.createPermanentBlocker();
                    this.updateProgress(100, 'تم إنشاء حاجب دائم');

                    this.showStatus('✅ تم حذف نظام النسخ الاحتياطي نهائياً!', 'success');
                    this.log('🎉 الإزالة النهائية اكتملت بنجاح!', 'success');

                } catch (error) {
                    this.log(`❌ فشل الإزالة: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في الإزالة النهائية', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            async disableBackupSystemOnly() {
                this.log('🚫 بدء تعطيل نظام النسخ الاحتياطي...', 'warning');
                this.updateProgress(0, 'تعطيل النظام...');

                try {
                    await this.disableBackupSystemInternal();
                    this.updateProgress(100, 'تم التعطيل');
                    this.showStatus('✅ تم تعطيل نظام النسخ الاحتياطي!', 'success');
                    this.log('✅ تم تعطيل النظام بنجاح!', 'success');
                } catch (error) {
                    this.log(`❌ فشل التعطيل: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في تعطيل النظام', 'error');
                }
            }

            async removeBackupSystemClass() {
                this.log('🗑️ إزالة DatabaseBackupSystem class...', 'info');

                if (typeof window.DatabaseBackupSystem !== 'undefined') {
                    // Store original for potential restoration
                    window._originalDatabaseBackupSystem = window.DatabaseBackupSystem;
                    
                    // Delete the class
                    delete window.DatabaseBackupSystem;
                    this.log('✅ تم حذف DatabaseBackupSystem class', 'success');
                } else {
                    this.log('ℹ️ DatabaseBackupSystem class غير موجود', 'info');
                }

                // Create a blocker to prevent recreation
                window.DatabaseBackupSystem = function() {
                    console.warn('⚠️ DatabaseBackupSystem تم حذفه نهائياً');
                    return {
                        init: () => {
                            console.warn('⚠️ نظام النسخ الاحتياطي معطل');
                            return Promise.resolve();
                        }
                    };
                };

                // Mark as blocked
                window.DatabaseBackupSystem._blocked = true;
            }

            async removeGlobalInstance() {
                this.log('🗑️ إزالة مثيل databaseBackupSystem العام...', 'info');

                if (window.databaseBackupSystem) {
                    // Store original for potential restoration
                    window._originalDatabaseBackupSystemInstance = window.databaseBackupSystem;
                    
                    // Delete the instance
                    delete window.databaseBackupSystem;
                    this.log('✅ تم حذف مثيل databaseBackupSystem', 'success');
                } else {
                    this.log('ℹ️ مثيل databaseBackupSystem غير موجود', 'info');
                }

                // Create a blocker
                window.databaseBackupSystem = {
                    init: () => {
                        console.warn('⚠️ نظام النسخ الاحتياطي معطل نهائياً');
                        return Promise.resolve();
                    },
                    _blocked: true
                };
            }

            async removeScriptTags() {
                this.log('🗑️ إزالة سكريبتات database-backup-system...', 'info');

                const scripts = document.querySelectorAll('script[src*="database-backup-system"]');
                let removedCount = 0;

                scripts.forEach(script => {
                    script.remove();
                    removedCount++;
                    this.log(`🗑️ تم إزالة سكريبت: ${script.src}`, 'info');
                });

                if (removedCount > 0) {
                    this.log(`✅ تم إزالة ${removedCount} سكريبت`, 'success');
                } else {
                    this.log('ℹ️ لم يتم العثور على سكريبتات للإزالة', 'info');
                }

                // Prevent future loading
                const originalCreateElement = document.createElement;
                document.createElement = function(tagName) {
                    const element = originalCreateElement.call(this, tagName);
                    
                    if (tagName.toLowerCase() === 'script') {
                        const originalSetAttribute = element.setAttribute;
                        element.setAttribute = function(name, value) {
                            if (name === 'src' && value.includes('database-backup-system')) {
                                console.warn('⚠️ منع تحميل database-backup-system script');
                                return;
                            }
                            return originalSetAttribute.call(this, name, value);
                        };
                    }
                    
                    return element;
                };
            }

            async clearFirebaseConnections() {
                this.log('🔥 قطع اتصالات Firebase للنسخ الاحتياطي...', 'info');

                // Clear any Firebase backup connections
                if (window.backupFirestore) {
                    delete window.backupFirestore;
                    this.log('🗑️ تم حذف backupFirestore', 'info');
                }

                if (window.backupStorage) {
                    delete window.backupStorage;
                    this.log('🗑️ تم حذف backupStorage', 'info');
                }

                // Clear backup-related localStorage
                const backupKeys = [
                    'databaseBackupState',
                    'backupSystemConfig',
                    'firebaseBackupConfig'
                ];

                backupKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        this.log(`🗑️ تم مسح ${key} من localStorage`, 'info');
                    }
                });

                this.log('✅ تم قطع جميع اتصالات Firebase', 'success');
            }

            async createPermanentBlocker() {
                this.log('🛡️ إنشاء حاجب دائم...', 'info');

                // Create a permanent blocker in localStorage
                localStorage.setItem('backupSystemBlocked', 'true');
                localStorage.setItem('backupSystemBlockedAt', new Date().toISOString());

                // Override any attempts to recreate the system
                Object.defineProperty(window, 'DatabaseBackupSystem', {
                    get: function() {
                        console.warn('⚠️ DatabaseBackupSystem محذوف نهائياً');
                        return function() {
                            return {
                                init: () => Promise.resolve(),
                                _blocked: true
                            };
                        };
                    },
                    set: function(value) {
                        console.warn('⚠️ محاولة إعادة إنشاء DatabaseBackupSystem تم منعها');
                    },
                    configurable: false
                });

                Object.defineProperty(window, 'databaseBackupSystem', {
                    get: function() {
                        console.warn('⚠️ databaseBackupSystem محذوف نهائياً');
                        return {
                            init: () => Promise.resolve(),
                            _blocked: true
                        };
                    },
                    set: function(value) {
                        console.warn('⚠️ محاولة إعادة إنشاء databaseBackupSystem تم منعها');
                    },
                    configurable: false
                });

                this.log('✅ تم إنشاء حاجب دائم', 'success');
            }

            async disableBackupSystemInternal() {
                this.log('🚫 تعطيل نظام النسخ الاحتياطي...', 'info');

                // Method 1: Override the constructor
                if (typeof window.DatabaseBackupSystem !== 'undefined') {
                    const OriginalDatabaseBackupSystem = window.DatabaseBackupSystem;
                    
                    window.DatabaseBackupSystem = function() {
                        this.log('⚠️ DatabaseBackupSystem تم تعطيله', 'warning');
                        
                        return {
                            init: () => {
                                console.log('⚠️ نظام النسخ الاحتياطي معطل');
                                return Promise.resolve();
                            },
                            initializeDatabaseConnections: () => Promise.resolve(),
                            initializeFirebaseBackup: () => Promise.resolve(),
                            checkDatabaseHealth: () => Promise.resolve(),
                            createBackup: () => Promise.resolve(),
                            restoreBackup: () => Promise.resolve(),
                            _disabled: true
                        };
                    };
                }

                // Method 2: Disable existing instance
                if (window.databaseBackupSystem) {
                    this.log('🔧 تعطيل مثيل databaseBackupSystem الموجود...', 'info');
                    
                    window.databaseBackupSystem = {
                        init: () => {
                            console.log('⚠️ نظام النسخ الاحتياطي معطل');
                            return Promise.resolve();
                        },
                        initializeDatabaseConnections: () => Promise.resolve(),
                        initializeFirebaseBackup: () => Promise.resolve(),
                        checkDatabaseHealth: () => Promise.resolve(),
                        _disabled: true
                    };
                }

                this.log('✅ تم تعطيل نظام النسخ الاحتياطي', 'success');
            }

            async checkBackupSystemStatus() {
                this.log('🔍 فحص حالة نظام النسخ الاحتياطي...', 'info');

                const status = {
                    classExists: typeof window.DatabaseBackupSystem !== 'undefined',
                    instanceExists: typeof window.databaseBackupSystem !== 'undefined',
                    isBlocked: localStorage.getItem('backupSystemBlocked') === 'true',
                    scriptsFound: document.querySelectorAll('script[src*="database-backup-system"]').length
                };

                this.log('📊 نتائج الفحص:', 'info');
                this.log(`   📦 DatabaseBackupSystem class: ${status.classExists ? '✅ موجود' : '❌ غير موجود'}`, 'info');
                this.log(`   🔧 مثيل databaseBackupSystem: ${status.instanceExists ? '✅ موجود' : '❌ غير موجود'}`, 'info');
                this.log(`   🛡️ محجوب: ${status.isBlocked ? '✅ نعم' : '❌ لا'}`, 'info');
                this.log(`   📜 سكريبتات موجودة: ${status.scriptsFound}`, 'info');

                if (status.classExists || status.instanceExists) {
                    this.showStatus('⚠️ نظام النسخ الاحتياطي لا يزال موجود', 'error');
                } else {
                    this.showStatus('✅ نظام النسخ الاحتياطي غير موجود', 'success');
                }
            }
        }

        // Create global instance
        const backupSystemRemover = new BackupSystemRemover();

        // Global functions
        function removeBackupSystemCompletely() {
            backupSystemRemover.removeBackupSystemCompletely();
        }

        function disableBackupSystemOnly() {
            backupSystemRemover.disableBackupSystemOnly();
        }

        function checkBackupSystemStatus() {
            backupSystemRemover.checkBackupSystemStatus();
        }

        // Auto-check status on page load
        setTimeout(() => {
            backupSystemRemover.log('🗑️ أداة إزالة نظام النسخ الاحتياطي جاهزة', 'success');
            backupSystemRemover.checkBackupSystemStatus();
        }, 500);
    </script>
</body>
</html>
