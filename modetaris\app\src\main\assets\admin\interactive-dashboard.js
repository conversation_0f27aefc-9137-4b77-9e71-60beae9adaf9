// Interactive Dashboard JavaScript
// لوحة المعلومات التفاعلية المتقدمة

// Global variables for dashboard
let dashboardData = {};
let realTimeUpdates = true;
let updateInterval;
let chartInstances = {};

// Initialize interactive dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('Interactive Dashboard loaded');
    initializeInteractiveDashboard();
});

// Initialize dashboard
async function initializeInteractiveDashboard() {
    try {
        // Setup real-time updates
        setupRealTimeUpdates();
        
        // Load initial data
        await loadDashboardData();
        
        // Initialize charts
        initializeCharts();
        
        // Setup auto-refresh
        setupAutoRefresh();
        
        console.log('✅ Interactive dashboard initialized');
    } catch (error) {
        console.error('Error initializing interactive dashboard:', error);
    }
}

// Setup real-time updates
function setupRealTimeUpdates() {
    // Real-time data updates every 30 seconds
    updateInterval = setInterval(async () => {
        if (realTimeUpdates) {
            await updateDashboardData();
        }
    }, 30000);
}

// Load dashboard data
async function loadDashboardData() {
    try {
        showDashboardLoading(true);
        
        // Load all dashboard metrics
        await Promise.all([
            loadUserMetrics(),
            loadContentMetrics(),
            loadPerformanceMetrics(),
            loadRevenueMetrics(),
            loadSystemMetrics()
        ]);
        
        showDashboardLoading(false);
        updateLastRefreshTime();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showDashboardLoading(false);
    }
}

// Load user metrics
async function loadUserMetrics() {
    try {
        if (!supabaseClient) return;

        // Total users
        const { count: totalUsers } = await supabaseClient
            .from('user_languages')
            .select('*', { count: 'exact', head: true });

        // Active users (last 24 hours)
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        
        const { count: activeUsers } = await supabaseClient
            .from('user_languages')
            .select('*', { count: 'exact', head: true })
            .gte('updated_at', yesterday.toISOString());

        // New users today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const { count: newUsersToday } = await supabaseClient
            .from('user_languages')
            .select('*', { count: 'exact', head: true })
            .gte('created_at', today.toISOString());

        // Update dashboard
        updateMetricCard('total-users-metric', totalUsers || 0, 'إجمالي المستخدمين');
        updateMetricCard('active-users-metric', activeUsers || 0, 'المستخدمون النشطون');
        updateMetricCard('new-users-metric', newUsersToday || 0, 'مستخدمون جدد اليوم');

        // Calculate growth rate
        const growthRate = calculateGrowthRate(totalUsers, activeUsers);
        updateGrowthIndicator('user-growth', growthRate);

    } catch (error) {
        console.error('Error loading user metrics:', error);
    }
}

// Load content metrics
async function loadContentMetrics() {
    try {
        if (!supabaseClient) return;

        // Total mods
        const { count: totalMods } = await supabaseClient
            .from('mods')
            .select('*', { count: 'exact', head: true });

        // Featured mods
        const { count: featuredMods } = await supabaseClient
            .from('featured_mods')
            .select('*', { count: 'exact', head: true });

        // Popular mods (mock data for now)
        const popularMods = Math.floor(totalMods * 0.3);

        // Update dashboard
        updateMetricCard('total-mods-metric', totalMods || 0, 'إجمالي المودات');
        updateMetricCard('featured-mods-metric', featuredMods || 0, 'المودات المميزة');
        updateMetricCard('popular-mods-metric', popularMods, 'المودات الشائعة');

    } catch (error) {
        console.error('Error loading content metrics:', error);
    }
}

// Load performance metrics
async function loadPerformanceMetrics() {
    try {
        // Simulate performance data
        const performanceData = {
            responseTime: Math.floor(Math.random() * 500) + 200, // 200-700ms
            uptime: 99.9,
            errorRate: Math.random() * 2, // 0-2%
            throughput: Math.floor(Math.random() * 1000) + 500 // 500-1500 req/min
        };

        updateMetricCard('response-time-metric', performanceData.responseTime + 'ms', 'وقت الاستجابة');
        updateMetricCard('uptime-metric', performanceData.uptime + '%', 'وقت التشغيل');
        updateMetricCard('error-rate-metric', performanceData.errorRate.toFixed(2) + '%', 'معدل الأخطاء');
        updateMetricCard('throughput-metric', performanceData.throughput, 'المعدل/دقيقة');

        // Update performance indicators
        updatePerformanceIndicator('response-time-indicator', performanceData.responseTime, 500);
        updatePerformanceIndicator('uptime-indicator', performanceData.uptime, 99);
        updatePerformanceIndicator('error-rate-indicator', performanceData.errorRate, 1, true);

    } catch (error) {
        console.error('Error loading performance metrics:', error);
    }
}

// Load revenue metrics
async function loadRevenueMetrics() {
    try {
        // Simulate revenue data
        const revenueData = {
            dailyRevenue: Math.floor(Math.random() * 1000) + 500,
            monthlyRevenue: Math.floor(Math.random() * 30000) + 15000,
            adRevenue: Math.floor(Math.random() * 800) + 200,
            subscriptionRevenue: Math.floor(Math.random() * 200) + 100
        };

        updateMetricCard('daily-revenue-metric', '$' + revenueData.dailyRevenue, 'الإيرادات اليومية');
        updateMetricCard('monthly-revenue-metric', '$' + revenueData.monthlyRevenue, 'الإيرادات الشهرية');
        updateMetricCard('ad-revenue-metric', '$' + revenueData.adRevenue, 'إيرادات الإعلانات');
        updateMetricCard('subscription-revenue-metric', '$' + revenueData.subscriptionRevenue, 'إيرادات الاشتراكات');

    } catch (error) {
        console.error('Error loading revenue metrics:', error);
    }
}

// Load system metrics
async function loadSystemMetrics() {
    try {
        // Simulate system metrics
        const systemData = {
            cpuUsage: Math.floor(Math.random() * 80) + 10,
            memoryUsage: Math.floor(Math.random() * 70) + 20,
            diskUsage: Math.floor(Math.random() * 60) + 30,
            networkUsage: Math.floor(Math.random() * 90) + 10
        };

        updateSystemMetric('cpu-usage-system', systemData.cpuUsage);
        updateSystemMetric('memory-usage-system', systemData.memoryUsage);
        updateSystemMetric('disk-usage-system', systemData.diskUsage);
        updateSystemMetric('network-usage-system', systemData.networkUsage);

    } catch (error) {
        console.error('Error loading system metrics:', error);
    }
}

// Update metric card
function updateMetricCard(elementId, value, label) {
    const element = document.getElementById(elementId);
    if (element) {
        const valueElement = element.querySelector('.metric-value');
        const labelElement = element.querySelector('.metric-label');
        
        if (valueElement) valueElement.textContent = value;
        if (labelElement) labelElement.textContent = label;
        
        // Add animation
        element.classList.add('metric-updated');
        setTimeout(() => element.classList.remove('metric-updated'), 1000);
    }
}

// Update growth indicator
function updateGrowthIndicator(elementId, growthRate) {
    const element = document.getElementById(elementId);
    if (element) {
        const isPositive = growthRate >= 0;
        element.textContent = (isPositive ? '+' : '') + growthRate.toFixed(1) + '%';
        element.className = `growth-indicator ${isPositive ? 'positive' : 'negative'}`;
    }
}

// Update performance indicator
function updatePerformanceIndicator(elementId, value, threshold, inverse = false) {
    const element = document.getElementById(elementId);
    if (element) {
        const isGood = inverse ? value < threshold : value > threshold;
        element.className = `performance-indicator ${isGood ? 'good' : 'warning'}`;
    }
}

// Update system metric
function updateSystemMetric(elementId, percentage) {
    const element = document.getElementById(elementId);
    if (element) {
        const bar = element.querySelector('.system-metric-bar');
        const value = element.querySelector('.system-metric-value');
        
        if (bar) {
            bar.style.width = percentage + '%';
            bar.className = `system-metric-bar ${getSystemMetricClass(percentage)}`;
        }
        if (value) value.textContent = percentage + '%';
    }
}

// Get system metric class based on percentage
function getSystemMetricClass(percentage) {
    if (percentage < 50) return 'good';
    if (percentage < 80) return 'warning';
    return 'critical';
}

// Calculate growth rate
function calculateGrowthRate(total, active) {
    if (!total || total === 0) return 0;
    return ((active / total) * 100) - 50; // Simplified calculation
}

// Initialize charts
function initializeCharts() {
    // This would initialize Chart.js charts
    console.log('Initializing dashboard charts...');
    
    // Mock chart data
    const chartData = {
        users: generateMockChartData(7),
        downloads: generateMockChartData(7),
        revenue: generateMockChartData(7)
    };
    
    // Store chart data for later use
    dashboardData.charts = chartData;
}

// Generate mock chart data
function generateMockChartData(days) {
    const data = [];
    for (let i = 0; i < days; i++) {
        data.push({
            date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000),
            value: Math.floor(Math.random() * 1000) + 100
        });
    }
    return data;
}

// Setup auto-refresh
function setupAutoRefresh() {
    // Add refresh controls
    const refreshButton = document.getElementById('dashboard-refresh');
    if (refreshButton) {
        refreshButton.addEventListener('click', async () => {
            await loadDashboardData();
        });
    }

    // Add auto-refresh toggle
    const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
    if (autoRefreshToggle) {
        autoRefreshToggle.addEventListener('change', (e) => {
            realTimeUpdates = e.target.checked;
            updateAutoRefreshStatus();
        });
    }
}

// Update dashboard data (for real-time updates)
async function updateDashboardData() {
    try {
        // Only update key metrics for real-time
        await Promise.all([
            loadUserMetrics(),
            loadPerformanceMetrics(),
            loadSystemMetrics()
        ]);
        
        updateLastRefreshTime();
        
    } catch (error) {
        console.error('Error updating dashboard data:', error);
    }
}

// Show/hide dashboard loading
function showDashboardLoading(show) {
    const loadingElement = document.getElementById('dashboard-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

// Update last refresh time
function updateLastRefreshTime() {
    const element = document.getElementById('last-refresh-time');
    if (element) {
        element.textContent = new Date().toLocaleTimeString('ar-SA');
    }
}

// Update auto-refresh status
function updateAutoRefreshStatus() {
    const element = document.getElementById('auto-refresh-status');
    if (element) {
        element.textContent = realTimeUpdates ? 'مفعل' : 'معطل';
        element.className = `auto-refresh-status ${realTimeUpdates ? 'active' : 'inactive'}`;
    }
}

// Export dashboard data
function exportDashboardData() {
    const data = {
        timestamp: new Date().toISOString(),
        metrics: dashboardData,
        charts: dashboardData.charts
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
}

// Print dashboard report
function printDashboardReport() {
    const printContent = generateDashboardReport();
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

// Generate dashboard report
function generateDashboardReport() {
    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير لوحة المعلومات - ${new Date().toLocaleDateString('ar-SA')}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; }
                .metric { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
                .metric-value { font-weight: bold; color: #ffcc00; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير لوحة المعلومات</h1>
                <p>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                <p>الوقت: ${new Date().toLocaleTimeString('ar-SA')}</p>
            </div>
            <div class="content">
                <h2>المقاييس الرئيسية</h2>
                <div class="metric">
                    <span>إجمالي المستخدمين: </span>
                    <span class="metric-value">${document.getElementById('total-users-metric')?.querySelector('.metric-value')?.textContent || 'N/A'}</span>
                </div>
                <div class="metric">
                    <span>المستخدمون النشطون: </span>
                    <span class="metric-value">${document.getElementById('active-users-metric')?.querySelector('.metric-value')?.textContent || 'N/A'}</span>
                </div>
                <div class="metric">
                    <span>إجمالي المودات: </span>
                    <span class="metric-value">${document.getElementById('total-mods-metric')?.querySelector('.metric-value')?.textContent || 'N/A'}</span>
                </div>
            </div>
        </body>
        </html>
    `;
}

// Cleanup function
function cleanupDashboard() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
    
    // Cleanup chart instances
    Object.values(chartInstances).forEach(chart => {
        if (chart && typeof chart.destroy === 'function') {
            chart.destroy();
        }
    });
}

// Make functions globally available
window.exportDashboardData = exportDashboardData;
window.printDashboardReport = printDashboardReport;
window.loadDashboardData = loadDashboardData;

// Cleanup on page unload
window.addEventListener('beforeunload', cleanupDashboard);
