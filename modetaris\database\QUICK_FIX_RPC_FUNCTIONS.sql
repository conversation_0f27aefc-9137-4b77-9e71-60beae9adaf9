-- ========================================
-- حل سريع لمشكلة دوال RPC المفقودة
-- Quick Fix for Missing RPC Functions
-- ========================================

-- 1. إضافة عمود clicks إذا لم يكن موجوداً
ALTER TABLE mods ADD COLUMN IF NOT EXISTS clicks INTEGER DEFAULT 0;

-- 2. إنشاء دالة increment_clicks
CREATE OR REPLACE FUNCTION increment_clicks(mod_id_in UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET clicks = COALESCE(clicks, 0) + 1
    WHERE id = mod_id_in;
EXCEPTION
    WHEN OTHERS THEN
        -- تجاهل الأخطاء لتجنب توقف التطبيق
        NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. إنشاء دالة increment_downloads إذا لم تكن موجودة
CREATE OR REPLACE FUNCTION increment_downloads(mod_id_in UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET downloads = COALESCE(downloads, 0) + 1
    WHERE id = mod_id_in;
EXCEPTION
    WHEN OTHERS THEN
        NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_clicks(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION increment_downloads(UUID) TO anon, authenticated;
