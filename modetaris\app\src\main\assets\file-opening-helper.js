// نظام مساعد فتح الملفات
// File Opening Helper System

class FileOpeningHelper {
    constructor() {
        this.supportedFormats = ['.mcpack', '.mcworld', '.mcaddon', '.mctemplate'];
        this.troubleshootingSteps = {
            'ar': {
                'mcpack_not_opening': [
                    'تأكد من أن لديك Minecraft مثبت على جهازك',
                    'حاول إعادة تشغيل Minecraft',
                    'تحقق من أن الملف لم يتلف أثناء التحميل',
                    'جرب فتح الملف من مدير الملفات مباشرة',
                    'تأكد من أن إصدار Minecraft يدعم هذا المود'
                ],
                'file_corrupted': [
                    'أعد تحميل الملف مرة أخرى',
                    'تحقق من اتصال الإنترنت أثناء التحميل',
                    'امسح ذاكرة التخزين المؤقت للمتصفح',
                    'جر<PERSON> التحميل من متصفح آخر',
                    'تواصل مع الدعم الفني إذا استمرت المشكلة'
                ],
                'minecraft_not_found': [
                    'تأكد من تثبيت Minecraft Pocket Edition',
                    'حدث Minecraft إلى أحدث إصدار',
                    'أعد تشغيل جهازك',
                    'تحقق من إعدادات الأمان في جهازك',
                    'جرب فتح Minecraft أولاً ثم الملف'
                ]
            },
            'en': {
                'mcpack_not_opening': [
                    'Make sure Minecraft is installed on your device',
                    'Try restarting Minecraft',
                    'Check if the file was corrupted during download',
                    'Try opening the file directly from file manager',
                    'Ensure your Minecraft version supports this mod'
                ],
                'file_corrupted': [
                    'Re-download the file',
                    'Check your internet connection during download',
                    'Clear browser cache',
                    'Try downloading from another browser',
                    'Contact support if the problem persists'
                ],
                'minecraft_not_found': [
                    'Make sure Minecraft Pocket Edition is installed',
                    'Update Minecraft to the latest version',
                    'Restart your device',
                    'Check security settings on your device',
                    'Try opening Minecraft first, then the file'
                ]
            }
        };
        
        this.init();
    }
    
    init() {
        console.log('✅ تم تهيئة نظام مساعد فتح الملفات');
        
        // إضافة مستمع للأخطاء العامة
        window.addEventListener('error', (event) => {
            if (event.filename && this.isModFile(event.filename)) {
                this.handleFileError(event.filename, 'general_error');
            }
        });
        
        // مراقبة محاولات فتح الملفات
        this.monitorFileOpening();
    }
    
    isModFile(filename) {
        return this.supportedFormats.some(format => 
            filename.toLowerCase().includes(format)
        );
    }
    
    monitorFileOpening() {
        // مراقبة النقرات على روابط التحميل
        document.addEventListener('click', (event) => {
            const target = event.target.closest('[onclick*="downloadMod"]');
            if (target) {
                // استخراج معلومات المود
                const onclickAttr = target.getAttribute('onclick');
                const match = onclickAttr.match(/downloadMod\('([^']+)',\s*'([^']+)',\s*'([^']+)'/);
                
                if (match) {
                    const [, modId, modName, downloadUrl] = match;
                    this.trackDownloadAttempt(modId, modName, downloadUrl);
                }
            }
        });
    }
    
    trackDownloadAttempt(modId, modName, downloadUrl) {
        console.log(`📱 تتبع محاولة تحميل: ${modName}`);
        
        // حفظ معلومات المحاولة
        const attemptData = {
            modId,
            modName,
            downloadUrl,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            platform: this.detectPlatform()
        };
        
        // حفظ في localStorage للمراجعة اللاحقة
        const attempts = JSON.parse(localStorage.getItem('downloadAttempts') || '[]');
        attempts.push(attemptData);
        
        // الاحتفاظ بآخر 50 محاولة فقط
        if (attempts.length > 50) {
            attempts.splice(0, attempts.length - 50);
        }
        
        localStorage.setItem('downloadAttempts', JSON.stringify(attempts));
        
        // بدء مراقبة ما بعد التحميل
        this.startPostDownloadMonitoring(attemptData);
    }
    
    startPostDownloadMonitoring(attemptData) {
        // مراقبة لمدة 30 ثانية بعد التحميل
        setTimeout(() => {
            this.checkFileOpeningSuccess(attemptData);
        }, 5000); // فحص بعد 5 ثوان
        
        setTimeout(() => {
            this.showFileOpeningHelp(attemptData);
        }, 15000); // عرض المساعدة بعد 15 ثانية
    }
    
    checkFileOpeningSuccess(attemptData) {
        // فحص ما إذا كان المستخدم لا يزال في التطبيق
        // إذا كان لا يزال هنا، فربما لم يفتح الملف بنجاح
        
        if (document.visibilityState === 'visible') {
            console.log('⚠️ المستخدم لا يزال في التطبيق، قد تكون هناك مشكلة في فتح الملف');
            this.recordPotentialIssue(attemptData, 'file_not_opened');
        }
    }
    
    showFileOpeningHelp(attemptData) {
        if (document.visibilityState === 'visible') {
            // عرض نافذة مساعدة
            this.displayHelpModal(attemptData);
        }
    }
    
    displayHelpModal(attemptData) {
        const userLanguage = this.getUserLanguage();
        const steps = this.troubleshootingSteps[userLanguage] || this.troubleshootingSteps['ar'];
        
        const modal = document.createElement('div');
        modal.className = 'file-help-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        
        content.innerHTML = `
            <div style="margin-bottom: 20px;">
                <i class="fas fa-question-circle" style="font-size: 3em; color: #ffa500; margin-bottom: 15px;"></i>
                <h3 style="color: #333; margin-bottom: 10px;">
                    ${userLanguage === 'ar' ? 'هل تحتاج مساعدة في فتح الملف؟' : 'Need help opening the file?'}
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    ${userLanguage === 'ar' 
                        ? `لاحظنا أنك قمت بتحميل "${attemptData.modName}" ولكن قد تواجه صعوبة في فتحه.`
                        : `We noticed you downloaded "${attemptData.modName}" but might be having trouble opening it.`
                    }
                </p>
            </div>
            
            <div style="text-align: right; margin-bottom: 20px;">
                <h4 style="color: #333; margin-bottom: 15px;">
                    ${userLanguage === 'ar' ? 'خطوات حل المشكلة:' : 'Troubleshooting steps:'}
                </h4>
                <ol style="color: #555; line-height: 1.6;">
                    ${steps.mcpack_not_opening.map(step => `<li>${step}</li>`).join('')}
                </ol>
            </div>
            
            <div style="margin-bottom: 20px;">
                <button onclick="fileOpeningHelper.reportIssue('${attemptData.modId}', 'file_not_opening')" 
                        style="background: #ff6b6b; color: white; border: none; padding: 10px 20px; border-radius: 25px; margin: 5px; cursor: pointer;">
                    ${userLanguage === 'ar' ? 'الملف لا يفتح' : 'File won\'t open'}
                </button>
                <button onclick="fileOpeningHelper.reportIssue('${attemptData.modId}', 'file_corrupted')" 
                        style="background: #ffa500; color: white; border: none; padding: 10px 20px; border-radius: 25px; margin: 5px; cursor: pointer;">
                    ${userLanguage === 'ar' ? 'الملف تالف' : 'File corrupted'}
                </button>
                <button onclick="fileOpeningHelper.reportIssue('${attemptData.modId}', 'minecraft_not_found')" 
                        style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 25px; margin: 5px; cursor: pointer;">
                    ${userLanguage === 'ar' ? 'Minecraft غير موجود' : 'Minecraft not found'}
                </button>
            </div>
            
            <div>
                <button onclick="fileOpeningHelper.closeHelpModal()" 
                        style="background: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 25px; margin: 5px; cursor: pointer;">
                    ${userLanguage === 'ar' ? 'تم حل المشكلة' : 'Problem solved'}
                </button>
                <button onclick="fileOpeningHelper.closeHelpModal()" 
                        style="background: #6c757d; color: white; border: none; padding: 12px 30px; border-radius: 25px; margin: 5px; cursor: pointer;">
                    ${userLanguage === 'ar' ? 'إغلاق' : 'Close'}
                </button>
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // إغلاق عند النقر خارج المحتوى
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeHelpModal();
            }
        });
        
        // حفظ مرجع للنافذة
        this.currentHelpModal = modal;
    }
    
    closeHelpModal() {
        if (this.currentHelpModal) {
            document.body.removeChild(this.currentHelpModal);
            this.currentHelpModal = null;
        }
    }
    
    async reportIssue(modId, issueType) {
        console.log(`📝 تسجيل مشكلة: ${issueType} للمود: ${modId}`);
        
        try {
            // إرسال تقرير المشكلة إلى قاعدة البيانات
            if (typeof supabaseClient !== 'undefined' && supabaseClient) {
                const { error } = await supabaseClient
                    .from('download_errors')
                    .insert({
                        mod_id: modId,
                        mod_name: this.getModNameFromAttempts(modId),
                        original_url: this.getModUrlFromAttempts(modId),
                        error_type: issueType,
                        error_message: `User reported: ${issueType}`,
                        user_agent: navigator.userAgent,
                        timestamp: new Date().toISOString(),
                        is_resolved: false
                    });
                
                if (error) {
                    console.error('❌ خطأ في إرسال تقرير المشكلة:', error);
                } else {
                    console.log('✅ تم إرسال تقرير المشكلة بنجاح');
                    
                    // عرض رسالة شكر
                    this.showThankYouMessage();
                }
            }
            
            // إغلاق نافذة المساعدة
            this.closeHelpModal();
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل المشكلة:', error);
        }
    }
    
    showThankYouMessage() {
        const userLanguage = this.getUserLanguage();
        const message = userLanguage === 'ar' 
            ? 'شكراً لك! تم إرسال تقريرك وسنعمل على حل المشكلة قريباً.'
            : 'Thank you! Your report has been sent and we\'ll work on fixing the issue soon.';
        
        this.showAlert(message, 'success');
    }
    
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px; border-radius: 10px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
    
    getModNameFromAttempts(modId) {
        const attempts = JSON.parse(localStorage.getItem('downloadAttempts') || '[]');
        const attempt = attempts.find(a => a.modId === modId);
        return attempt ? attempt.modName : 'Unknown Mod';
    }
    
    getModUrlFromAttempts(modId) {
        const attempts = JSON.parse(localStorage.getItem('downloadAttempts') || '[]');
        const attempt = attempts.find(a => a.modId === modId);
        return attempt ? attempt.downloadUrl : '';
    }
    
    recordPotentialIssue(attemptData, issueType) {
        const issues = JSON.parse(localStorage.getItem('potentialIssues') || '[]');
        issues.push({
            ...attemptData,
            issueType,
            detectedAt: Date.now()
        });
        
        // الاحتفاظ بآخر 20 مشكلة فقط
        if (issues.length > 20) {
            issues.splice(0, issues.length - 20);
        }
        
        localStorage.setItem('potentialIssues', JSON.stringify(issues));
    }
    
    getUserLanguage() {
        // محاولة الحصول على لغة المستخدم من localStorage أو المتصفح
        return localStorage.getItem('userLanguage') || 
               (navigator.language.startsWith('ar') ? 'ar' : 'en');
    }
    
    detectPlatform() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (userAgent.includes('android')) return 'android';
        if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'ios';
        if (userAgent.includes('windows')) return 'windows';
        if (userAgent.includes('mac')) return 'mac';
        if (userAgent.includes('linux')) return 'linux';
        
        return 'unknown';
    }
    
    // وظائف إدارية للحصول على الإحصائيات
    getIssueStatistics() {
        const issues = JSON.parse(localStorage.getItem('potentialIssues') || '[]');
        const attempts = JSON.parse(localStorage.getItem('downloadAttempts') || '[]');
        
        return {
            totalAttempts: attempts.length,
            totalIssues: issues.length,
            issueRate: attempts.length > 0 ? (issues.length / attempts.length * 100).toFixed(2) : 0,
            issueTypes: this.groupIssuesByType(issues),
            platformBreakdown: this.groupIssuesByPlatform(issues)
        };
    }
    
    groupIssuesByType(issues) {
        return issues.reduce((acc, issue) => {
            acc[issue.issueType] = (acc[issue.issueType] || 0) + 1;
            return acc;
        }, {});
    }
    
    groupIssuesByPlatform(issues) {
        return issues.reduce((acc, issue) => {
            acc[issue.platform] = (acc[issue.platform] || 0) + 1;
            return acc;
        }, {});
    }
}

// إنشاء مثيل عام للنظام
window.fileOpeningHelper = new FileOpeningHelper();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileOpeningHelper;
}
