# 🆕 نظام إعدادات المودات الجديدة - New Mods Settings System

## 📋 نظرة عامة

تم إضافة نظام شامل لإدارة إعدادات المودات الجديدة يمكن الأدمن من التحكم الكامل في:
- **مدة عرض المودات الجديدة** (من 1 إلى 365 يوم)
- **مدة التخزين المؤقت** (من 1 إلى 60 دقيقة)
- **عدد المودات في الصفحة الرئيسية** (من 1 إلى 50 مود)

---

## 🗂️ الملفات المضافة

### 1. صفحة الإعدادات الرئيسية
- **المسار**: `app/src/main/assets/admin/new-mods-settings.html`
- **الوصف**: واجهة إدارة إعدادات المودات الجديدة
- **الميزات**:
  - تحكم بالمدة عبر slider أو إدخال مخصص
  - معاينة فورية للإعدادات
  - حالة النظام والاتصال بقاعدة البيانات
  - أزرار متقدمة (اختبار، إعادة تعيين، مسح التخزين)

### 2. ملف JavaScript للتحكم
- **المسار**: `app/src/main/assets/admin/new-mods-settings.js`
- **الوصف**: منطق إدارة الإعدادات
- **الوظائف الرئيسية**:
  - `saveSettings()` - حفظ الإعدادات
  - `testSettings()` - اختبار الإعدادات الجديدة
  - `resetToDefault()` - إعادة تعيين افتراضية
  - `clearCache()` - مسح التخزين المؤقت

### 3. ملف SQL لقاعدة البيانات
- **المسار**: `database/new_mods_settings_table.sql`
- **الوصف**: إنشاء جدول وإجراءات قاعدة البيانات
- **المحتويات**:
  - جدول `new_mods_settings`
  - دوال SQL للإدارة والاستعلام
  - فهارس للأداء

---

## ⚙️ كيفية الاستخدام

### 1. الوصول للإعدادات
```
لوحة الإدارة → تبويب "الإعدادات العامة" → كارت "إعدادات المودات الجديدة" → زر "إدارة الإعدادات"
```

### 2. تعديل المدة
- **عبر Slider**: اسحب المؤشر لتحديد المدة (1-30 يوم)
- **إدخال مخصص**: أدخل أي قيمة من 1 إلى 365 يوم

### 3. حفظ الإعدادات
- اضغط زر "حفظ الإعدادات"
- سيتم الحفظ في localStorage وقاعدة البيانات
- سيتم مسح التخزين المؤقت تلقائياً

---

## 🔧 التكامل مع النظام الرئيسي

### التعديلات على script.js
```javascript
// تم تعديل دالة isRecentMod لتستخدم الإعدادات
function isRecentMod(createdAt) {
    const adminDuration = parseInt(localStorage.getItem('newModsDuration') || '7');
    return diffDays <= adminDuration;
}

// تم تعديل دالة fetchNewModsFromSupabase
async function fetchNewModsFromSupabase(limit = null) {
    const adminDuration = parseInt(localStorage.getItem('newModsDuration') || '7');
    const adminCacheMinutes = parseInt(localStorage.getItem('newModsCacheMinutes') || '3');
    // ... باقي الكود
}
```

### التعديلات على لوحة الإدارة
- إضافة كارت إعدادات المودات الجديدة
- إضافة وظيفة `openNewModsSettings()`
- تحديث تلقائي لعرض الإعدادات

---

## 📊 قاعدة البيانات

### جدول new_mods_settings
```sql
CREATE TABLE new_mods_settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    duration INTEGER NOT NULL DEFAULT 7,
    cache_minutes INTEGER NOT NULL DEFAULT 3,
    home_page_limit INTEGER NOT NULL DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### الدوال المتاحة
- `get_new_mods_settings()` - جلب الإعدادات الحالية
- `update_new_mods_settings()` - تحديث الإعدادات
- `get_new_mods_with_settings()` - جلب المودات بناءً على الإعدادات

---

## 🎯 الميزات المتقدمة

### 1. التخزين المؤقت الذكي
- مدة تخزين قابلة للتخصيص
- مسح تلقائي عند تغيير الإعدادات
- مفاتيح تخزين ديناميكية تتضمن المدة

### 2. التحقق من سلامة البيانات
- فحص الاتصال بقاعدة البيانات
- عدد المودات الجديدة الحالية
- اختبار الإعدادات قبل التطبيق

### 3. واجهة مستخدم متقدمة
- معاينة فورية للتغييرات
- رسائل نجاح وخطأ واضحة
- تصميم متجاوب للجوال

---

## 🔄 دورة العمل

```
1. الأدمن يفتح صفحة الإعدادات
2. يعدل المدة المطلوبة (مثلاً من 7 إلى 14 يوم)
3. يضغط "حفظ الإعدادات"
4. النظام يحفظ في localStorage وقاعدة البيانات
5. يتم مسح التخزين المؤقت
6. التطبيق الرئيسي يستخدم المدة الجديدة فوراً
7. المودات تظهر لمدة 14 يوم بدلاً من 7
```

---

## 🛠️ الصيانة والاستكشاف

### مشاكل شائعة وحلولها

**المشكلة**: الإعدادات لا تحفظ
**الحل**: تحقق من الاتصال بقاعدة البيانات وصلاحيات localStorage

**المشكلة**: المودات لا تظهر بالمدة الجديدة
**الحل**: امسح التخزين المؤقت من صفحة الإعدادات

**المشكلة**: صفحة الإعدادات لا تفتح
**الحل**: تأكد من وجود ملف new-mods-settings.html في مجلد admin

### أوامر الصيانة
```javascript
// مسح جميع إعدادات المودات الجديدة
localStorage.removeItem('newModsDuration');
localStorage.removeItem('newModsCacheMinutes');
localStorage.removeItem('newModsHomePageLimit');

// إعادة تعيين للقيم الافتراضية
localStorage.setItem('newModsDuration', '7');
localStorage.setItem('newModsCacheMinutes', '3');
localStorage.setItem('newModsHomePageLimit', '10');
```

---

## 📈 إحصائيات الاستخدام

يمكن مراقبة:
- عدد مرات تغيير الإعدادات
- المدة الأكثر استخداماً
- تأثير التغييرات على أداء التطبيق

---

## 🔮 تطويرات مستقبلية

- إضافة إعدادات متقدمة أكثر
- جدولة تغيير الإعدادات تلقائياً
- تقارير تفصيلية عن المودات الجديدة
- إشعارات عند إضافة مودات جديدة

---

## 📞 الدعم التقني

للمساعدة أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **الملفات المرجعية**: هذا الملف + التعليقات في الكود

---

**تاريخ الإنشاء**: ديسمبر 2024  
**الإصدار**: 1.0  
**المطور**: Mod Etaris Team
