# إعداد قاعدة البيانات - Supabase Database Setup

## المشكلة الحالية - Current Issue

التطبيق يحاول إنشاء جداول جديدة باستخدام دالة `execute_sql` غير موجودة في Supabase، مما يؤدي إلى الأخطاء التالية:

The application is trying to create new tables using an `execute_sql` function that doesn't exist in Supabase, causing these errors:

```
Could not find the function public.execute_sql(sql_query) in the schema cache
```

## الحل - Solution

### الخطوة 1: إنشاء الجداول المفقودة
### Step 1: Create Missing Tables

1. افتح Supabase Dashboard
   Open Supabase Dashboard

2. اذهب إلى SQL Editor
   Go to SQL Editor

3. نفذ محتوى الملف التالي:
   Execute the content of the following file:
   ```
   database/missing_tables.sql
   ```

### الخطوة 2: إنشاء دالة execute_sql (اختيارية)
### Step 2: Create execute_sql Function (Optional)

إذا كنت تريد السماح للتطبيق بإنشاء جداول تلقائياً في المستقبل:
If you want to allow the application to create tables automatically in the future:

1. نفذ محتوى الملف التالي في SQL Editor:
   Execute the content of the following file in SQL Editor:
   ```
   database/create_execute_sql_function.sql
   ```

2. قم بإلغاء التعليق عن أسطر منح الصلاحيات في نهاية الملف
   Uncomment the permission grant lines at the end of the file

## الجداول المطلوبة - Required Tables

### 1. custom_mod_dialogs
جدول لحفظ إعدادات النوافذ المنبثقة المخصصة للمودات
Table for storing custom popup dialog configurations for mods

### 2. custom_dialog_mods
جدول ربط بين النوافذ المنبثقة والمودات المحددة
Junction table linking dialogs to specific mods

### 3. custom_copyright_mods
جدول لتتبع المودات التي لها وصف حقوق طبع ونشر مخصص
Table for tracking mods with custom copyright descriptions

## التحقق من نجاح العملية - Verify Success

بعد تنفيذ SQL، يجب أن تختفي رسائل الخطأ من console وتظهر الرسائل التالية:
After executing the SQL, error messages should disappear from console and you should see:

```
Table custom_mod_dialogs exists and is accessible
Table custom_dialog_mods exists and is accessible  
Table custom_copyright_mods exists and is accessible
```

## ملاحظات مهمة - Important Notes

1. **الأمان**: دالة `execute_sql` محدودة لتنفيذ `CREATE TABLE` فقط لأغراض الأمان
   **Security**: The `execute_sql` function is limited to `CREATE TABLE` operations only for security

2. **الصلاحيات**: تأكد من منح الصلاحيات المناسبة للدالة إذا قررت استخدامها
   **Permissions**: Make sure to grant appropriate permissions to the function if you decide to use it

3. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات قبل تنفيذ أي تغييرات
   **Backup**: Create a database backup before executing any changes

## استكشاف الأخطاء - Troubleshooting

### إذا استمرت المشاكل:
### If problems persist:

1. تحقق من صلاحيات المستخدم في Supabase
   Check user permissions in Supabase

2. تأكد من أن جميع الجداول المرجعية موجودة (مثل `mods`)
   Ensure all referenced tables exist (like `mods`)

3. راجع سجلات الأخطاء في Supabase Dashboard
   Review error logs in Supabase Dashboard

### للحصول على مساعدة إضافية:
### For additional help:

- راجع وثائق Supabase الرسمية
  Check official Supabase documentation
- تحقق من إعدادات RLS (Row Level Security)
  Check RLS (Row Level Security) settings
- تأكد من صحة متغيرات البيئة
  Verify environment variables are correct
