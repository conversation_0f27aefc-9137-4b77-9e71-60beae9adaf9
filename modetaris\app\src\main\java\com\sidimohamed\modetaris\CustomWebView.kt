package com.sidimohamed.modetaris

import android.content.Context
import android.util.AttributeSet
import android.util.Log // Import Log
import android.view.MotionEvent
import android.webkit.WebView
// Interface to notify listener about scroll position
interface ScrollListener {
    fun onScrollTopChanged(isAtTop: <PERSON>olean)
}

class CustomWebView : WebView {
    private var scrollListener: ScrollListener? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    fun setScrollListener(listener: ScrollListener) {
        this.scrollListener = listener
    }

    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
        super.onScrollChanged(l, t, oldl, oldt)
        // Check if scrolled to the top (scrollY == 0)
        val isAtTop = scrollY == 0
        // Log.d("CustomWebView", "onScrollChanged: scrollY=$scrollY, isAtTop=$isAtTop") // Optional log
        scrollListener?.onScrollTopChanged(isAtTop)
    }

    // REMOVED onTouchEvent override again to ensure simplest possible custom view
}
