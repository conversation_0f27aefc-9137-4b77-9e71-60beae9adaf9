<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام توفير البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            border: 2px solid #ffcc00;
        }
        
        h1 {
            color: #ffcc00;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 204, 0, 0.3);
        }
        
        .test-section h3 {
            color: #ffcc00;
            margin-bottom: 15px;
        }
        
        button {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: black;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.4);
        }
        
        .stats {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .cache-info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #666;
        }
        
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام توفير البيانات الذكي</h1>
        
        <div class="test-section">
            <h3>📊 إحصائيات التخزين المؤقت</h3>
            <div id="cache-stats" class="stats">
                <p>جاري تحميل الإحصائيات...</p>
            </div>
            <button onclick="updateCacheStats()">🔄 تحديث الإحصائيات</button>
            <button onclick="showDataSavingSettingsModal()">⚙️ إعدادات توفير البيانات</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات التحميل</h3>
            <div class="cache-info" id="cache-info">
                <p>معلومات التخزين المؤقت ستظهر هنا...</p>
            </div>
            <button onclick="testSmartFetch('newsItems')">📰 اختبار تحميل الأخبار</button>
            <button onclick="testSmartFetch('addonsItems')">🔧 اختبار تحميل الإضافات</button>
            <button onclick="testSmartFetch('shadersItems')">🎨 اختبار تحميل الشيدرز</button>
            <button onclick="clearAllCache()">🗑️ مسح جميع البيانات</button>
        </div>
        
        <div class="test-section">
            <h3>📈 اختبار الأداء</h3>
            <div id="performance-stats" class="cache-info">
                <p>إحصائيات الأداء ستظهر هنا...</p>
            </div>
            <button onclick="runPerformanceTest()">🚀 تشغيل اختبار الأداء</button>
            <button onclick="simulatePullToRefresh()">🔄 محاكاة Pull-to-Refresh</button>
        </div>
        
        <div class="test-section">
            <h3>📝 سجل الأحداث</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">🧹 مسح السجل</button>
        </div>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="script.js"></script>
    
    <script>
        // دوال الاختبار
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateCacheStats() {
            try {
                const stats = {
                    totalItems: 0,
                    cacheSize: 0,
                    validItems: 0,
                    expiredItems: 0
                };
                
                // حساب الإحصائيات
                Object.keys(appDataCache.lastFetchTimes).forEach(dataType => {
                    if (appDataCache[dataType]) {
                        stats.totalItems++;
                        if (isCacheValid(dataType)) {
                            stats.validItems++;
                        } else {
                            stats.expiredItems++;
                        }
                    }
                });
                
                stats.cacheSize = JSON.stringify(appDataCache).length / 1024; // KB
                
                document.getElementById('cache-stats').innerHTML = `
                    <p><strong>📦 إجمالي العناصر المحفوظة:</strong> ${stats.totalItems}</p>
                    <p><strong>✅ العناصر الصالحة:</strong> ${stats.validItems}</p>
                    <p><strong>⏰ العناصر المنتهية الصلاحية:</strong> ${stats.expiredItems}</p>
                    <p><strong>💾 حجم التخزين المؤقت:</strong> ${Math.round(stats.cacheSize)} KB</p>
                `;
                
                log(`تم تحديث الإحصائيات: ${stats.validItems}/${stats.totalItems} عنصر صالح`, 'success');
            } catch (error) {
                log(`خطأ في تحديث الإحصائيات: ${error.message}`, 'error');
            }
        }
        
        async function testSmartFetch(dataType) {
            const startTime = Date.now();
            log(`بدء اختبار تحميل ${dataType}...`, 'info');
            
            try {
                let result;
                switch(dataType) {
                    case 'newsItems':
                        result = await smartFetchNewsItems(5);
                        break;
                    case 'addonsItems':
                        result = await smartFetchAddonsItems(null, false, 5);
                        break;
                    case 'shadersItems':
                        result = await smartFetchShadersItems(null, false, 5);
                        break;
                    default:
                        throw new Error('نوع بيانات غير مدعوم');
                }
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                const fromCache = isCacheValid(dataType);
                
                log(`✅ تم تحميل ${dataType} في ${duration}ms (${fromCache ? 'من الذاكرة' : 'من الشبكة'})`, 'success');
                log(`📊 تم جلب ${result ? result.length : 0} عنصر`, 'info');
                
                updateCacheInfo();
                updateCacheStats();
            } catch (error) {
                log(`❌ خطأ في تحميل ${dataType}: ${error.message}`, 'error');
            }
        }
        
        function updateCacheInfo() {
            const info = Object.keys(appDataCache.lastFetchTimes).map(dataType => {
                const isValid = isCacheValid(dataType);
                const hasData = appDataCache[dataType] !== null;
                const lastFetch = appDataCache.lastFetchTimes[dataType];
                const age = lastFetch ? Math.round((Date.now() - lastFetch) / 1000) : 0;
                
                return `<p><strong>${dataType}:</strong> ${hasData ? '✅' : '❌'} ${isValid ? '(صالح)' : '(منتهي)'} - ${age}s</p>`;
            }).join('');
            
            document.getElementById('cache-info').innerHTML = info;
        }
        
        function clearAllCache() {
            if (confirm('هل تريد مسح جميع البيانات المحفوظة؟')) {
                forceRefreshData();
                log('🗑️ تم مسح جميع البيانات المحفوظة', 'warning');
                updateCacheStats();
                updateCacheInfo();
            }
        }
        
        async function runPerformanceTest() {
            log('🚀 بدء اختبار الأداء...', 'info');
            const results = [];
            
            // اختبار التحميل الأول (من الشبكة)
            forceRefreshData();
            const test1Start = Date.now();
            await testSmartFetch('newsItems');
            const test1Duration = Date.now() - test1Start;
            results.push(`التحميل الأول: ${test1Duration}ms`);
            
            // اختبار التحميل الثاني (من الذاكرة)
            const test2Start = Date.now();
            await testSmartFetch('newsItems');
            const test2Duration = Date.now() - test2Start;
            results.push(`التحميل الثاني: ${test2Duration}ms`);
            
            const improvement = Math.round(((test1Duration - test2Duration) / test1Duration) * 100);
            results.push(`تحسن الأداء: ${improvement}%`);
            
            document.getElementById('performance-stats').innerHTML = results.map(r => `<p>${r}</p>`).join('');
            log(`📈 اكتمل اختبار الأداء: تحسن بنسبة ${improvement}%`, 'success');
        }
        
        async function simulatePullToRefresh() {
            log('🔄 محاكاة Pull-to-Refresh...', 'info');
            try {
                await handlePullToRefresh();
                log('✅ تم تنفيذ Pull-to-Refresh بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في Pull-to-Refresh: ${error.message}`, 'error');
            }
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🎉 تم تحميل صفحة اختبار نظام توفير البيانات', 'success');
            updateCacheStats();
            updateCacheInfo();
            
            // تحديث الإحصائيات كل 10 ثوانٍ
            setInterval(() => {
                updateCacheStats();
                updateCacheInfo();
            }, 10000);
        });
    </script>
</body>
</html>
