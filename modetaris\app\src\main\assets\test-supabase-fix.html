<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Supabase</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #22c55e;
            color: white;
        }
        .error {
            background-color: #ef4444;
            color: white;
        }
        .warning {
            background-color: #f59e0b;
            color: white;
        }
        .info {
            background-color: #3b82f6;
            color: white;
        }
        .test-button {
            background-color: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #4f46e5;
        }
        .log-container {
            background-color: #000000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار إصلاح مشكلة Supabase</h1>
        
        <div id="status-container">
            <div class="status info">🔄 جاري تحميل الاختبارات...</div>
        </div>
        
        <div>
            <button class="test-button" onclick="testSupabaseLibrary()">اختبار تحميل المكتبة</button>
            <button class="test-button" onclick="testSupabaseManager()">اختبار مدير Supabase</button>
            <button class="test-button" onclick="testDatabaseConnection()">اختبار الاتصال بقاعدة البيانات</button>
            <button class="test-button" onclick="clearLogs()">مسح السجلات</button>
        </div>
        
        <div id="log-container" class="log-container">
            <div>📋 سجل الاختبارات:</div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="libs/supabase.min.js"></script>
    <script src="supabase-manager.js"></script>

    <script>
        // دالة لإضافة رسائل إلى السجل
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : 
                                  type === 'success' ? '#51cf66' : 
                                  type === 'warning' ? '#ffd43b' : '#74c0fc';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // دالة لتحديث حالة الاختبار
        function updateStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // اختبار تحميل مكتبة Supabase
        function testSupabaseLibrary() {
            addLog('🔍 بدء اختبار تحميل مكتبة Supabase...');
            
            if (typeof supabase !== 'undefined') {
                addLog('✅ مكتبة Supabase محملة بنجاح!', 'success');
                updateStatus('✅ مكتبة Supabase محملة بنجاح', 'success');
                return true;
            } else {
                addLog('❌ مكتبة Supabase غير محملة', 'error');
                updateStatus('❌ مكتبة Supabase غير محملة', 'error');
                return false;
            }
        }

        // اختبار مدير Supabase
        function testSupabaseManager() {
            addLog('🔍 بدء اختبار مدير Supabase...');
            
            if (typeof window.supabaseManager !== 'undefined') {
                addLog('✅ مدير Supabase متاح', 'success');
                
                if (window.supabaseManager.isReady && window.supabaseManager.isReady()) {
                    addLog('✅ مدير Supabase جاهز ومهيأ', 'success');
                    updateStatus('✅ مدير Supabase يعمل بشكل صحيح', 'success');
                    return true;
                } else {
                    addLog('⚠️ مدير Supabase متاح لكن غير مهيأ', 'warning');
                    updateStatus('⚠️ مدير Supabase غير مهيأ', 'warning');
                    return false;
                }
            } else {
                addLog('❌ مدير Supabase غير متاح', 'error');
                updateStatus('❌ مدير Supabase غير متاح', 'error');
                return false;
            }
        }

        // اختبار الاتصال بقاعدة البيانات
        async function testDatabaseConnection() {
            addLog('🔍 بدء اختبار الاتصال بقاعدة البيانات...');
            
            if (!testSupabaseLibrary() || !testSupabaseManager()) {
                addLog('❌ لا يمكن اختبار قاعدة البيانات - المتطلبات غير متوفرة', 'error');
                return;
            }
            
            try {
                const client = window.supabaseManager.getClient();
                if (!client) {
                    addLog('❌ لا يمكن الحصول على عميل Supabase', 'error');
                    updateStatus('❌ فشل في الحصول على عميل Supabase', 'error');
                    return;
                }
                
                addLog('🔄 محاولة الاتصال بجدول mods...');
                
                const { data, error } = await client
                    .from('mods')
                    .select('id')
                    .limit(1);
                
                if (error) {
                    addLog(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                    updateStatus('❌ فشل الاتصال بقاعدة البيانات', 'error');
                } else {
                    addLog('✅ تم الاتصال بقاعدة البيانات بنجاح!', 'success');
                    addLog(`📊 تم العثور على ${data ? data.length : 0} عنصر في الاختبار`, 'info');
                    updateStatus('✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح', 'success');
                }
                
            } catch (error) {
                addLog(`❌ خطأ غير متوقع: ${error.message}`, 'error');
                updateStatus('❌ خطأ غير متوقع في الاتصال', 'error');
            }
        }

        // مسح السجلات
        function clearLogs() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div>📋 سجل الاختبارات:</div>';
        }

        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 بدء الاختبارات التلقائية...');
            
            // انتظار قليل للتأكد من تحميل كل شيء
            setTimeout(() => {
                testSupabaseLibrary();
                setTimeout(() => {
                    testSupabaseManager();
                }, 1000);
            }, 500);
        });

        // الاستماع لأحداث تحميل Supabase
        document.addEventListener('supabaseLoaded', function() {
            addLog('📡 تم استلام إشارة تحميل مكتبة Supabase', 'success');
        });

        document.addEventListener('supabaseLoadError', function() {
            addLog('📡 تم استلام إشارة فشل تحميل مكتبة Supabase', 'error');
        });
    </script>
</body>
</html>
