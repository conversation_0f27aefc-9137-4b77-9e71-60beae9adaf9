// Social Icons Manager
// إدارة أيقونات مواقع التواصل الاجتماعي

// قاموس شامل لأيقونات مواقع التواصل الاجتماعي
const SOCIAL_ICONS_CONFIG = {
    // MCPEDL - أولوية عالية (يظهر أولاً)
    mcpedl: {
        icon: 'image/social/mcpedl.png',
        color: '#4CAF50',
        name: 'MCPEDL',
        priority: 1,
        isImage: true
    },
    
    // مواقع التواصل الاجتماعي الرئيسية
    youtube: {
        icon: 'image/social/youtube.png',
        color: '#FF0000',
        name: 'YouTube',
        priority: 2,
        isImage: true,
        fallbackIcon: 'fab fa-youtube'
    },
    
    telegram: {
        icon: 'image/social/telegram.png',
        color: '#0088CC',
        name: 'Telegram',
        priority: 3,
        isImage: true,
        fallbackIcon: 'fab fa-telegram'
    },
    
    discord: {
        icon: 'image/social/discord.png',
        color: '#5865F2',
        name: 'Discord',
        priority: 4,
        isImage: true,
        fallbackIcon: 'fab fa-discord'
    },
    
    twitter: {
        icon: 'image/social/twitter.png',
        color: '#1DA1F2',
        name: 'Twitter',
        priority: 5,
        isImage: true,
        fallbackIcon: 'fab fa-twitter'
    },
    
    x: {
        icon: 'image/social/x.png',
        color: '#000000',
        name: 'X (Twitter)',
        priority: 5,
        isImage: true,
        fallbackIcon: 'fab fa-x-twitter'
    },
    
    instagram: {
        icon: 'image/social/instagram.png',
        color: '#E4405F',
        name: 'Instagram',
        priority: 6,
        isImage: true,
        fallbackIcon: 'fab fa-instagram'
    },
    
    facebook: {
        icon: 'image/social/facebook.png',
        color: '#1877F2',
        name: 'Facebook',
        priority: 7,
        isImage: true,
        fallbackIcon: 'fab fa-facebook'
    },
    
    tiktok: {
        icon: 'image/social/tiktok.png',
        color: '#000000',
        name: 'TikTok',
        priority: 8,
        isImage: true,
        fallbackIcon: 'fab fa-tiktok'
    },
    
    github: {
        icon: 'image/social/github.png',
        color: '#333333',
        name: 'GitHub',
        priority: 9,
        isImage: true,
        fallbackIcon: 'fab fa-github'
    },
    
    linkedin: {
        icon: 'image/social/linkedin.png',
        color: '#0077B5',
        name: 'LinkedIn',
        priority: 10,
        isImage: true,
        fallbackIcon: 'fab fa-linkedin'
    },
    
    snapchat: {
        icon: 'image/social/snapchat.png',
        color: '#FFFC00',
        name: 'Snapchat',
        priority: 11,
        isImage: true,
        fallbackIcon: 'fab fa-snapchat'
    },
    
    whatsapp: {
        icon: 'image/social/whatsapp.png',
        color: '#25D366',
        name: 'WhatsApp',
        priority: 12,
        isImage: true,
        fallbackIcon: 'fab fa-whatsapp'
    },
    
    reddit: {
        icon: 'image/social/reddit.png',
        color: '#FF4500',
        name: 'Reddit',
        priority: 13,
        isImage: true,
        fallbackIcon: 'fab fa-reddit'
    },
    
    pinterest: {
        icon: 'image/social/pinterest.png',
        color: '#BD081C',
        name: 'Pinterest',
        priority: 14,
        isImage: true,
        fallbackIcon: 'fab fa-pinterest'
    },
    
    twitch: {
        icon: 'image/social/twitch.png',
        color: '#9146FF',
        name: 'Twitch',
        priority: 15,
        isImage: true,
        fallbackIcon: 'fab fa-twitch'
    },
    
    // مواقع إضافية
    website: {
        icon: 'image/social/website.png',
        color: '#4CAF50',
        name: 'Website',
        priority: 16,
        isImage: true,
        fallbackIcon: 'fas fa-globe'
    },
    
    email: {
        icon: 'image/social/email.png',
        color: '#EA4335',
        name: 'Email',
        priority: 17,
        isImage: true,
        fallbackIcon: 'fas fa-envelope'
    },
    
    // مواقع ألعاب
    steam: {
        icon: 'image/social/steam.png',
        color: '#171A21',
        name: 'Steam',
        priority: 18,
        isImage: true,
        fallbackIcon: 'fab fa-steam'
    },
    
    // مواقع عربية
    clubhouse: {
        icon: 'image/social/clubhouse.png',
        color: '#F1C40F',
        name: 'Clubhouse',
        priority: 19,
        isImage: true,
        fallbackIcon: 'fas fa-microphone'
    },
    
    // مواقع أخرى
    patreon: {
        icon: 'image/social/patreon.png',
        color: '#FF424D',
        name: 'Patreon',
        priority: 20,
        isImage: true,
        fallbackIcon: 'fab fa-patreon'
    },
    
    paypal: {
        icon: 'image/social/paypal.png',
        color: '#00457C',
        name: 'PayPal',
        priority: 21,
        isImage: true,
        fallbackIcon: 'fab fa-paypal'
    },
    
    // مواقع فيديو
    vimeo: {
        icon: 'image/social/vimeo.png',
        color: '#1AB7EA',
        name: 'Vimeo',
        priority: 22,
        isImage: true,
        fallbackIcon: 'fab fa-vimeo'
    },
    
    // مواقع موسيقى
    spotify: {
        icon: 'image/social/spotify.png',
        color: '#1DB954',
        name: 'Spotify',
        priority: 23,
        isImage: true,
        fallbackIcon: 'fab fa-spotify'
    },
    
    soundcloud: {
        icon: 'image/social/soundcloud.png',
        color: '#FF5500',
        name: 'SoundCloud',
        priority: 24,
        isImage: true,
        fallbackIcon: 'fab fa-soundcloud'
    },
    
    // مواقع أخرى
    medium: {
        icon: 'image/social/medium.png',
        color: '#000000',
        name: 'Medium',
        priority: 25,
        isImage: true,
        fallbackIcon: 'fab fa-medium'
    },
    
    behance: {
        icon: 'image/social/behance.png',
        color: '#1769FF',
        name: 'Behance',
        priority: 26,
        isImage: true,
        fallbackIcon: 'fab fa-behance'
    },
    
    dribbble: {
        icon: 'image/social/dribbble.png',
        color: '#EA4C89',
        name: 'Dribbble',
        priority: 27,
        isImage: true,
        fallbackIcon: 'fab fa-dribbble'
    }
};

// دالة للحصول على معلومات الأيقونة
function getSocialIconConfig(platform) {
    const platformKey = platform.toLowerCase().trim();
    
    // البحث المباشر
    if (SOCIAL_ICONS_CONFIG[platformKey]) {
        return SOCIAL_ICONS_CONFIG[platformKey];
    }
    
    // البحث الذكي للمطابقات الجزئية
    const partialMatches = Object.keys(SOCIAL_ICONS_CONFIG).filter(key => 
        key.includes(platformKey) || platformKey.includes(key)
    );
    
    if (partialMatches.length > 0) {
        return SOCIAL_ICONS_CONFIG[partialMatches[0]];
    }
    
    // البحث في الروابط للتعرف على المنصة
    return detectPlatformFromUrl(platformKey);
}

// دالة للتعرف على المنصة من الرابط
function detectPlatformFromUrl(url) {
    const urlLower = url.toLowerCase();
    
    // قائمة الكلمات المفتاحية للمنصات
    const platformKeywords = {
        'youtube.com': 'youtube',
        'youtu.be': 'youtube',
        'telegram.org': 'telegram',
        't.me': 'telegram',
        'discord.gg': 'discord',
        'discord.com': 'discord',
        'twitter.com': 'twitter',
        'x.com': 'x',
        'instagram.com': 'instagram',
        'facebook.com': 'facebook',
        'fb.com': 'facebook',
        'tiktok.com': 'tiktok',
        'github.com': 'github',
        'linkedin.com': 'linkedin',
        'snapchat.com': 'snapchat',
        'whatsapp.com': 'whatsapp',
        'wa.me': 'whatsapp',
        'reddit.com': 'reddit',
        'pinterest.com': 'pinterest',
        'twitch.tv': 'twitch',
        'steam': 'steam',
        'patreon.com': 'patreon',
        'paypal.com': 'paypal',
        'vimeo.com': 'vimeo',
        'spotify.com': 'spotify',
        'soundcloud.com': 'soundcloud',
        'medium.com': 'medium',
        'behance.net': 'behance',
        'dribbble.com': 'dribbble',
        'mcpedl.com': 'mcpedl'
    };
    
    for (const [keyword, platform] of Object.entries(platformKeywords)) {
        if (urlLower.includes(keyword)) {
            return SOCIAL_ICONS_CONFIG[platform];
        }
    }
    
    // إرجاع أيقونة افتراضية
    return {
        icon: 'fas fa-link',
        color: '#ffcc00',
        name: 'Link',
        priority: 999,
        isImage: false
    };
}

// دالة لترتيب المنصات حسب الأولوية
function sortSocialPlatforms(platforms) {
    return platforms.sort((a, b) => {
        const configA = getSocialIconConfig(a.platform || a.name || '');
        const configB = getSocialIconConfig(b.platform || b.name || '');
        
        return (configA.priority || 999) - (configB.priority || 999);
    });
}

// دالة لإنشاء HTML للأيقونة
function createSocialIconHTML(platform, url, size = 'medium') {
    const config = getSocialIconConfig(platform);
    
    // تحديد الأحجام
    const sizes = {
        small: { width: 30, height: 30, fontSize: 14 },
        medium: { width: 45, height: 45, fontSize: 20 },
        large: { width: 60, height: 60, fontSize: 24 }
    };
    
    const sizeConfig = sizes[size] || sizes.medium;
    
    // إنشاء محتوى الأيقونة
    let iconContent;
    if (config.isImage) {
        iconContent = `<img src="${config.icon}" alt="${config.name}" style="
            width: ${sizeConfig.fontSize}px; 
            height: ${sizeConfig.fontSize}px; 
            object-fit: contain;
        " onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">
        <i class="${config.fallbackIcon || 'fas fa-link'}" style="
            color: #fff; 
            font-size: ${sizeConfig.fontSize}px; 
            display: none;
        "></i>`;
    } else {
        iconContent = `<i class="${config.icon}" style="color: #fff; font-size: ${sizeConfig.fontSize}px;"></i>`;
    }
    
    return `
        <a href="${url}" target="_blank" style="
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: ${sizeConfig.width}px;
            height: ${sizeConfig.height}px;
            background: ${config.color};
            border-radius: 50%;
            margin: 5px;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        " onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 20px rgba(0,0,0,0.4)';" 
           onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 2px 10px rgba(0,0,0,0.3)';" 
           title="${config.name}">
            ${iconContent}
        </a>
    `;
}

// دالة لمعالجة قائمة مواقع التواصل الاجتماعي
function processSocialChannels(socialChannelsData, customSiteUrl = null, customSiteName = null, size = 'medium') {
    let socialChannels = [];
    
    // معالجة البيانات الاجتماعية
    if (socialChannelsData) {
        try {
            const channels = typeof socialChannelsData === 'string' 
                ? JSON.parse(socialChannelsData) 
                : socialChannelsData;
            
            socialChannels = Object.entries(channels)
                .filter(([platform, url]) => url && url.trim())
                .map(([platform, url]) => ({ platform, url }));
        } catch (error) {
            console.error('Error parsing social channels:', error);
        }
    }
    
    // إضافة الموقع المخصص (MCPEDL عادة)
    if (customSiteUrl && customSiteName) {
        // تحديد نوع الموقع المخصص
        let customPlatform = 'website';
        if (customSiteUrl.toLowerCase().includes('mcpedl')) {
            customPlatform = 'mcpedl';
        }
        
        socialChannels.unshift({ 
            platform: customPlatform, 
            url: customSiteUrl, 
            name: customSiteName 
        });
    }
    
    // ترتيب المنصات حسب الأولوية
    socialChannels = sortSocialPlatforms(socialChannels);
    
    // إنشاء HTML
    return socialChannels.map(({ platform, url, name }) => 
        createSocialIconHTML(platform, url, size)
    ).join('');
}

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
    window.socialIconsManager = {
        getSocialIconConfig,
        createSocialIconHTML,
        processSocialChannels,
        sortSocialPlatforms,
        SOCIAL_ICONS_CONFIG
    };
}
