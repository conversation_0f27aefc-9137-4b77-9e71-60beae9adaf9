<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراك المجاني - Mod Etaris Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .header h1 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 3rem;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .header p {
            font-size: 1.2rem;
            color: #ccc;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .stat-card .icon {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #ccc;
            font-size: 1.1rem;
        }

        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1rem;
        }

        .tab-btn.active {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .tab-content.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.1);
        }

        .form-section h3 {
            color: #ffd700;
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #ffd700;
            font-weight: bold;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #aaa;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffcc00);
            color: black;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .campaigns-list {
            display: grid;
            gap: 20px;
            margin-top: 30px;
        }

        .campaign-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.3s ease;
        }

        .campaign-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
        }

        .campaign-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .campaign-title {
            color: #ffd700;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .campaign-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid #22c55e;
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }

        .campaign-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .info-label {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .info-value {
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .campaign-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            position: relative;
            border: 2px solid #ffd700;
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #ffd700;
            font-size: 1.2rem;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .success-message {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #86efac;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2rem;
            }

            .campaign-info {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .tab-content.active {
            animation: fadeIn 0.3s ease-out;
        }

        .campaign-card {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-crown"></i> إدارة الاشتراك المجاني</h1>
            <p>نظام متقدم لإدارة حملات الاشتراك المجاني والمهام التفاعلية</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon"><i class="fas fa-bullhorn"></i></div>
                <div class="number" id="totalCampaigns">0</div>
                <div class="label">إجمالي الحملات</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-users"></i></div>
                <div class="number" id="totalSubscribers">0</div>
                <div class="label">المشتركون النشطون</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-tasks"></i></div>
                <div class="number" id="totalTasks">0</div>
                <div class="label">إجمالي المهام</div>
            </div>
            <div class="stat-card">
                <div class="icon"><i class="fas fa-chart-line"></i></div>
                <div class="number" id="completionRate">0%</div>
                <div class="label">معدل الإكمال</div>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <button class="tab-btn active" onclick="switchTab('campaigns')">
                <i class="fas fa-bullhorn"></i> إدارة الحملات
            </button>
            <button class="tab-btn" onclick="switchTab('tasks')">
                <i class="fas fa-tasks"></i> إدارة المهام
            </button>
            <button class="tab-btn" onclick="switchTab('subscribers')">
                <i class="fas fa-users"></i> المشتركون
            </button>
            <button class="tab-btn" onclick="switchTab('analytics')">
                <i class="fas fa-chart-bar"></i> التحليلات
            </button>
            <button class="tab-btn" onclick="switchTab('settings')">
                <i class="fas fa-cog"></i> الإعدادات
            </button>
        </div>

        <!-- محتوى التبويبات -->
        
        <!-- تبويب إدارة الحملات -->
        <div id="campaigns-tab" class="tab-content active">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h2 style="color: #ffd700; font-size: 2rem;">
                    <i class="fas fa-bullhorn"></i> إدارة الحملات
                </h2>
                <button class="btn btn-primary" onclick="showCreateCampaignModal()">
                    <i class="fas fa-plus"></i> إنشاء حملة جديدة
                </button>
            </div>

            <div id="campaignsList" class="campaigns-list">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل الحملات...
                </div>
            </div>
        </div>

        <!-- تبويب إدارة المهام -->
        <div id="tasks-tab" class="tab-content">
            <h2 style="color: #ffd700; margin-bottom: 30px;">
                <i class="fas fa-tasks"></i> إدارة المهام
            </h2>
            <p style="text-align: center; color: #ccc; font-size: 1.2rem; padding: 50px;">
                قريباً... سيتم إضافة إدارة المهام المتقدمة
            </p>
        </div>

        <!-- تبويب المشتركون -->
        <div id="subscribers-tab" class="tab-content">
            <h2 style="color: #ffd700; margin-bottom: 30px;">
                <i class="fas fa-users"></i> المشتركون
            </h2>
            <p style="text-align: center; color: #ccc; font-size: 1.2rem; padding: 50px;">
                قريباً... سيتم إضافة إدارة المشتركين
            </p>
        </div>

        <!-- تبويب التحليلات -->
        <div id="analytics-tab" class="tab-content">
            <h2 style="color: #ffd700; margin-bottom: 30px;">
                <i class="fas fa-chart-bar"></i> التحليلات والإحصائيات
            </h2>
            <p style="text-align: center; color: #ccc; font-size: 1.2rem; padding: 50px;">
                قريباً... سيتم إضافة التحليلات المتقدمة
            </p>
        </div>

        <!-- تبويب الإعدادات -->
        <div id="settings-tab" class="tab-content">
            <h2 style="color: #ffd700; margin-bottom: 30px;">
                <i class="fas fa-cog"></i> إعدادات النظام
            </h2>
            <p style="text-align: center; color: #ccc; font-size: 1.2rem; padding: 50px;">
                قريباً... سيتم إضافة إعدادات النظام
            </p>
        </div>
    </div>

    <!-- نافذة إنشاء حملة جديدة -->
    <div id="createCampaignModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <button class="close-modal" onclick="closeModal('createCampaignModal')">&times;</button>
            <h2 style="color: #ffd700; text-align: center; margin-bottom: 30px;">
                <i class="fas fa-plus-circle"></i> إنشاء حملة اشتراك مجاني جديدة
            </h2>
            
            <form id="createCampaignForm">
                <div class="form-grid">
                    <div class="form-section">
                        <h3>معلومات الحملة</h3>
                        
                        <div class="form-group">
                            <label for="campaignTitleAr">عنوان الحملة (عربي) *</label>
                            <input type="text" id="campaignTitleAr" required placeholder="احصل على اشتراك مجاني">
                        </div>

                        <div class="form-group">
                            <label for="campaignTitleEn">عنوان الحملة (إنجليزي) *</label>
                            <input type="text" id="campaignTitleEn" required placeholder="Get Free Subscription">
                        </div>

                        <div class="form-group">
                            <label for="campaignDescAr">وصف الحملة (عربي) *</label>
                            <textarea id="campaignDescAr" rows="3" required placeholder="احصل على ميزة مجانية تمكنك من تحميل المودات بدون إعلانات"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="campaignDescEn">وصف الحملة (إنجليزي) *</label>
                            <textarea id="campaignDescEn" rows="3" required placeholder="Get a free feature that allows you to download mods without ads"></textarea>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3>إعدادات الحملة</h3>
                        
                        <div class="form-group">
                            <label for="subscriptionDuration">مدة الاشتراك (بالأيام) *</label>
                            <input type="number" id="subscriptionDuration" value="30" min="1" max="365" required>
                        </div>

                        <div class="form-group">
                            <label for="maxUsers">الحد الأقصى للمستخدمين</label>
                            <input type="number" id="maxUsers" placeholder="اتركه فارغاً للعدد غير المحدود" min="1">
                        </div>

                        <div class="form-group">
                            <label for="campaignEndDate">تاريخ انتهاء الحملة</label>
                            <input type="datetime-local" id="campaignEndDate">
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="campaignActive" checked> الحملة نشطة
                            </label>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء الحملة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createCampaignModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="subscription_admin.js"></script>
</body>
</html>
