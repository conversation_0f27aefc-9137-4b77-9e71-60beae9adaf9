// Simple Network Handler - استعادة النظام البسيط
// نسخة مبسطة بدون تعقيدات

class NetworkHandler {
    constructor() {
        this.isOnline = navigator.onLine;
        this.setupBasicEventListeners();
    }

    setupBasicEventListeners() {
        // مراقبة بسيطة للاتصال
        window.addEventListener('online', () => {
            console.log('✅ Network: Back online');
            this.isOnline = true;
        });

        window.addEventListener('offline', () => {
            console.log('❌ Network: Gone offline');
            this.isOnline = false;
        });
    }

}

// تشغيل بسيط للنظام
let networkHandler;
document.addEventListener('DOMContentLoaded', function() {
    networkHandler = new NetworkHandler();
    console.log('✅ Simple network handler initialized');
});
