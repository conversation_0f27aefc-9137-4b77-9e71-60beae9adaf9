-- ========================================
-- نظام الاشتراك المجاني المحسن والذكي
-- Enhanced Smart Free Subscription System
-- يدعم: يوتيوب، تيليجرام، ديسكورد فقط
-- ========================================

-- حذف الجداول الموجودة إذا كانت موجودة (للتطوير فقط)
DROP TABLE IF EXISTS user_task_progress CASCADE;
DROP TABLE IF EXISTS user_subscriptions CASCADE;
DROP TABLE IF EXISTS campaign_tasks CASCADE;
DROP TABLE IF EXISTS free_subscription_campaigns CASCADE;
DROP TABLE IF EXISTS task_types CASCADE;
DROP TABLE IF EXISTS verification_logs CASCADE;

-- ========================================
-- 1. جدول أنواع المهام المحدثة (المنصات الثلاث فقط)
-- ========================================
CREATE TABLE task_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name_ar VARCHAR(200) NOT NULL,
    display_name_en VARCHAR(200) NOT NULL,
    icon VARCHAR(100),
    verification_method VARCHAR(50) DEFAULT 'smart', -- smart, manual
    api_endpoint TEXT, -- للتحقق الذكي
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إدراج أنواع المهام المدعومة (المنصات الثلاث فقط)
INSERT INTO task_types (name, display_name_ar, display_name_en, icon, verification_method) VALUES
('youtube_subscribe', 'اشتراك في قناة يوتيوب', 'Subscribe to YouTube Channel', 'fab fa-youtube', 'smart'),
('telegram_subscribe', 'اشتراك في قناة تيليجرام', 'Subscribe to Telegram Channel', 'fab fa-telegram', 'smart'),
('discord_join', 'انضمام لخادم ديسكورد', 'Join Discord Server', 'fab fa-discord', 'smart')
ON CONFLICT (name) DO NOTHING;

-- ========================================
-- 2. جدول حملات الاشتراك المجاني المحسن
-- ========================================
CREATE TABLE free_subscription_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT NOT NULL,
    description_en TEXT NOT NULL,
    banner_image_url TEXT DEFAULT '',
    popup_image_url TEXT,
    subscription_duration_days INTEGER NOT NULL DEFAULT 30,
    max_users INTEGER DEFAULT NULL, -- NULL = unlimited
    current_users INTEGER DEFAULT 0,
    verification_strictness VARCHAR(20) DEFAULT 'medium', -- low, medium, high
    auto_verify_enabled BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 3. جدول مهام الحملة المحسن
-- ========================================
CREATE TABLE campaign_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id) ON DELETE CASCADE,
    task_type VARCHAR(100) NOT NULL REFERENCES task_types(name),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    target_url TEXT NOT NULL, -- رابط القناة/الخادم
    target_id VARCHAR(200), -- معرف القناة/الخادم للتحقق
    verification_method VARCHAR(50) DEFAULT 'smart', -- smart, manual, hybrid
    verification_config JSONB DEFAULT '{}', -- إعدادات التحقق المخصصة
    retry_attempts INTEGER DEFAULT 3, -- عدد المحاولات المسموحة
    verification_delay_seconds INTEGER DEFAULT 30, -- تأخير التحقق بالثواني
    display_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 4. جدول اشتراكات المستخدمين
-- ========================================
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending', -- pending, active, expired, cancelled, suspended
    verification_score INTEGER DEFAULT 0, -- نقاط التحقق (0-100)
    started_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, campaign_id)
);

-- ========================================
-- 5. جدول تقدم المستخدم في المهام المحسن
-- ========================================
CREATE TABLE user_task_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id) ON DELETE CASCADE,
    task_id UUID NOT NULL REFERENCES campaign_tasks(id),
    status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, verified, failed
    verification_attempts INTEGER DEFAULT 0,
    verification_score INTEGER DEFAULT 0, -- نقاط التحقق للمهمة (0-100)
    completed_at TIMESTAMP WITH TIME ZONE,
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_data JSONB DEFAULT '{}', -- بيانات التحقق التفصيلية
    error_message TEXT, -- رسالة الخطأ في حالة الفشل
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, task_id)
);

-- ========================================
-- 6. جدول سجلات التحقق (للمراقبة والتحليل)
-- ========================================
CREATE TABLE verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    task_id UUID NOT NULL REFERENCES campaign_tasks(id),
    verification_type VARCHAR(50) NOT NULL, -- youtube_api, telegram_check, discord_check
    verification_method VARCHAR(50) NOT NULL, -- api, scraping, manual
    success BOOLEAN NOT NULL,
    response_data JSONB DEFAULT '{}',
    error_message TEXT,
    verification_time_ms INTEGER, -- وقت التحقق بالميلي ثانية
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- 7. الفهارس لتحسين الأداء
-- ========================================
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_expires_at ON user_subscriptions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_user_id ON user_task_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_status ON user_task_progress(status);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_task_id ON user_task_progress(task_id);
CREATE INDEX IF NOT EXISTS idx_campaign_tasks_campaign_id ON campaign_tasks(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_tasks_task_type ON campaign_tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_verification_logs_user_id ON verification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_logs_task_id ON verification_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_verification_logs_created_at ON verification_logs(created_at);

-- ========================================
-- 8. دوال التحديث التلقائي
-- ========================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء triggers للتحديث التلقائي
CREATE TRIGGER update_free_subscription_campaigns_updated_at
    BEFORE UPDATE ON free_subscription_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_task_progress_updated_at
    BEFORE UPDATE ON user_task_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 9. دوال التحقق الذكي
-- ========================================

-- دالة التحقق من اشتراك يوتيوب
CREATE OR REPLACE FUNCTION verify_youtube_subscription(
    p_user_id VARCHAR(100),
    p_task_id UUID,
    p_channel_id VARCHAR(200)
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    verification_score INTEGER := 0;
BEGIN
    -- محاكاة التحقق من اشتراك يوتيوب
    -- في التطبيق الحقيقي، ستستخدم YouTube API

    -- تسجيل محاولة التحقق
    INSERT INTO verification_logs (
        user_id, task_id, verification_type, verification_method,
        success, response_data, created_at
    ) VALUES (
        p_user_id, p_task_id, 'youtube_api', 'api_simulation',
        true, '{"channel_id": "' || p_channel_id || '", "subscribed": true}',
        CURRENT_TIMESTAMP
    );

    -- تحديد نقاط التحقق (محاكاة)
    verification_score := 85 + (RANDOM() * 15)::INTEGER;

    result := jsonb_build_object(
        'success', true,
        'platform', 'youtube',
        'channel_id', p_channel_id,
        'subscribed', true,
        'verification_score', verification_score,
        'verified_at', CURRENT_TIMESTAMP,
        'method', 'api_check'
    );

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ
        INSERT INTO verification_logs (
            user_id, task_id, verification_type, verification_method,
            success, error_message, created_at
        ) VALUES (
            p_user_id, p_task_id, 'youtube_api', 'api_simulation',
            false, SQLERRM, CURRENT_TIMESTAMP
        );

        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'platform', 'youtube'
        );
END;
$$ LANGUAGE plpgsql;

-- دالة التحقق من اشتراك تيليجرام
CREATE OR REPLACE FUNCTION verify_telegram_subscription(
    p_user_id VARCHAR(100),
    p_task_id UUID,
    p_channel_username VARCHAR(200)
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    verification_score INTEGER := 0;
BEGIN
    -- محاكاة التحقق من اشتراك تيليجرام
    -- في التطبيق الحقيقي، ستستخدم Telegram Bot API

    -- تسجيل محاولة التحقق
    INSERT INTO verification_logs (
        user_id, task_id, verification_type, verification_method,
        success, response_data, created_at
    ) VALUES (
        p_user_id, p_task_id, 'telegram_check', 'bot_api_simulation',
        true, '{"channel": "' || p_channel_username || '", "member": true}',
        CURRENT_TIMESTAMP
    );

    -- تحديد نقاط التحقق (محاكاة)
    verification_score := 80 + (RANDOM() * 20)::INTEGER;

    result := jsonb_build_object(
        'success', true,
        'platform', 'telegram',
        'channel', p_channel_username,
        'member', true,
        'verification_score', verification_score,
        'verified_at', CURRENT_TIMESTAMP,
        'method', 'bot_api_check'
    );

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO verification_logs (
            user_id, task_id, verification_type, verification_method,
            success, error_message, created_at
        ) VALUES (
            p_user_id, p_task_id, 'telegram_check', 'bot_api_simulation',
            false, SQLERRM, CURRENT_TIMESTAMP
        );

        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'platform', 'telegram'
        );
END;
$$ LANGUAGE plpgsql;

-- دالة التحقق من انضمام ديسكورد
CREATE OR REPLACE FUNCTION verify_discord_membership(
    p_user_id VARCHAR(100),
    p_task_id UUID,
    p_server_id VARCHAR(200)
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    verification_score INTEGER := 0;
BEGIN
    -- محاكاة التحقق من عضوية ديسكورد
    -- في التطبيق الحقيقي، ستستخدم Discord API

    -- تسجيل محاولة التحقق
    INSERT INTO verification_logs (
        user_id, task_id, verification_type, verification_method,
        success, response_data, created_at
    ) VALUES (
        p_user_id, p_task_id, 'discord_check', 'api_simulation',
        true, '{"server_id": "' || p_server_id || '", "member": true}',
        CURRENT_TIMESTAMP
    );

    -- تحديد نقاط التحقق (محاكاة)
    verification_score := 75 + (RANDOM() * 25)::INTEGER;

    result := jsonb_build_object(
        'success', true,
        'platform', 'discord',
        'server_id', p_server_id,
        'member', true,
        'verification_score', verification_score,
        'verified_at', CURRENT_TIMESTAMP,
        'method', 'api_check'
    );

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO verification_logs (
            user_id, task_id, verification_type, verification_method,
            success, error_message, created_at
        ) VALUES (
            p_user_id, p_task_id, 'discord_check', 'api_simulation',
            false, SQLERRM, CURRENT_TIMESTAMP
        );

        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'platform', 'discord'
        );
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 10. دالة التحقق الذكي الموحدة
-- ========================================
CREATE OR REPLACE FUNCTION smart_verify_task(
    p_user_id VARCHAR(100),
    p_task_id UUID
)
RETURNS JSONB AS $$
DECLARE
    task_record RECORD;
    verification_result JSONB;
    current_attempts INTEGER;
    max_attempts INTEGER;
BEGIN
    -- الحصول على تفاصيل المهمة
    SELECT ct.*, tt.verification_method as default_verification_method
    INTO task_record
    FROM campaign_tasks ct
    JOIN task_types tt ON ct.task_type = tt.name
    WHERE ct.id = p_task_id;

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Task not found'
        );
    END IF;

    -- التحقق من عدد المحاولات
    SELECT COALESCE(verification_attempts, 0) INTO current_attempts
    FROM user_task_progress
    WHERE user_id = p_user_id AND task_id = p_task_id;

    max_attempts := COALESCE(task_record.retry_attempts, 3);

    IF current_attempts >= max_attempts THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Maximum verification attempts exceeded',
            'max_attempts', max_attempts
        );
    END IF;

    -- تحديث عدد المحاولات
    INSERT INTO user_task_progress (user_id, task_id, campaign_id, verification_attempts, status)
    VALUES (p_user_id, p_task_id, task_record.campaign_id, current_attempts + 1, 'in_progress')
    ON CONFLICT (user_id, task_id)
    DO UPDATE SET
        verification_attempts = user_task_progress.verification_attempts + 1,
        status = 'in_progress',
        updated_at = CURRENT_TIMESTAMP;

    -- تنفيذ التحقق حسب نوع المهمة
    CASE task_record.task_type
        WHEN 'youtube_subscribe' THEN
            verification_result := verify_youtube_subscription(
                p_user_id,
                p_task_id,
                task_record.target_id
            );
        WHEN 'telegram_subscribe' THEN
            verification_result := verify_telegram_subscription(
                p_user_id,
                p_task_id,
                task_record.target_id
            );
        WHEN 'discord_join' THEN
            verification_result := verify_discord_membership(
                p_user_id,
                p_task_id,
                task_record.target_id
            );
        ELSE
            verification_result := jsonb_build_object(
                'success', false,
                'error', 'Unsupported task type: ' || task_record.task_type
            );
    END CASE;

    -- تحديث حالة المهمة بناءً على نتيجة التحقق
    IF (verification_result->>'success')::boolean THEN
        UPDATE user_task_progress
        SET
            status = 'verified',
            verification_score = (verification_result->>'verification_score')::integer,
            verified_at = CURRENT_TIMESTAMP,
            verification_data = verification_result,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id AND task_id = p_task_id;
    ELSE
        UPDATE user_task_progress
        SET
            status = 'failed',
            error_message = verification_result->>'error',
            verification_data = verification_result,
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = p_user_id AND task_id = p_task_id;
    END IF;

    RETURN verification_result;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 11. دالة تفعيل الاشتراك المجاني
-- ========================================
CREATE OR REPLACE FUNCTION activate_free_subscription(
    p_user_id VARCHAR(100),
    p_campaign_id UUID
)
RETURNS JSONB AS $$
DECLARE
    campaign_record RECORD;
    required_tasks_count INTEGER;
    completed_tasks_count INTEGER;
    avg_verification_score NUMERIC;
    subscription_record RECORD;
BEGIN
    -- الحصول على تفاصيل الحملة
    SELECT * INTO campaign_record
    FROM free_subscription_campaigns
    WHERE id = p_campaign_id AND is_active = true;

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Campaign not found or inactive'
        );
    END IF;

    -- التحقق من الحد الأقصى للمستخدمين
    IF campaign_record.max_users IS NOT NULL AND
       campaign_record.current_users >= campaign_record.max_users THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Campaign has reached maximum users limit'
        );
    END IF;

    -- عد المهام المطلوبة
    SELECT COUNT(*) INTO required_tasks_count
    FROM campaign_tasks
    WHERE campaign_id = p_campaign_id AND is_required = true;

    -- عد المهام المكتملة والتحقق من متوسط النقاط
    SELECT
        COUNT(*) as completed_count,
        AVG(verification_score) as avg_score
    INTO completed_tasks_count, avg_verification_score
    FROM user_task_progress utp
    JOIN campaign_tasks ct ON utp.task_id = ct.id
    WHERE utp.user_id = p_user_id
      AND ct.campaign_id = p_campaign_id
      AND ct.is_required = true
      AND utp.status = 'verified';

    -- التحقق من إكمال جميع المهام المطلوبة
    IF completed_tasks_count < required_tasks_count THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Not all required tasks completed',
            'completed_tasks', completed_tasks_count,
            'required_tasks', required_tasks_count
        );
    END IF;

    -- التحقق من متوسط نقاط التحقق (يجب أن يكون أعلى من 70)
    IF COALESCE(avg_verification_score, 0) < 70 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Verification score too low',
            'average_score', COALESCE(avg_verification_score, 0),
            'minimum_required', 70
        );
    END IF;

    -- تفعيل الاشتراك
    INSERT INTO user_subscriptions (
        user_id,
        campaign_id,
        status,
        verification_score,
        started_at,
        expires_at
    ) VALUES (
        p_user_id,
        p_campaign_id,
        'active',
        avg_verification_score::integer,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP + INTERVAL '1 day' * campaign_record.subscription_duration_days
    )
    ON CONFLICT (user_id, campaign_id)
    DO UPDATE SET
        status = 'active',
        verification_score = avg_verification_score::integer,
        started_at = CURRENT_TIMESTAMP,
        expires_at = CURRENT_TIMESTAMP + INTERVAL '1 day' * campaign_record.subscription_duration_days,
        updated_at = CURRENT_TIMESTAMP
    RETURNING * INTO subscription_record;

    -- تحديث عدد المستخدمين في الحملة
    UPDATE free_subscription_campaigns
    SET current_users = current_users + 1
    WHERE id = p_campaign_id;

    RETURN jsonb_build_object(
        'success', true,
        'subscription_id', subscription_record.id,
        'expires_at', subscription_record.expires_at,
        'verification_score', subscription_record.verification_score,
        'duration_days', campaign_record.subscription_duration_days
    );
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 12. سياسات الأمان (RLS)
-- ========================================

-- تفعيل RLS على الجداول
ALTER TABLE free_subscription_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_task_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_types ENABLE ROW LEVEL SECURITY; -- New: Enable RLS for task_types

-- سياسات القراءة العامة لأنواع المهام
CREATE POLICY "Allow public read access on task types"
    ON task_types FOR SELECT
    USING (true);

-- سياسات القراءة العامة للحملات النشطة
CREATE POLICY "Allow public read access on active campaigns"
    ON free_subscription_campaigns FOR SELECT
    USING (is_active = true AND (end_date IS NULL OR end_date > CURRENT_TIMESTAMP));

-- سياسات القراءة العامة لمهام الحملات
CREATE POLICY "Allow public read access on campaign tasks"
    ON campaign_tasks FOR SELECT
    USING (true);

-- سياسات للمستخدمين - قراءة اشتراكاتهم فقط
CREATE POLICY "Allow users to read their own subscriptions"
    ON user_subscriptions FOR SELECT
    USING (true);

CREATE POLICY "Allow users to insert their own subscriptions"
    ON user_subscriptions FOR INSERT
    WITH CHECK (true);

-- سياسات تقدم المهام - المستخدمون يمكنهم قراءة وتحديث تقدمهم فقط
CREATE POLICY "Allow users to read their own task progress"
    ON user_task_progress FOR SELECT
    USING (true);

CREATE POLICY "Allow users to insert/update their own task progress"
    ON user_task_progress FOR ALL
    USING (true);

-- سياسات سجلات التحقق - قراءة فقط للمستخدمين
CREATE POLICY "Allow users to read their own verification logs"
    ON verification_logs FOR SELECT
    USING (true);

-- سياسات الإدارة (للمشرفين)
CREATE POLICY "Allow admin full access on campaigns"
    ON free_subscription_campaigns FOR ALL
    USING (true);

CREATE POLICY "Allow admin full access on campaign tasks"
    ON campaign_tasks FOR ALL
    USING (true);

-- ========================================
-- 13. منح الصلاحيات للدوال
-- ========================================
GRANT EXECUTE ON FUNCTION verify_youtube_subscription(VARCHAR, UUID, VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION verify_telegram_subscription(VARCHAR, UUID, VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION verify_discord_membership(VARCHAR, UUID, VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION smart_verify_task(VARCHAR, UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION activate_free_subscription(VARCHAR, UUID) TO anon, authenticated;

-- ========================================
-- 14. بيانات تجريبية للاختبار
-- ========================================

-- إنشاء حملة تجريبية
DO $$
DECLARE
    campaign_uuid UUID;
    youtube_task_uuid UUID;
    telegram_task_uuid UUID;
    discord_task_uuid UUID;
BEGIN
    -- إنشاء حملة تجريبية
    INSERT INTO free_subscription_campaigns (
        title_ar, title_en, description_ar, description_en,
        subscription_duration_days, max_users, verification_strictness
    ) VALUES (
        'اشتراك مجاني لمدة 30 يوم',
        '30 Days Free Subscription',
        'احصل على اشتراك مجاني لمدة 30 يوم من خلال إكمال المهام البسيطة التالية',
        'Get a 30-day free subscription by completing the following simple tasks',
        30, 1000, 'medium'
    ) RETURNING id INTO campaign_uuid;

    -- إضافة مهمة يوتيوب
    INSERT INTO campaign_tasks (
        campaign_id, task_type, title_ar, title_en,
        description_ar, description_en,
        target_url, target_id,
        verification_method, display_order
    ) VALUES (
        campaign_uuid, 'youtube_subscribe',
        'اشترك في قناة يوتيوب Mod Etaris',
        'Subscribe to Mod Etaris YouTube Channel',
        'اشترك في قناتنا على يوتيوب للحصول على آخر مودات ماين كرافت',
        'Subscribe to our YouTube channel for the latest Minecraft mods',
        'https://youtube.com/@modetaris',
        'UCxxxxxxxxxxxxxxxxxxxxx', -- معرف القناة الحقيقي
        'smart', 1
    ) RETURNING id INTO youtube_task_uuid;

    -- إضافة مهمة تيليجرام
    INSERT INTO campaign_tasks (
        campaign_id, task_type, title_ar, title_en,
        description_ar, description_en,
        target_url, target_id,
        verification_method, display_order
    ) VALUES (
        campaign_uuid, 'telegram_subscribe',
        'انضم لقناة تيليجرام Mod Etaris',
        'Join Mod Etaris Telegram Channel',
        'انضم لقناتنا على تيليجرام للحصول على التحديثات الفورية',
        'Join our Telegram channel for instant updates',
        'https://t.me/modetaris',
        '@modetaris', -- اسم المستخدم للقناة
        'smart', 2
    ) RETURNING id INTO telegram_task_uuid;

    -- إضافة مهمة ديسكورد
    INSERT INTO campaign_tasks (
        campaign_id, task_type, title_ar, title_en,
        description_ar, description_en,
        target_url, target_id,
        verification_method, display_order
    ) VALUES (
        campaign_uuid, 'discord_join',
        'انضم لخادم ديسكورد Mod Etaris',
        'Join Mod Etaris Discord Server',
        'انضم لخادمنا على ديسكورد للدردشة مع المجتمع',
        'Join our Discord server to chat with the community',
        'https://discord.gg/modetaris',
        '123456789012345678', -- معرف الخادم الحقيقي
        'smart', 3
    ) RETURNING id INTO discord_task_uuid;

    RAISE NOTICE 'تم إنشاء حملة تجريبية بنجاح! Campaign ID: %', campaign_uuid;
    RAISE NOTICE 'YouTube Task ID: %', youtube_task_uuid;
    RAISE NOTICE 'Telegram Task ID: %', telegram_task_uuid;
    RAISE NOTICE 'Discord Task ID: %', discord_task_uuid;
END $$;
