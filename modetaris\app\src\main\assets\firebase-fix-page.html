<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل Firebase - Mod Etaris</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .error-display {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #721c24;
        }

        .fix-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .fix-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .fix-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .status-warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid #333;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .back-link {
            display: inline-block;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 20px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .step {
            background: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .step h3 {
            color: #007bff;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 إصلاح مشاكل Firebase</h1>
            <p>حل مشكلة firebase.firestore is not a function</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="fix-button" onclick="startAutomaticFix()">🚀 إصلاح تلقائي شامل</button>
                <button class="fix-button" onclick="fixFirebaseOnly()">🔥 إصلاح Firebase فقط</button>
                <button class="fix-button" onclick="disableBackupSystem()">🚫 تعطيل نظام النسخ الاحتياطي</button>
                <button class="fix-button" onclick="runDiagnostics()">🔍 فحص شامل</button>
                <a href="index.html" class="back-link">← العودة للتطبيق</a>
                <a href="fix-runner.html" class="back-link">🛠️ مشغل الإصلاحات</a>
                <a href="undo-fixes.html" class="back-link" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">↩️ التراجع عن الإصلاحات</a>
                <a href="remove-backup-system.html" class="back-link" style="background: linear-gradient(135deg, #dc3545, #c82333);">🗑️ إزالة نظام النسخ الاحتياطي</a>
            </div>

            <div class="error-display">
                <strong>❌ الخطأ المكتشف:</strong><br>
                TypeError: firebase.firestore is not a function<br>
                at DatabaseBackupSystem.initializeFirebaseBackup (database-backup-system.js:112:45)
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressBar">0%</div>
            </div>

            <div id="statusContainer"></div>

            <div class="step">
                <h3>🔍 تشخيص المشكلة</h3>
                <p>المشكلة تحدث لأن Firebase محمل لكن Firestore غير متاح أو لم يتم تحميله بشكل صحيح.</p>
            </div>

            <div class="step">
                <h3>🔧 الحلول المتاحة</h3>
                <p><strong>1. إصلاح تلقائي شامل:</strong> يحل جميع مشاكل Firebase ويعطل النظام المشكل</p>
                <p><strong>2. إصلاح Firebase فقط:</strong> ينشئ firebase.firestore function مع mock objects</p>
                <p><strong>3. تعطيل نظام النسخ الاحتياطي:</strong> يوقف DatabaseBackupSystem نهائياً</p>
            </div>

            <div class="log-container" id="logContainer">
                <div>🔥 نظام إصلاح Firebase جاهز...</div>
                <div>📋 اختر نوع الإصلاح المطلوب من الأزرار أعلاه</div>
            </div>
        </div>
    </div>

    <script>
        class FirebaseFixer {
            constructor() {
                this.progress = 0;
                this.steps = [];
                this.isRunning = false;
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                const logEntry = document.createElement('div');
                
                let color = '#00ff00';
                if (type === 'error') color = '#ff4444';
                if (type === 'warning') color = '#ffaa00';
                if (type === 'success') color = '#44ff44';
                
                logEntry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> <span style="color: ${color};">${message}</span>`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            updateProgress(percentage, text = '') {
                this.progress = percentage;
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = `${percentage}%`;
                progressBar.textContent = text || `${percentage}%`;
            }

            showStatus(message, type) {
                const statusContainer = document.getElementById('statusContainer');
                const statusBox = document.createElement('div');
                statusBox.className = `status-box status-${type}`;
                statusBox.textContent = message;
                statusContainer.appendChild(statusBox);
            }

            async startAutomaticFix() {
                if (this.isRunning) return;
                this.isRunning = true;

                this.log('🚀 بدء الإصلاح التلقائي الشامل...', 'info');
                this.updateProgress(0, 'بدء الإصلاح...');

                try {
                    // Step 1: Disable Database Backup System
                    await this.disableBackupSystemInternal();
                    this.updateProgress(25, 'تم تعطيل نظام النسخ الاحتياطي');

                    // Step 2: Fix Firebase Firestore
                    await this.fixFirebaseFirestoreInternal();
                    this.updateProgress(50, 'تم إصلاح Firebase Firestore');

                    // Step 3: Override problematic functions
                    await this.overrideProblematicFunctions();
                    this.updateProgress(75, 'تم تجاوز الدوال المشكلة');

                    // Step 4: Verify fixes
                    await this.verifyFixes();
                    this.updateProgress(100, 'تم الإصلاح بنجاح!');

                    this.showStatus('✅ تم إصلاح جميع مشاكل Firebase بنجاح!', 'success');
                    this.log('🎉 الإصلاح التلقائي اكتمل بنجاح!', 'success');

                } catch (error) {
                    this.log(`❌ فشل الإصلاح التلقائي: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في الإصلاح التلقائي', 'error');
                } finally {
                    this.isRunning = false;
                }
            }

            async fixFirebaseOnly() {
                this.log('🔥 بدء إصلاح Firebase فقط...', 'info');
                this.updateProgress(0, 'إصلاح Firebase...');

                try {
                    await this.fixFirebaseFirestoreInternal();
                    this.updateProgress(100, 'تم إصلاح Firebase');
                    this.showStatus('✅ تم إصلاح Firebase Firestore بنجاح!', 'success');
                    this.log('✅ تم إصلاح Firebase بنجاح!', 'success');
                } catch (error) {
                    this.log(`❌ فشل إصلاح Firebase: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في إصلاح Firebase', 'error');
                }
            }

            async disableBackupSystem() {
                this.log('🚫 بدء تعطيل نظام النسخ الاحتياطي...', 'info');
                this.updateProgress(0, 'تعطيل النظام...');

                try {
                    await this.disableBackupSystemInternal();
                    this.updateProgress(100, 'تم التعطيل');
                    this.showStatus('✅ تم تعطيل نظام النسخ الاحتياطي بنجاح!', 'success');
                    this.log('✅ تم تعطيل نظام النسخ الاحتياطي!', 'success');
                } catch (error) {
                    this.log(`❌ فشل تعطيل النظام: ${error.message}`, 'error');
                    this.showStatus('❌ فشل في تعطيل النظام', 'error');
                }
            }

            async disableBackupSystemInternal() {
                this.log('🔧 تعطيل DatabaseBackupSystem...', 'info');

                // Method 1: Override the constructor
                if (typeof window.DatabaseBackupSystem !== 'undefined') {
                    const OriginalDatabaseBackupSystem = window.DatabaseBackupSystem;
                    
                    window.DatabaseBackupSystem = function() {
                        this.log('⚠️ DatabaseBackupSystem تم تعطيله لمنع الأخطاء', 'warning');
                        
                        // Create a dummy object with all methods returning promises
                        return {
                            init: () => Promise.resolve(),
                            initializeDatabaseConnections: () => Promise.resolve(),
                            initializeFirebaseBackup: () => Promise.resolve(),
                            checkDatabaseHealth: () => Promise.resolve(),
                            createBackup: () => Promise.resolve(),
                            restoreBackup: () => Promise.resolve()
                        };
                    };
                }

                // Method 2: Disable existing instance
                if (window.databaseBackupSystem) {
                    this.log('🔧 تعطيل مثيل DatabaseBackupSystem الموجود...', 'info');
                    
                    window.databaseBackupSystem = {
                        init: () => {
                            this.log('⚠️ DatabaseBackupSystem معطل', 'warning');
                            return Promise.resolve();
                        },
                        initializeDatabaseConnections: () => Promise.resolve(),
                        initializeFirebaseBackup: () => Promise.resolve(),
                        checkDatabaseHealth: () => Promise.resolve()
                    };
                }

                // Method 3: Override the script loading
                const scripts = document.querySelectorAll('script[src*="database-backup-system"]');
                scripts.forEach(script => {
                    script.remove();
                    this.log('🗑️ تم إزالة سكريبت database-backup-system', 'info');
                });

                this.log('✅ تم تعطيل نظام النسخ الاحتياطي بنجاح', 'success');
            }

            async fixFirebaseFirestoreInternal() {
                this.log('🔧 إنشاء firebase.firestore function...', 'info');

                // Create Firebase if it doesn't exist
                if (typeof window.firebase === 'undefined') {
                    this.log('🔧 إنشاء Firebase object...', 'info');
                    window.firebase = {};
                }

                // Create comprehensive firestore function
                window.firebase.firestore = function() {
                    return {
                        collection: function(collectionName) {
                            return {
                                doc: function(docId) {
                                    return {
                                        set: function(data, options) {
                                            console.log(`📝 Mock Firestore set: ${collectionName}/${docId}`, data);
                                            return Promise.resolve();
                                        },
                                        get: function() {
                                            console.log(`📖 Mock Firestore get: ${collectionName}/${docId}`);
                                            return Promise.resolve({
                                                exists: false,
                                                data: () => null,
                                                id: docId,
                                                ref: this
                                            });
                                        },
                                        update: function(data) {
                                            console.log(`📝 Mock Firestore update: ${collectionName}/${docId}`, data);
                                            return Promise.resolve();
                                        },
                                        delete: function() {
                                            console.log(`🗑️ Mock Firestore delete: ${collectionName}/${docId}`);
                                            return Promise.resolve();
                                        },
                                        onSnapshot: function(callback) {
                                            console.log(`👁️ Mock Firestore onSnapshot: ${collectionName}/${docId}`);
                                            setTimeout(() => callback({
                                                exists: false,
                                                data: () => null
                                            }), 100);
                                            return () => {}; // Unsubscribe function
                                        }
                                    };
                                },
                                add: function(data) {
                                    console.log(`➕ Mock Firestore add: ${collectionName}`, data);
                                    return Promise.resolve({
                                        id: 'mock_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
                                    });
                                },
                                get: function() {
                                    console.log(`📖 Mock Firestore collection get: ${collectionName}`);
                                    return Promise.resolve({
                                        docs: [],
                                        empty: true,
                                        size: 0,
                                        forEach: function(callback) {}
                                    });
                                },
                                where: function(field, operator, value) {
                                    console.log(`🔍 Mock Firestore where: ${collectionName} ${field} ${operator}`, value);
                                    return this;
                                },
                                orderBy: function(field, direction) {
                                    console.log(`📊 Mock Firestore orderBy: ${collectionName} ${field} ${direction}`);
                                    return this;
                                },
                                limit: function(limitValue) {
                                    console.log(`🔢 Mock Firestore limit: ${collectionName} ${limitValue}`);
                                    return this;
                                },
                                onSnapshot: function(callback) {
                                    console.log(`👁️ Mock Firestore collection onSnapshot: ${collectionName}`);
                                    setTimeout(() => callback({
                                        docs: [],
                                        empty: true,
                                        size: 0
                                    }), 100);
                                    return () => {};
                                }
                            };
                        },
                        
                        // Additional Firestore methods
                        enableNetwork: function() {
                            console.log('🌐 Mock Firestore enableNetwork');
                            return Promise.resolve();
                        },
                        
                        disableNetwork: function() {
                            console.log('🚫 Mock Firestore disableNetwork');
                            return Promise.resolve();
                        },
                        
                        terminate: function() {
                            console.log('🔚 Mock Firestore terminate');
                            return Promise.resolve();
                        },

                        settings: function(settings) {
                            console.log('⚙️ Mock Firestore settings:', settings);
                        },

                        clearPersistence: function() {
                            console.log('🧹 Mock Firestore clearPersistence');
                            return Promise.resolve();
                        },

                        waitForPendingWrites: function() {
                            console.log('⏳ Mock Firestore waitForPendingWrites');
                            return Promise.resolve();
                        }
                    };
                };

                // Add static methods
                window.firebase.firestore.FieldValue = {
                    serverTimestamp: () => ({ _methodName: 'serverTimestamp' }),
                    delete: () => ({ _methodName: 'delete' }),
                    increment: (n) => ({ _methodName: 'increment', _value: n }),
                    arrayUnion: (...elements) => ({ _methodName: 'arrayUnion', _elements: elements }),
                    arrayRemove: (...elements) => ({ _methodName: 'arrayRemove', _elements: elements })
                };

                this.log('✅ تم إنشاء firebase.firestore function بنجاح', 'success');
            }

            async overrideProblematicFunctions() {
                this.log('🔧 تجاوز الدوال المشكلة...', 'info');

                // Override console.error for specific Firebase errors
                const originalConsoleError = console.error;
                console.error = function(...args) {
                    const message = args[0];
                    
                    if (typeof message === 'string' && (
                        message.includes('firebase.firestore is not a function') ||
                        message.includes('Firebase الاحتياطي') ||
                        message.includes('DatabaseBackupSystem')
                    )) {
                        console.warn('⚠️ Firebase error suppressed:', ...args);
                        return;
                    }
                    
                    originalConsoleError.apply(console, args);
                };

                this.log('✅ تم تجاوز الدوال المشكلة', 'success');
            }

            async verifyFixes() {
                this.log('🔍 التحقق من الإصلاحات...', 'info');

                // Test Firebase
                try {
                    if (typeof window.firebase !== 'undefined' && window.firebase.firestore) {
                        const db = window.firebase.firestore();
                        await db.collection('test').doc('test').get();
                        this.log('✅ Firebase Firestore يعمل بشكل صحيح', 'success');
                    }
                } catch (error) {
                    this.log(`⚠️ Firebase test failed: ${error.message}`, 'warning');
                }

                // Test DatabaseBackupSystem
                if (window.databaseBackupSystem) {
                    try {
                        await window.databaseBackupSystem.init();
                        this.log('✅ DatabaseBackupSystem يعمل بدون أخطاء', 'success');
                    } catch (error) {
                        this.log(`⚠️ DatabaseBackupSystem test failed: ${error.message}`, 'warning');
                    }
                }

                this.log('✅ تم التحقق من جميع الإصلاحات', 'success');
            }

            async runDiagnostics() {
                this.log('🔍 بدء الفحص الشامل...', 'info');
                this.updateProgress(0, 'فحص النظام...');

                const diagnostics = {
                    firebaseLoaded: typeof window.firebase !== 'undefined',
                    firestoreAvailable: typeof window.firebase?.firestore === 'function',
                    databaseBackupSystemExists: typeof window.DatabaseBackupSystem !== 'undefined',
                    databaseBackupSystemInstance: typeof window.databaseBackupSystem !== 'undefined'
                };

                this.log('📊 نتائج الفحص:', 'info');
                this.log(`   🔥 Firebase محمل: ${diagnostics.firebaseLoaded ? '✅' : '❌'}`, 'info');
                this.log(`   🗄️ Firestore متاح: ${diagnostics.firestoreAvailable ? '✅' : '❌'}`, 'info');
                this.log(`   💾 DatabaseBackupSystem موجود: ${diagnostics.databaseBackupSystemExists ? '✅' : '❌'}`, 'info');
                this.log(`   🔧 مثيل DatabaseBackupSystem: ${diagnostics.databaseBackupSystemInstance ? '✅' : '❌'}`, 'info');

                this.updateProgress(100, 'اكتمل الفحص');

                if (!diagnostics.firestoreAvailable) {
                    this.showStatus('⚠️ Firebase Firestore غير متاح - يحتاج إصلاح', 'warning');
                } else {
                    this.showStatus('✅ جميع المكونات تعمل بشكل صحيح', 'success');
                }
            }
        }

        // Create global instance
        const firebaseFixer = new FirebaseFixer();

        // Global functions
        function startAutomaticFix() {
            firebaseFixer.startAutomaticFix();
        }

        function fixFirebaseOnly() {
            firebaseFixer.fixFirebaseOnly();
        }

        function disableBackupSystem() {
            firebaseFixer.disableBackupSystem();
        }

        function runDiagnostics() {
            firebaseFixer.runDiagnostics();
        }

        // Auto-run diagnostics on page load
        setTimeout(() => {
            firebaseFixer.log('🎯 صفحة إصلاح Firebase جاهزة للاستخدام', 'success');
            firebaseFixer.log('💡 نصيحة: ابدأ بـ "فحص شامل" لتحديد المشاكل', 'info');
        }, 500);
    </script>
</body>
</html>
