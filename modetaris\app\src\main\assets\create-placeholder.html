<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Image</title>
</head>
<body>
    <h1>Creating placeholder.png...</h1>
    <canvas id="canvas" width="200" height="200" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <a id="download" download="placeholder.png">Download placeholder.png</a>

    <script>
        // Create placeholder image
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 200, 200);
        gradient.addColorStop(0, '#ffa500');
        gradient.addColorStop(1, '#ff6b00');
        
        // Fill background
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 200, 200);

        // Draw image icon (circle for camera)
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.arc(100, 80, 30, 0, 2 * Math.PI);
        ctx.fill();

        // Draw mountain shape
        ctx.beginPath();
        ctx.moveTo(60, 90);
        ctx.lineTo(140, 90);
        ctx.lineTo(120, 130);
        ctx.lineTo(80, 130);
        ctx.closePath();
        ctx.fill();

        // Add text
        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('No Image', 100, 160);

        // Convert to blob and create download link
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const link = document.getElementById('download');
            link.href = url;
            link.click(); // Auto download
        }, 'image/png');
    </script>
</body>
</html>
