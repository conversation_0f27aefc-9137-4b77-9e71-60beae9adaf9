// DOM Elements
const bannerForm = document.getElementById('banner-form');
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const imageUrlInput = document.getElementById('image-url');
const targetUrlInput = document.getElementById('target-url');
const displayOrderInput = document.getElementById('display-order');
const isActiveInput = document.getElementById('is-active');
const submitButton = document.getElementById('submit-button');
const bannersList = document.getElementById('banners-list');

// Global variables
let editingBannerId = null;

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    // Add event listener for form submission
    bannerForm.addEventListener('submit', handleFormSubmit);
    
    // Load existing banners
    loadBanners();
});

// Load all banners
async function loadBanners() {
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .order('display_order', { ascending: true });
            
        if (error) {
            console.error('Error fetching banners:', error);
            bannersList.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل البانرات</div>';
            return;
        }
        
        if (!data || data.length === 0) {
            bannersList.innerHTML = '<div class="empty-message">لا توجد بانرات إعلانية حالياً</div>';
            return;
        }
        
        renderBanners(data);
    } catch (error) {
        console.error('Unexpected error in loadBanners:', error);
        bannersList.innerHTML = '<div class="error-message">حدث خطأ غير متوقع</div>';
    }
}

// Render banners list
function renderBanners(banners) {
    let html = '<div class="admin-table-container">';
    html += '<table class="admin-table">';
    html += '<thead><tr><th>الصورة</th><th>العنوان</th><th>الوصف</th><th>الترتيب</th><th>الحالة</th><th>الإجراءات</th></tr></thead>';
    html += '<tbody>';
    
    banners.forEach(banner => {
        html += `
            <tr>
                <td><img src="${banner.image_url}" alt="${banner.title || 'Banner'}" style="width: 100px; height: auto;"></td>
                <td>${banner.title || '-'}</td>
                <td>${banner.description || '-'}</td>
                <td>${banner.display_order}</td>
                <td>${banner.is_active ? '<span style="color: #4CAF50;">نشط</span>' : '<span style="color: #f44336;">غير نشط</span>'}</td>
                <td>
                    <button class="button" onclick="editBanner('${banner.id}')">تعديل</button>
                    <button class="button danger" onclick="deleteBanner('${banner.id}')">حذف</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    bannersList.innerHTML = html;
}

// Handle form submission (add/edit banner)
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // Validate form
    if (!imageUrlInput.value.trim()) {
        alert('يجب إدخال رابط الصورة');
        return;
    }
    
    // Disable submit button to prevent multiple submissions
    submitButton.disabled = true;
    submitButton.textContent = 'جاري الحفظ...';
    
    try {
        const bannerData = {
            title: titleInput.value.trim(),
            description: descriptionInput.value.trim(),
            image_url: imageUrlInput.value.trim(),
            target_url: targetUrlInput.value.trim(),
            display_order: parseInt(displayOrderInput.value) || 0,
            is_active: isActiveInput.checked
        };
        
        let result;
        
        if (editingBannerId) {
            // Update existing banner
            result = await supabaseClient
                .from(BANNER_ADS_TABLE)
                .update(bannerData)
                .eq('id', editingBannerId);
                
            if (result.error) {
                throw new Error(result.error.message);
            }
            
            alert('تم تحديث البانر بنجاح');
            editingBannerId = null;
            submitButton.textContent = 'إضافة البانر';
        } else {
            // Add new banner
            result = await supabaseClient
                .from(BANNER_ADS_TABLE)
                .insert([bannerData]);
                
            if (result.error) {
                throw new Error(result.error.message);
            }
            
            alert('تمت إضافة البانر بنجاح');
        }
        
        // Reset form
        bannerForm.reset();
        
        // Reload banners
        loadBanners();
    } catch (error) {
        console.error('Error saving banner:', error);
        alert(`حدث خطأ: ${error.message}`);
    } finally {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.textContent = editingBannerId ? 'تحديث البانر' : 'إضافة البانر';
    }
}

// Edit banner
async function editBanner(id) {
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .eq('id', id)
            .single();
            
        if (error) {
            console.error('Error fetching banner details:', error);
            alert('حدث خطأ أثناء تحميل بيانات البانر');
            return;
        }
        
        // Populate form with banner data
        titleInput.value = data.title || '';
        descriptionInput.value = data.description || '';
        imageUrlInput.value = data.image_url || '';
        targetUrlInput.value = data.target_url || '';
        displayOrderInput.value = data.display_order || 0;
        isActiveInput.checked = data.is_active;
        
        // Update form state
        editingBannerId = id;
        submitButton.textContent = 'تحديث البانر';
        
        // Scroll to form
        bannerForm.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('Unexpected error in editBanner:', error);
        alert('حدث خطأ غير متوقع');
    }
}

// Delete banner
async function deleteBanner(id) {
    if (!confirm('هل أنت متأكد من حذف هذا البانر؟')) {
        return;
    }
    
    try {
        const { error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .delete()
            .eq('id', id);
            
        if (error) {
            console.error('Error deleting banner:', error);
            alert('حدث خطأ أثناء حذف البانر');
            return;
        }
        
        // If we were editing this banner, reset the form
        if (editingBannerId === id) {
            bannerForm.reset();
            editingBannerId = null;
            submitButton.textContent = 'إضافة البانر';
        }
        
        // Reload banners
        loadBanners();
        
        alert('تم حذف البانر بنجاح');
    } catch (error) {
        console.error('Unexpected error in deleteBanner:', error);
        alert('حدث خطأ غير متوقع');
    }
}
