<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft Mods - Language Selection</title>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Press Start 2P', monospace;
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #0a0a0a 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
            position: relative;
            opacity: 0; /* Initial state for fade-in */
            animation: fadeIn 1s ease-in-out forwards; /* Apply fade-in animation */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Animated background particles */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ff8c00;
            animation: float 6s infinite ease-in-out;
        }

        .particle:nth-child(odd) {
            background: #ffd700;
            animation-delay: -2s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        .container {
            background: rgba(42, 42, 42, 0.9);
            border: 4px solid #ffd700;
            border-radius: 0;
            padding: 40px;
            text-align: center;
            box-shadow: 
                0 0 20px rgba(255, 215, 0, 0.5),
                inset 0 0 20px rgba(255, 140, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            max-width: 500px;
            width: 90%;
        }

        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ff8c00, #ffd700, #ff8c00);
            z-index: -1;
            animation: borderGlow 2s linear infinite;
        }

        @keyframes borderGlow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .app-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .app-icon {
            width: 80px;
            height: 80px;
            border: 3px solid #ffd700;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
            animation: iconGlow 2s infinite ease-in-out;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .app-logo {
            max-width: 300px;
            height: auto;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
            animation: pulse 2s infinite ease-in-out;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        @keyframes iconGlow {
            0%, 100% { 
                box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
                border-color: #ffd700;
            }
            50% { 
                box-shadow: 0 0 25px rgba(255, 140, 0, 0.8);
                border-color: #ff8c00;
            }
        }

        .title {
            font-size: 18px;
            margin-bottom: 30px;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            animation: pulse 2s infinite ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .language-options {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 30px;
        }

        .language-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
            border: 3px solid #ff8c00;
            color: white;
            font-family: 'Press Start 2P', monospace;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .language-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
            transition: left 0.5s;
        }

        .language-btn:hover::before {
            left: 100%;
        }

        .language-btn:hover {
            border-color: #ffd700;
            background: linear-gradient(135deg, #4a4a4a 0%, #3a3a3a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(255, 140, 0, 0.3);
        }

        .language-btn:active {
            transform: translateY(0px);
            box-shadow: 0 4px 8px rgba(255, 140, 0, 0.2);
        }

        .flag-icon {
            width: 32px;
            height: 32px;
            border: 2px solid #ffd700;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: rgba(255, 215, 0, 0.1);
        }

        .arabic-flag {
            background: linear-gradient(to bottom, #ce1126 33%, #ffffff 33%, #ffffff 66%, #000000 66%);
            color: transparent;
        }

        .english-flag {
            background: 
                linear-gradient(45deg, #012169 25%, transparent 25%),
                linear-gradient(-45deg, #012169 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #012169 75%),
                linear-gradient(-45deg, transparent 75%, #012169 75%),
                linear-gradient(to bottom, #ce1126 40%, #ffffff 40%, #ffffff 60%, #ce1126 60%);
            background-size: 8px 8px, 8px 8px, 8px 8px, 8px 8px, 100% 100%;
            color: transparent;
        }

        .subtitle {
            font-size: 10px;
            color: white;
            margin-bottom: 20px;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        /* Pixel-style decorations */
        .decoration {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ffd700;
        }

        .decoration.top-left {
            top: 10px;
            left: 10px;
            clip-path: polygon(0 0, 100% 0, 100% 50%, 50% 50%, 50% 100%, 0 100%);
        }

        .decoration.top-right {
            top: 10px;
            right: 10px;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 50% 100%, 50% 50%, 0 50%);
        }

        .decoration.bottom-left {
            bottom: 10px;
            left: 10px;
            clip-path: polygon(0 0, 50% 0, 50% 50%, 100% 50%, 100% 100%, 0 100%);
        }

        .decoration.bottom-right {
            bottom: 10px;
            right: 10px;
            clip-path: polygon(50% 0, 100% 0, 100% 100%, 0 100%, 0 50%, 50% 50%);
        }

        @media (max-width: 600px) {
            .app-icon {
                width: 60px;
                height: 60px;
            }
            
            .app-logo {
                max-width: 250px;
            }
            
            .container {
                padding: 30px 20px;
                width: 95%; /* زيادة العرض على الأجهزة المحمولة */
                border-width: 3px; /* تقليل سمك الحدود */
            }
            
            .title {
                font-size: 14px;
            }
            
            .language-btn {
                font-size: 12px;
                padding: 15px;
            }
            
            .flag-icon {
                width: 28px;
                height: 28px;
            }
            
            /* تحسين الأداء على الأجهزة الضعيفة */
            .particle {
                display: none; /* إخفاء الجسيمات على الأجهزة المحمولة لتحسين الأداء */
            }
        }
    </style>
</head>
<body class="fade-in">
    <div class="particles"></div>
    
    <div class="container">
        <div class="decoration top-left"></div>
        <div class="decoration top-right"></div>
        <div class="decoration bottom-left"></div>
        <div class="decoration bottom-right"></div>
        
        <div class="app-header">
            <img id="app-logo" class="app-logo" src="image/app_name.png" alt="App Name">
        </div>
        <p class="subtitle">Choose Your Language / اختر لغتك</p>
        
        <div class="language-options">
            <button class="language-btn" onclick="selectLanguage('ar', event)">
                <div class="flag-icon arabic-flag">🏴</div>
                <span>العربية</span>
            </button>
            
            <button class="language-btn" onclick="selectLanguage('en', event)">
                <div class="flag-icon english-flag">🏴</div>
                <span>English</span>
            </button>
        </div>
    </div>

    <script>
        // Create floating particles
        function createParticles() {
            const particlesContainer = document.querySelector('.particles');
            const particleCount = 20;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Language selection function
        function selectLanguage(lang, event) {
            const buttons = document.querySelectorAll('.language-btn');
            
            // Disable all buttons to prevent multiple clicks
            buttons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.7';
                btn.style.cursor = 'wait';
            });
            
            // Get the button element safely
            const button = event ? event.target.closest('.language-btn') : null;
            
            // Add click effect if button exists
            if (button) {
                button.style.transform = 'scale(0.95)';
            }
            
          // Set language selected flag and selected language in all storage methods
            // to ensure it works across different browsers and conditions
            try {  
                // Try localStorage first
                localStorage.setItem('languageSelected', 'true');
                localStorage.setItem('selectedLanguage', lang);
                console.log('Language selection saved to localStorage');
                
                // Also save to sessionStorage as backup
                sessionStorage.setItem('languageSelected', 'true');
                sessionStorage.setItem('selectedLanguage', lang);
                
                // Set cookies as additional fallback
                document.cookie = "languageSelected=true; path=/; max-age=31536000"; // 1 year
                document.cookie = "selectedLanguage=" + lang + "; path=/; max-age=31536000";
            } catch (error) {
                console.error('Error saving language selection:', error);
                // If localStorage fails, try sessionStorage
                try {
                    sessionStorage.setItem('languageSelected', 'true');
                    sessionStorage.setItem('selectedLanguage', lang);
                    console.log('Language selection saved to sessionStorage as fallback');
                } catch (sessionError) {
                    console.error('Error saving to sessionStorage:', sessionError);
                }
            }
            
            // Show visual feedback instead of alert (alerts can be problematic on mobile)
            const container = document.querySelector('.container');
            const feedbackMsg = document.createElement('div');
            feedbackMsg.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 16px;
                z-index: 100;
                animation: fadeIn 0.3s ease;
            `;
            
            feedbackMsg.innerHTML = lang === 'ar' ? 
                '<div style="text-align: center; padding: 20px;">تم اختيار اللغة العربية!<br>جاري تحميل التطبيق...</div>' : 
                '<div style="text-align: center; padding: 20px;">English language selected!<br>Loading application...</div>';
            
            container.appendChild(feedbackMsg);
            
            // Redirect to index.html with language parameter after a short delay
            setTimeout(() => {
                window.location.href = 'index.html?lang=' + lang;
            }, 800);
        }

        // Sound effects simulation (visual feedback)
        function addSoundEffect(element) {
            element.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.8)';
            setTimeout(() => {
                element.style.boxShadow = '';
            }, 200);
        }

        // Add hover sound effects
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => addSoundEffect(btn));
        });

        // Initialize particles when page loads
        window.addEventListener('load', () => {
            createParticles();
            // Remove fade-in class after animation to prevent re-triggering on subsequent loads/redirects
            document.body.classList.remove('fade-in');
        });

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            const buttons = document.querySelectorAll('.language-btn');
            if (e.key === '1') {
                buttons[0].click();
            } else if (e.key === '2') {
                buttons[1].click();
            }
        });
    </script>
</body>
</html>
