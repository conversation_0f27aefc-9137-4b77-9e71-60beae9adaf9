// Subscription Page JavaScript
// صفحة الاشتراك المجاني المخصصة

// Global variables
let supabaseClient; // Will be initialized from SupabaseManager
let currentCampaign = null;
let campaignTasks = [];
let userProgress = {};
let userId = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Subscription page loaded');
    initializePage();
    createFloatingParticles();
});

// Initialize Supabase and load campaign data
async function initializePage() {
    try {
        // Initialize Supabase client using SupabaseManager
        if (typeof supabaseManager !== 'undefined') {
            supabaseClient = supabaseManager.getMainClient();
        } else {
            console.error('SupabaseManager is not defined. Ensure supabase-manager.js is loaded before subscription-page.js');
            // Fallback to direct initialization if SupabaseManager is not available (should not happen in production)
            const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
            supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        }

        // Generate or get user ID
        userId = generateUserId();

        // Get campaign ID from URL parameters or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const campaignId = urlParams.get('campaign') || localStorage.getItem('currentCampaignId');

        if (campaignId) {
            await loadCampaignData(campaignId);
        } else {
            // Load the first active campaign
            await loadFirstActiveCampaign();
        }

    } catch (error) {
        console.error('Error initializing subscription page:', error);
        showError('حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.');
    }
}

// Generate or get user ID
function generateUserId() {
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('userId', userId);
    }
    return userId;
}

// Load campaign data
async function loadCampaignData(campaignId) {
    try {
        // Fetch campaign details
        const { data: campaign, error: campaignError } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('id', campaignId)
            .eq('is_active', true)
            .single();

        if (campaignError || !campaign) {
            console.error('Error fetching campaign:', campaignError);
            showError('لم يتم العثور على حملة اشتراك نشطة.');
            return;
        }

        currentCampaign = campaign;

        // Update UI with campaign data
        updateCampaignUI();

        // Load campaign tasks
        await loadCampaignTasks(campaignId);

        // Load user progress
        await loadUserProgress(campaignId);

    } catch (error) {
        console.error('Error loading campaign data:', error);
        showError('حدث خطأ أثناء تحميل بيانات الحملة.');
    }
}

// Load first active campaign
async function loadFirstActiveCampaign() {
    try {
        const { data: campaigns, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('*')
            .eq('is_active', true)
            .order('created_at', { ascending: false })
            .limit(1);

        if (error || !campaigns || campaigns.length === 0) {
            console.error('No active campaigns found:', error);
            showError('لا توجد حملات اشتراك نشطة حالياً.');
            return;
        }

        await loadCampaignData(campaigns[0].id);

    } catch (error) {
        console.error('Error loading first active campaign:', error);
        showError('حدث خطأ أثناء تحميل الحملة.');
    }
}

// Update campaign UI
function updateCampaignUI() {
    if (!currentCampaign) return;

    const userLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    const isArabic = userLanguage === 'ar';

    // Update title and description
    const title = isArabic ? currentCampaign.title_ar : currentCampaign.title_en;
    const description = isArabic ? currentCampaign.description_ar : currentCampaign.description_en;

    document.getElementById('campaignTitle').textContent = title;
    document.getElementById('campaignDescription').textContent = description;
    document.getElementById('subscriptionDuration').textContent = currentCampaign.subscription_duration_days;
}

// Load campaign tasks
async function loadCampaignTasks(campaignId) {
    try {
        const { data: tasks, error } = await supabaseClient
            .from('campaign_tasks')
            .select(`
                *,
                task_types (
                    name,
                    display_name_ar,
                    display_name_en,
                    icon
                )
            `)
            .eq('campaign_id', campaignId)
            .eq('is_required', true) /* Changed from is_active to is_required */
            .order('task_order', { ascending: true });

        if (error) {
            console.error('Error fetching tasks:', error);
            showError('حدث خطأ أثناء تحميل المهام.');
            return;
        }

        campaignTasks = tasks || [];
        renderTasks();

    } catch (error) {
        console.error('Error loading campaign tasks:', error);
        showError('حدث خطأ أثناء تحميل المهام.');
    }
}

// Load user progress
async function loadUserProgress(campaignId) {
    try {
        const { data: progress, error } = await supabaseClient
            .from('user_task_progress')
            .select('*')
            .eq('user_id', userId)
            .eq('campaign_id', campaignId);

        if (error) {
            console.error('Error fetching user progress:', error);
            return;
        }

        // Convert progress array to object for easier access
        userProgress = {};
        if (progress) {
            progress.forEach(p => {
                userProgress[p.task_id] = p;
            });
        }

        updateProgressUI();

    } catch (error) {
        console.error('Error loading user progress:', error);
    }
}

// Render tasks
function renderTasks() {
    const container = document.getElementById('tasksContainer');
    if (!container || !campaignTasks.length) {
        container.innerHTML = '<div class="error-message">لا توجد مهام متاحة حالياً.</div>';
        return;
    }

    const userLanguage = localStorage.getItem('selectedLanguage') || 'ar';
    const isArabic = userLanguage === 'ar';

    let tasksHtml = '';

    campaignTasks.forEach(task => {
        const taskType = task.task_types;
        const taskName = isArabic ? taskType.display_name_ar : taskType.display_name_en;
        const taskDescription = isArabic ? task.description_ar : task.description_en;
        const progress = userProgress[task.id];
        const isCompleted = progress && progress.status === 'completed';

        tasksHtml += `
            <div class="task-card" data-task-id="${task.id}">
                <div class="task-header">
                    <div class="task-info">
                        <div class="task-icon">
                            <i class="${taskType.icon}"></i>
                        </div>
                        <div class="task-details">
                            <h3>${taskName}</h3>
                            <p>${taskDescription}</p>
                        </div>
                    </div>
                    <div class="task-status ${isCompleted ? 'status-completed' : 'status-pending'}">
                        ${isCompleted ? '✓ مكتملة' : 'في الانتظار'}
                    </div>
                </div>
                <div class="task-action">
                    <button class="task-btn" 
                            onclick="handleTaskClick('${task.id}')" 
                            ${isCompleted ? 'disabled' : ''}>
                        ${isCompleted ? 'تم الإكمال' : 'ابدأ المهمة'}
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = tasksHtml;
    updateProgressUI();
}

// Handle task click
async function handleTaskClick(taskId) {
    try {
        const task = campaignTasks.find(t => t.id === taskId);
        if (!task) return;

        // Check if task is already completed
        const progress = userProgress[taskId];
        if (progress && progress.status === 'completed') {
            showMessage('هذه المهمة مكتملة بالفعل!', 'info');
            return;
        }

        // Open task URL if available
        if (task.task_url) {
            window.open(task.task_url, '_blank');
        }

        // Use smart verification system if available
        if (window.smartVerificationSystem) {
            const result = await window.smartVerificationSystem.handleTaskClick(taskId, task);
            
            if (result.success) {
                await updateTaskProgress(taskId, 'completed', result.verificationScore);
                showMessage('تم إكمال المهمة بنجاح!', 'success');
            } else {
                showMessage('لم يتم التحقق من إكمال المهمة. يرجى المحاولة مرة أخرى.', 'warning');
            }
        } else {
            // Fallback: Mark as completed after delay
            setTimeout(async () => {
                await updateTaskProgress(taskId, 'completed', 100);
                showMessage('تم إكمال المهمة!', 'success');
            }, 30000); // 30 seconds delay
        }

    } catch (error) {
        console.error('Error handling task click:', error);
        showMessage('حدث خطأ أثناء معالجة المهمة.', 'error');
    }
}

// Update task progress
async function updateTaskProgress(taskId, status, verificationScore = null) {
    try {
        const progressData = {
            user_id: userId,
            campaign_id: currentCampaign.id,
            task_id: taskId,
            status: status,
            verification_score: verificationScore,
            completed_at: status === 'completed' ? new Date().toISOString() : null,
            updated_at: new Date().toISOString()
        };

        const { error } = await supabaseClient
            .from('user_task_progress')
            .upsert([progressData], {
                onConflict: 'user_id,campaign_id,task_id'
            });

        if (error) {
            console.error('Error updating task progress:', error);
            return false;
        }

        // Update local progress
        userProgress[taskId] = progressData;
        
        // Re-render tasks and update progress
        renderTasks();
        updateProgressUI();

        return true;

    } catch (error) {
        console.error('Error updating task progress:', error);
        return false;
    }
}

// Update progress UI
function updateProgressUI() {
    const totalTasks = campaignTasks.length;
    const completedTasks = Object.values(userProgress).filter(p => p.status === 'completed').length;
    const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // Update progress bar
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    if (progressFill) {
        progressFill.style.width = `${progressPercentage}%`;
    }
    
    if (progressText) {
        progressText.textContent = `${completedTasks} من ${totalTasks} مهام مكتملة`;
    }

    // Update activate button
    const activateBtn = document.getElementById('activateBtn');
    if (activateBtn) {
        activateBtn.disabled = completedTasks < totalTasks;
        
        if (completedTasks >= totalTasks) {
            activateBtn.innerHTML = '<i class="fas fa-rocket"></i> تفعيل الاشتراك المجاني';
        } else {
            activateBtn.innerHTML = `<i class="fas fa-tasks"></i> أكمل ${totalTasks - completedTasks} مهام أخرى`;
        }
    }
}

// Activate subscription
async function activateSubscription() {
    try {
        const totalTasks = campaignTasks.length;
        const completedTasks = Object.values(userProgress).filter(p => p.status === 'completed').length;

        if (completedTasks < totalTasks) {
            showMessage('يجب إكمال جميع المهام أولاً!', 'warning');
            return;
        }

        // Check if user already has an active subscription
        const { data: existingSubscription } = await supabaseClient
            .from('user_subscriptions')
            .select('*')
            .eq('user_id', userId)
            .eq('campaign_id', currentCampaign.id)
            .eq('status', 'active')
            .single();

        if (existingSubscription) {
            showMessage('لديك اشتراك مجاني نشط بالفعل!', 'info');
            return;
        }

        // Create new subscription
        const subscriptionData = {
            user_id: userId,
            campaign_id: currentCampaign.id,
            status: 'active',
            started_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + (currentCampaign.subscription_duration_days * 24 * 60 * 60 * 1000)).toISOString(),
            created_at: new Date().toISOString()
        };

        const { error } = await supabaseClient
            .from('user_subscriptions')
            .insert([subscriptionData]);

        if (error) {
            console.error('Error creating subscription:', error);
            showMessage('حدث خطأ أثناء تفعيل الاشتراك. يرجى المحاولة مرة أخرى.', 'error');
            return;
        }

        // Show success message
        showMessage('تهانينا! تم تفعيل اشتراكك المجاني بنجاح!', 'success');
        
        // Update button
        const activateBtn = document.getElementById('activateBtn');
        if (activateBtn) {
            activateBtn.innerHTML = '<i class="fas fa-check"></i> تم التفعيل بنجاح!';
            activateBtn.disabled = true;
            activateBtn.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
        }

        // Redirect to main app after delay
        setTimeout(() => {
            goBack();
        }, 3000);

    } catch (error) {
        console.error('Error activating subscription:', error);
        showMessage('حدث خطأ أثناء تفعيل الاشتراك.', 'error');
    }
}

// Go back to main app
function goBack() {
    // Check if there's a pending download to process
    const urlParams = new URLSearchParams(window.location.search);
    const returnType = urlParams.get('return');

    if (returnType === 'download') {
        // Check if user now has active subscription
        checkActiveSubscription().then(hasSubscription => {
            if (hasSubscription) {
                // Process pending download
                processPendingDownload();
            } else {
                // Go back normally
                navigateBack();
            }
        });
    } else {
        navigateBack();
    }
}

// Navigate back to main app
function navigateBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = 'index.html';
    }
}

// Process pending download after subscription
function processPendingDownload() {
    try {
        const pendingDownload = localStorage.getItem('pendingDownload');

        if (pendingDownload) {
            const downloadInfo = JSON.parse(pendingDownload);

            // Clear pending download
            localStorage.removeItem('pendingDownload');

            // Show success message
            showMessage('تم تفعيل اشتراكك! سيتم بدء التحميل الآن...', 'success');

            // Redirect back to main app with download info
            setTimeout(() => {
                window.location.href = `index.html?autoDownload=${downloadInfo.modId}`;
            }, 2000);
        } else {
            navigateBack();
        }
    } catch (error) {
        console.error('Error processing pending download:', error);
        navigateBack();
    }
}

// Check if user has active subscription
async function checkActiveSubscription() {
    try {
        const { data: subscription, error } = await supabaseClient
            .from('user_subscriptions')
            .select('*')
            .eq('user_id', userId)
            .eq('status', 'active')
            .gte('expires_at', new Date().toISOString())
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error('Error checking subscription:', error);
            return false;
        }

        return !!subscription;
    } catch (error) {
        console.error('Error in checkActiveSubscription:', error);
        return false;
    }
}

// Show message
function showMessage(message, type = 'info') {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.success-message, .error-message, .info-message, .warning-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `${type}-message`;
    messageDiv.textContent = message;

    // Add appropriate styling based on type
    if (type === 'error') {
        messageDiv.style.cssText = `
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
            padding: 15px;
            border-radius: 10px;
            margin: 20px auto;
            text-align: center;
            max-width: 600px;
        `;
    } else if (type === 'success') {
        messageDiv.style.cssText = `
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #86efac;
            padding: 15px;
            border-radius: 10px;
            margin: 20px auto;
            text-align: center;
            max-width: 600px;
        `;
    } else if (type === 'warning') {
        messageDiv.style.cssText = `
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffd43b;
            padding: 15px;
            border-radius: 10px;
            margin: 20px auto;
            text-align: center;
            max-width: 600px;
        `;
    } else {
        messageDiv.style.cssText = `
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid #3b82f6;
            color: #93c5fd;
            padding: 15px;
            border-radius: 10px;
            margin: 20px auto;
            text-align: center;
            max-width: 600px;
        `;
    }

    // Insert at the top of the container
    const container = document.querySelector('.container');
    container.insertBefore(messageDiv, container.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}

// Show error
function showError(message) {
    showMessage(message, 'error');
}

// Create floating particles animation
function createFloatingParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;

    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every interval
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';
            
            particlesContainer.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 8000);
        }
    }, 500);
}
