-- SQL script to create user language preferences table
-- This table stores each user's selected language preference

-- Create user_languages table (updated name and structure)
CREATE TABLE IF NOT EXISTS user_languages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL UNIQUE,
    selected_language VARCHAR(10) NOT NULL DEFAULT 'en' CHECK (selected_language IN ('ar', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    device_info JSONB DEFAULT '{}',
    user_agent TEXT
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_languages_language
ON user_languages(selected_language);

CREATE INDEX IF NOT EXISTS idx_user_languages_created_at
ON user_languages(created_at);

CREATE INDEX IF NOT EXISTS idx_user_languages_updated_at
ON user_languages(updated_at);

-- Add comments for documentation
COMMENT ON TABLE user_languages IS 'Stores user language preferences';
COMMENT ON COLUMN user_languages.user_id IS 'Unique identifier for the user (generated client-side)';
COMMENT ON COLUMN user_languages.selected_language IS 'Language code: ar for Arabic, en for English';
COMMENT ON COLUMN user_languages.created_at IS 'When the user first selected their language';
COMMENT ON COLUMN user_languages.updated_at IS 'When the user last updated their language';
COMMENT ON COLUMN user_languages.user_agent IS 'Browser user agent string';
COMMENT ON COLUMN user_languages.device_info IS 'Device information in JSON format';

-- Create a view for language statistics
CREATE OR REPLACE VIEW language_usage_stats AS
SELECT
    selected_language,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage,
    MIN(created_at) as first_user,
    MAX(updated_at) as latest_update
FROM user_languages
GROUP BY selected_language
ORDER BY user_count DESC;

-- Create a view for daily language selections
CREATE OR REPLACE VIEW daily_language_selections AS
SELECT
    DATE(created_at) as selection_date,
    selected_language,
    COUNT(*) as new_users_count
FROM user_languages
GROUP BY DATE(created_at), selected_language
ORDER BY selection_date DESC, selected_language;

-- Create a view for monthly language trends
CREATE OR REPLACE VIEW monthly_language_trends AS
SELECT
    DATE_TRUNC('month', created_at) as month,
    selected_language,
    COUNT(*) as new_users_count,
    COUNT(DISTINCT user_id) as unique_users
FROM user_languages
GROUP BY DATE_TRUNC('month', created_at), selected_language
ORDER BY month DESC, selected_language;

-- Function to get language statistics
CREATE OR REPLACE FUNCTION get_language_statistics()
RETURNS TABLE (
    language VARCHAR(10),
    total_users BIGINT,
    percentage NUMERIC,
    recent_updates_7d BIGINT,
    recent_updates_30d BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ul.selected_language as language,
        COUNT(*) as total_users,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage,
        COUNT(*) FILTER (WHERE ul.updated_at >= NOW() - INTERVAL '7 days') as recent_updates_7d,
        COUNT(*) FILTER (WHERE ul.updated_at >= NOW() - INTERVAL '30 days') as recent_updates_30d
    FROM user_languages ul
    GROUP BY ul.selected_language
    ORDER BY total_users DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to update user language preference (upsert)
CREATE OR REPLACE FUNCTION save_user_language(
    p_user_id VARCHAR(255),
    p_language VARCHAR(10),
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT '{}'
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Validate language code
    IF p_language NOT IN ('ar', 'en') THEN
        RAISE EXCEPTION 'Invalid language code. Must be ar or en.';
    END IF;

    -- Insert or update user language preference
    INSERT INTO user_languages (
        user_id,
        selected_language,
        user_agent,
        device_info,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        p_language,
        p_user_agent,
        p_device_info,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id)
    DO UPDATE SET
        selected_language = EXCLUDED.selected_language,
        updated_at = CURRENT_TIMESTAMP,
        user_agent = COALESCE(EXCLUDED.user_agent, user_languages.user_agent),
        device_info = EXCLUDED.device_info;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create RLS (Row Level Security) policies if needed
-- ALTER TABLE user_languages ENABLE ROW LEVEL SECURITY;

-- Sample data for testing (optional - remove in production)
-- INSERT INTO user_languages (user_id, selected_language) VALUES
-- ('test_user_1', 'ar'),
-- ('test_user_2', 'en'),
-- ('test_user_3', 'ar');

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON user_languages TO your_app_user;
-- GRANT SELECT ON language_usage_stats TO your_app_user;
-- GRANT SELECT ON daily_language_selections TO your_app_user;
-- GRANT SELECT ON monthly_language_trends TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_language_statistics() TO your_app_user;
-- GRANT EXECUTE ON FUNCTION save_user_language(VARCHAR, VARCHAR, TEXT, JSONB) TO your_app_user;
