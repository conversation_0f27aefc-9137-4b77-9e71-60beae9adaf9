# 🎛️ لوحة الإدارة الموحدة - Mod Etaris

## 📋 نظرة عامة

لوحة إدارة شاملة وموحدة لتطبيق Mod Etaris تتيح إدارة جميع جوانب التطبيق من مكان واحد بواجهة سهلة ومتطورة.

## ✨ الميزات الرئيسية

### 🎯 لوحة المعلومات الذكية
- **إحصائيات مباشرة**: عرض إجمالي المودات، المستخدمين النشطين، والتحميلات
- **النشاط الأخير**: متابعة آخر الأنشطة في التطبيق
- **صحة النظام**: مراقبة حالة قاعدة البيانات والتخزين والاتصال
- **إجراءات سريعة**: أزرار سريعة للمهام الشائعة

### 🎮 إدارة المودات المتقدمة
- **المودات المميزة**: إدارة المودات التي تظهر بتأثيرات خاصة
- **الإضافات المجانية**: إدارة قسم الإضافات المجانية مع التأثيرات البصرية
- **المودات المقترحة**: تحديد المودات المقترحة للمستخدمين
- **الأقسام المخصصة**: إنشاء وإدارة أقسام مخصصة للمودات

### 🖼️ نظام البانرات المطور
- **بانر عادي**: بانرات توجه لروابط خارجية
- **بانر مود**: بانرات تعرض تفاصيل مود محدد عند النقر ⭐ **جديد**
- **بانر اشتراك**: بانرات لحملات الاشتراك المجاني
- **معاينة مباشرة**: معاينة البانرات قبل النشر
- **إدارة متقدمة**: تعديل، نسخ، حذف، وتفعيل/إلغاء تفعيل البانرات

### 👑 نظام الاشتراكات المجانية
- **إنشاء الحملات**: إنشاء حملات اشتراك مجاني مخصصة
- **إدارة المهام**: إضافة وإدارة المهام التفاعلية
- **إحصائيات مفصلة**: متابعة الحملات النشطة والمشتركين
- **التحقق الذكي**: نظام تحقق متطور للمهام

### 💬 المربعات المخصصة
- **مربعات الإشعار**: إنشاء مربعات إشعار مخصصة للمودات
- **مربعات حقوق الطبع**: مربعات خاصة بحقوق الطبع والنشر
- **دعم ثنائي اللغة**: دعم العربية والإنجليزية

### ⚙️ إعدادات النظام
- **مراقبة قاعدة البيانات**: فحص واختبار اتصال قاعدة البيانات
- **إدارة التخزين**: مراقبة استخدام مساحة التخزين
- **أدوات الصيانة**: تنظيف البيانات وتحسين الأداء

## 🚀 كيفية الاستخدام

### الوصول للوحة الإدارة
1. افتح ملف `admin/index.html` في المتصفح
2. أو ادخل على الرابط: `your-domain.com/admin/`

### إنشاء بانر مود جديد ⭐
1. انتقل إلى تبويب "البانرات الإعلانية"
2. انقر على "بانر مود"
3. املأ المعلومات الأساسية (العنوان، الوصف، الترتيب)
4. ارفع صورة البانر (الحد الأقصى: 2MB)
5. اختر المود المستهدف من القائمة المنسدلة
6. معاينة البانر قبل الحفظ
7. احفظ البانر

### إدارة البانرات الموجودة
- **التعديل**: انقر على أيقونة القلم لتعديل البانر
- **النسخ**: انقر على أيقونة النسخ لإنشاء نسخة من البانر
- **الحذف**: انقر على أيقونة سلة المهملات لحذف البانر
- **التفعيل/الإلغاء**: انقر على أيقونة الطاقة لتغيير حالة البانر

### إنشاء حملة اشتراك سريعة
1. انتقل إلى تبويب "الاشتراكات المجانية"
2. انقر على "إنشاء حملة جديدة"
3. املأ تفاصيل الحملة (العنوان، الوصف، المدة)
4. أضف المهام المطلوبة
5. احفظ الحملة

## 🔧 الإعداد الأولي

### 1. إعداد قاعدة البيانات
قم بتشغيل ملف SQL التالي في Supabase:
```sql
-- انسخ محتوى ملف database/update_banner_system.sql
```

### 2. إعداد التخزين
1. أنشئ bucket جديد في Supabase Storage باسم `banner-images`
2. اجعل الـ bucket عام (Public)
3. أضف سياسة للقراءة العامة

### 3. تحديث إعدادات Supabase
تأكد من صحة معلومات Supabase في ملف `config.js`:
```javascript
const SUPABASE_URL = 'your-supabase-url';
const SUPABASE_ANON_KEY = 'your-supabase-anon-key';
```

## 📁 هيكل الملفات الجديد

```
admin/
├── index.html                      # لوحة الإدارة الموحدة الرئيسية
├── unified-admin-style.css         # تصميم موحد لجميع الصفحات
├── unified-admin.js                # وظائف لوحة الإدارة الرئيسية
├── unified-banner-manager.html     # إدارة البانرات الموحدة ⭐ جديد
├── unified-banner-manager.js       # وظائف إدارة البانرات ⭐ جديد
├── config.js                       # إعدادات Supabase
├── featured_addons.html            # إدارة المودات المميزة
├── free_addons.html               # إدارة الإضافات المجانية
├── custom_dialogs.html            # إدارة المربعات المخصصة
├── custom_copyright.html          # إدارة حقوق الطبع
├── subscription_admin.html        # إدارة الاشتراكات
├── easy_campaign_creator.html     # إنشاء الحملات السريعة
└── README.md                      # هذا الملف
```

## 🆕 الميزات الجديدة

### بانرات المودات ⭐
- **ربط مباشر**: ربط البانر بمود محدد من قاعدة البيانات
- **عرض تلقائي**: عرض تفاصيل المود عند النقر على البانر
- **معاينة المود**: معاينة المود المختار أثناء إنشاء البانر
- **تحديث قاعدة البيانات**: دعم حقول جديدة في جدول `banner_ads`

### واجهة موحدة
- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات
- **ألوان متطابقة**: ألوان ذهبية متطابقة مع التطبيق
- **تأثيرات بصرية**: انيميشن وتأثيرات جميلة
- **تنقل سهل**: تبويبات منظمة لسهولة الوصول

### إحصائيات متقدمة
- **مراقبة مباشرة**: إحصائيات محدثة في الوقت الفعلي
- **صحة النظام**: مراقبة حالة جميع مكونات النظام
- **تقارير مفصلة**: تقارير شاملة عن الاستخدام والأداء

## 🔍 استكشاف الأخطاء

### مشكلة عدم ظهور المودات في قائمة البانر
**الحل**: تأكد من وجود مودات في جدول `mods` وصحة الاتصال بقاعدة البيانات

### مشكلة عدم حفظ البانر
**الحل**: 
1. تأكد من رفع صورة صالحة (أقل من 2MB)
2. تأكد من إنشاء bucket `banner-images` في Supabase Storage
3. تأكد من صحة إعدادات Supabase

### مشكلة عدم ظهور البانر في التطبيق
**الحل**:
1. تأكد من تفعيل البانر (is_active = true)
2. تأكد من تشغيل ملف SQL لتحديث قاعدة البيانات
3. تحقق من ترتيب العرض (display_order)

## 📊 قاعدة البيانات

### جدول banner_ads المحدث
```sql
- id: معرف البانر
- title: عنوان البانر
- description: وصف البانر
- image_url: رابط صورة البانر
- banner_type: نوع البانر (regular, mod, subscription)
- target_mod_id: معرف المود المستهدف ⭐ جديد
- campaign_id: معرف حملة الاشتراك
- click_url: رابط الوجهة للبانرات العادية
- display_order: ترتيب العرض
- is_active: حالة التفعيل
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

## 🔐 الأمان

- **Row Level Security (RLS)**: حماية البيانات على مستوى الصفوف
- **التحقق من الصلاحيات**: التحقق من صلاحيات المستخدم
- **تشفير البيانات**: حماية البيانات الحساسة
- **مراجعة الأنشطة**: تسجيل جميع العمليات الإدارية

## 🚀 التحديثات المستقبلية

- [ ] إدارة المستخدمين والصلاحيات
- [ ] تحليلات مفصلة ولوحة معلومات متقدمة
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] إشعارات تلقائية للمشرفين
- [ ] API للتكامل مع أنظمة خارجية
- [ ] نظام المهام المجدولة
- [ ] تقارير مخصصة وتصدير البيانات

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من تشغيل ملفات SQL المطلوبة
3. تحقق من إعدادات Supabase
4. راجع هذا الدليل للحلول الشائعة

---

**🎉 تم تطوير لوحة الإدارة الموحدة بواسطة فريق Mod Etaris**

*آخر تحديث: يناير 2025*
