// User Settings JavaScript
// إعدادات المستخدم

// Global variables
let supabaseClient; // Will be initialized from SupabaseManager
let currentSettings = {};
let userId = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log('User Settings page loaded');
    initializePage();
    createFloatingParticles();
    setupEventListeners();
    setupMobileOptimizations();
    setupTranslationSystem();
    displayUserId(); // Display user ID in the settings
});

// Initialize Supabase and load settings
async function initializePage() {
    try {
        // Initialize Supabase client using SupabaseManager
        if (typeof supabaseManager !== 'undefined') {
            supabaseClient = supabaseManager.getClient();
        } else {
            console.error('SupabaseManager is not defined. Ensure supabase-manager.js is loaded before user-settings.js');
            // Fallback to direct initialization if SupabaseManager is not available (should not happen in production)
            const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
            supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        }

        // Generate or get user ID
        userId = generateUserId();

        // Load current settings
        loadSettings();

    } catch (error) {
        console.error('Error initializing user settings page:', error);
        showError('حدث خطأ أثناء تحميل الصفحة');
    }
}

// Generate or get user ID
function generateUserId() {
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = `user_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        localStorage.setItem('userId', userId);
    }
    return userId;
}

// Setup event listeners
function setupEventListeners() {
    // Language change
    const appLanguage = document.getElementById('appLanguage');
    if (appLanguage) {
        appLanguage.addEventListener('change', function() {
            changeLanguage(this.value);
        });
    }

    // Description type change
    const descriptionType = document.getElementById('descriptionType');
    if (descriptionType) {
        descriptionType.addEventListener('change', function() {
            saveSettingToStorage('descriptionType', this.value);
            showSuccess('تم تغيير نوع الأوصاف بنجاح!');
        });
    }

    // Content filter toggle
    const contentFilter = document.getElementById('contentFilter');
    if (contentFilter) {
        contentFilter.addEventListener('change', function() {
            saveSettingToStorage('contentFilter', this.checked);
            showSuccess(this.checked ? 'تم تفعيل فلترة المحتوى' : 'تم إلغاء فلترة المحتوى');
        });
    }
    
    // User ID copy button
    const copyUserIdBtn = document.getElementById('copyUserId');
    if (copyUserIdBtn) {
        copyUserIdBtn.addEventListener('click', function() {
            copyUserIdToClipboard();
        });
    }

    // Additional settings event listeners
    setupAdditionalEventListeners();

    // Save and Reset buttons
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');
    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', saveAllSettings);
    }

    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    if (resetSettingsBtn) {
        resetSettingsBtn.addEventListener('click', resetAllSettings);
    }
}

// Setup additional event listeners for new settings
function setupAdditionalEventListeners() {
    // Data Saving Settings Event Listeners

    // Enable Data Saving
    const enableDataSaving = document.getElementById('enableDataSaving');
    if (enableDataSaving) {
        enableDataSaving.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_enabled', this.checked);
            updateDataSavingSettings();
            showSuccess(this.checked ? 'تم تفعيل توفير البيانات' : 'تم إلغاء توفير البيانات');
        });
    }

    // Aggressive Mode
    const aggressiveMode = document.getElementById('aggressiveMode');
    if (aggressiveMode) {
        aggressiveMode.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_aggressiveMode', this.checked);
            updateDataSavingSettings();
            showSuccess(this.checked ? 'تم تفعيل الوضع المتقدم' : 'تم إلغاء الوضع المتقدم');
        });
    }

    // Low Data Mode
    const lowDataMode = document.getElementById('lowDataMode');
    if (lowDataMode) {
        lowDataMode.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_lowDataMode', this.checked);
            updateDataSavingSettings();
            showSuccess(this.checked ? 'تم تفعيل وضع البيانات المنخفضة' : 'تم إلغاء وضع البيانات المنخفضة');
        });
    }

    // Auto Refresh on WiFi
    const autoRefreshWifi = document.getElementById('autoRefreshWifi');
    if (autoRefreshWifi) {
        autoRefreshWifi.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_autoRefreshOnWifi', this.checked);
            updateDataSavingSettings();
            showSuccess(this.checked ? 'تم تفعيل التحديث التلقائي مع الواي فاي' : 'تم إلغاء التحديث التلقائي');
        });
    }

    // Show Data Usage Stats
    const showDataStats = document.getElementById('showDataStats');
    if (showDataStats) {
        showDataStats.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_showDataUsageStats', this.checked);
            updateDataSavingSettings();
            showSuccess(this.checked ? 'تم تفعيل عرض الإحصائيات' : 'تم إلغاء عرض الإحصائيات');
        });
    }

    // Cache Size Slider
    const maxCacheSize = document.getElementById('maxCacheSize');
    const cacheValue = document.getElementById('cacheValue');
    if (maxCacheSize && cacheValue) {
        maxCacheSize.addEventListener('input', function() {
            cacheValue.textContent = `${this.value} MB`;
        });

        maxCacheSize.addEventListener('change', function() {
            saveSettingToStorage('dataSaving_maxCacheSize', this.value);
            updateDataSavingSettings();
            showSuccess(`تم تحديد حجم التخزين المؤقت إلى ${this.value} MB`);
        });
    }
}

// Load current settings
function loadSettings() {
    try {
        // Load language setting
        const currentLanguage = localStorage.getItem('selectedLanguage') || 'ar';
        const appLanguage = document.getElementById('appLanguage');
        if (appLanguage) {
            appLanguage.value = currentLanguage;
        }
        updateUserSettingsHeader(currentLanguage); // Update header on load

        // Load description type
        const descriptionType = localStorage.getItem('descriptionType') || 'official';
        const descriptionSelect = document.getElementById('descriptionType');
        if (descriptionSelect) {
            descriptionSelect.value = descriptionType;
        }

        // Load content filter
        const contentFilter = localStorage.getItem('contentFilter') === 'true';
        const contentFilterToggle = document.getElementById('contentFilter');
        if (contentFilterToggle) {
            contentFilterToggle.checked = contentFilter;
        }


        // Load additional settings
        loadAdditionalSettings();

        console.log('Settings loaded successfully');

    } catch (error) {
        console.error('Error loading settings:', error);
        showError('حدث خطأ أثناء تحميل الإعدادات');
    }
}

// Load additional settings
function loadAdditionalSettings() {
    try {
        // Load Data Saving Settings
        loadDataSavingSettings();

        // Update data saving statistics
        updateDataSavingStatistics();

        // Set up periodic statistics update
        setInterval(updateDataSavingStatistics, 10000); // Update every 10 seconds

    } catch (error) {
        console.error('Error loading additional settings:', error);
    }
}

// Load Data Saving Settings
function loadDataSavingSettings() {
    try {
        // Get settings from localStorage or use defaults
        const settings = getDataSavingSettingsFromStorage();

        // Apply settings to UI elements
        const enableDataSaving = document.getElementById('enableDataSaving');
        if (enableDataSaving) {
            enableDataSaving.checked = settings.enabled;
        }

        const aggressiveMode = document.getElementById('aggressiveMode');
        if (aggressiveMode) {
            aggressiveMode.checked = settings.aggressiveMode;
        }

        const lowDataMode = document.getElementById('lowDataMode');
        if (lowDataMode) {
            lowDataMode.checked = settings.lowDataMode;
        }

        const autoRefreshWifi = document.getElementById('autoRefreshWifi');
        if (autoRefreshWifi) {
            autoRefreshWifi.checked = settings.autoRefreshOnWifi;
        }

        const showDataStats = document.getElementById('showDataStats');
        if (showDataStats) {
            showDataStats.checked = settings.showDataUsageStats;
        }

        const maxCacheSize = document.getElementById('maxCacheSize');
        const cacheValue = document.getElementById('cacheValue');
        if (maxCacheSize && cacheValue) {
            maxCacheSize.value = settings.maxCacheSize;
            cacheValue.textContent = `${settings.maxCacheSize} MB`;
        }

        console.log('Data saving settings loaded:', settings);

    } catch (error) {
        console.error('Error loading data saving settings:', error);
    }
}

// Get Data Saving Settings from Storage
function getDataSavingSettingsFromStorage() {
    // Check if main app's getDataSavingSettings function is available
    if (typeof getDataSavingSettings === 'function') {
        return getDataSavingSettings();
    }

    // Fallback: read directly from localStorage
    return {
        enabled: localStorage.getItem('dataSaving_enabled') !== 'false', // Default true
        aggressiveMode: localStorage.getItem('dataSaving_aggressiveMode') === 'true',
        lowDataMode: localStorage.getItem('dataSaving_lowDataMode') === 'true',
        autoRefreshOnWifi: localStorage.getItem('dataSaving_autoRefreshOnWifi') !== 'false', // Default true
        showDataUsageStats: localStorage.getItem('dataSaving_showDataUsageStats') !== 'false', // Default true
        maxCacheSize: parseInt(localStorage.getItem('dataSaving_maxCacheSize')) || 50
    };
}

// Update Data Saving Settings in main app
function updateDataSavingSettings() {
    try {
        // Collect current settings from UI
        const settings = {
            enabled: document.getElementById('enableDataSaving')?.checked || false,
            aggressiveMode: document.getElementById('aggressiveMode')?.checked || false,
            lowDataMode: document.getElementById('lowDataMode')?.checked || false,
            autoRefreshOnWifi: document.getElementById('autoRefreshWifi')?.checked || false,
            showDataUsageStats: document.getElementById('showDataStats')?.checked || false,
            maxCacheSize: parseInt(document.getElementById('maxCacheSize')?.value) || 50
        };

        // Save to localStorage in the format expected by main app
        const settingsString = JSON.stringify(settings);
        localStorage.setItem('dataSavingSettings', settingsString);

        // If main app's saveDataSavingSettings function is available, use it
        if (typeof saveDataSavingSettings === 'function') {
            saveDataSavingSettings(settings);
        }

        // Apply settings immediately if main app functions are available
        if (typeof applyDataSavingSettings === 'function') {
            applyDataSavingSettings(settings);
        }

        console.log('Data saving settings updated:', settings);

        // Update statistics after settings change
        setTimeout(updateDataSavingStatistics, 1000);

    } catch (error) {
        console.error('Error updating data saving settings:', error);
    }
}

// Update Data Saving Statistics Display
function updateDataSavingStatistics() {
    try {
        const statsContainer = document.getElementById('dataSavingStats');
        if (!statsContainer) return;

        // Check if main app's cache system is available
        if (typeof appDataCache !== 'undefined' && typeof isCacheValid === 'function') {
            // Calculate statistics from main app's cache
            const stats = {
                totalItems: 0,
                validItems: 0,
                expiredItems: 0,
                cacheSize: 0,
                savingPercentage: 0
            };

            // Count cache items
            Object.keys(appDataCache.lastFetchTimes).forEach(dataType => {
                if (appDataCache[dataType]) {
                    stats.totalItems++;
                    if (isCacheValid(dataType)) {
                        stats.validItems++;
                    } else {
                        stats.expiredItems++;
                    }
                }
            });

            // Calculate cache size
            stats.cacheSize = JSON.stringify(appDataCache).length / 1024; // KB

            // Calculate saving percentage
            if (stats.totalItems > 0) {
                stats.savingPercentage = Math.round((stats.validItems / stats.totalItems) * 100);
            }

            // Update display
            statsContainer.innerHTML = `
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-[#363636] rounded-lg p-3">
                        <div class="text-[#ffd700] font-bold">📦 العناصر المحفوظة</div>
                        <div class="text-white text-lg">${stats.totalItems}</div>
                    </div>
                    <div class="bg-[#363636] rounded-lg p-3">
                        <div class="text-[#ffd700] font-bold">✅ العناصر الصالحة</div>
                        <div class="text-white text-lg">${stats.validItems}</div>
                    </div>
                    <div class="bg-[#363636] rounded-lg p-3">
                        <div class="text-[#ffd700] font-bold">💾 حجم التخزين</div>
                        <div class="text-white text-lg">${Math.round(stats.cacheSize)} KB</div>
                    </div>
                    <div class="bg-[#363636] rounded-lg p-3">
                        <div class="text-[#ffd700] font-bold">📊 نسبة التوفير</div>
                        <div class="text-white text-lg">${stats.savingPercentage}%</div>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-green-900/30 rounded-lg border border-green-700">
                    <div class="text-green-400 font-bold mb-2">💡 نصائح توفير البيانات:</div>
                    <ul class="text-green-300 text-sm space-y-1">
                        <li>• الوضع المتقدم يوفر حتى 70% من البيانات</li>
                        <li>• وضع البيانات المنخفضة مناسب للباقات المحدودة</li>
                        <li>• التحديث التلقائي يعمل فقط مع الواي فاي</li>
                    </ul>
                </div>
            `;

        } else {
            // عرض رسالة بسيطة بدلاً من الإشعارات المزعجة
            statsContainer.innerHTML = `
                <div class="text-center p-4">
                    <div class="text-[#adadad] text-sm">
                        نظام توفير البيانات يعمل في الخلفية بصمت
                    </div>
                    <div class="mt-2 text-[#adadad] text-sm">
                        ستظهر الإحصائيات هنا بعد استخدام التطبيق لفترة
                    </div>
                </div>
            `;
        }

    } catch (error) {
        console.error('Error updating data saving statistics:', error);

        // عرض رسالة بسيطة بدلاً من رسالة الخطأ
        const statsContainer = document.getElementById('dataSavingStats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="text-center p-4">
                    <div class="text-[#adadad] text-sm">
                        نظام توفير البيانات يعمل في الخلفية بصمت
                    </div>
                </div>
            `;
        }
    }
}

// Save setting to localStorage
function saveSettingToStorage(key, value) {
    try {
        localStorage.setItem(key, value.toString());
        console.log(`Setting saved: ${key} = ${value}`);

        // Auto-apply settings to main app
        setTimeout(() => {
            autoApplySettings();
        }, 100);

    } catch (error) {
        console.error('Error saving setting:', error);
        showError('حدث خطأ أثناء حفظ الإعداد');
    }
}

// Change language
async function changeLanguage(language) {
    try {
        // Update translation manager if available
        if (window.translationManager) {
            window.translationManager.setLanguage(language);
        }

        // Save to localStorage
        localStorage.setItem('selectedLanguage', language);
        localStorage.setItem('languageSelected', 'true');

        // Apply translations immediately
        applyTranslations(language);

        // Save to database if available
        if (supabaseClient) {
            await saveUserLanguagePreference(language);
        }

        // Show success message in the selected language
        const successMessage = language === 'ar' ?
            'تم تغيير اللغة بنجاح! ✅' :
            'Language changed successfully! ✅';
        showSuccess(successMessage);

        // Apply to main app if available
        if (window.parent && window.parent !== window) {
            try {
                window.parent.postMessage({
                    type: 'LANGUAGE_CHANGED',
                    language: language
                }, '*');
            } catch (e) {
                console.log('Could not communicate with parent window');
            }
        }

        console.log('Language changed to:', language);

    } catch (error) {
        console.error('Error changing language:', error);
        const errorMessage = language === 'ar' ?
            'حدث خطأ أثناء تغيير اللغة' :
            'Error changing language';
        showError(errorMessage);
    }
}

// Update the user settings header based on the selected language
function updateUserSettingsHeader(language) {
    const header = document.getElementById('userSettingsHeader');
    if (header) {
        if (language === 'ar') {
            header.textContent = header.getAttribute('data-ar');
        } else {
            header.textContent = header.getAttribute('data-en');
        }
    }
}

// Save user language preference to database
async function saveUserLanguagePreference(language) {
    try {
        if (!supabaseClient) return false;

        const deviceInfo = {
            device: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
            browser: getBrowserName(),
            screen: `${screen.width}x${screen.height}`,
            language: navigator.language,
            timestamp: new Date().toISOString()
        };

        const { error } = await supabaseClient
            .from('user_languages')
            .upsert([
                {
                    user_id: userId,
                    selected_language: language,
                    device_info: deviceInfo,
                    user_agent: navigator.userAgent,
                    updated_at: new Date().toISOString()
                }
            ], {
                onConflict: 'user_id'
            });

        if (error) {
            console.error('Error saving language preference:', error);
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error in saveUserLanguagePreference:', error);
        return false;
    }
}

// Get browser name
function getBrowserName() {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'chrome';
    if (userAgent.includes('Firefox')) return 'firefox';
    if (userAgent.includes('Safari')) return 'safari';
    if (userAgent.includes('Edge')) return 'edge';
    return 'unknown';
}


// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');

    if (errorDiv) errorDiv.style.display = 'none';

    if (successDiv) {
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        successDiv.style.animation = 'slideIn 0.3s ease';

        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 3000);
    } else {
        // Fallback: create success message if element doesn't exist
        console.log('✅ SUCCESS:', message);
        createTemporaryMessage(message, 'success');
    }
}

// Show error message
function showError(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');

    if (successDiv) successDiv.style.display = 'none';

    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        errorDiv.style.animation = 'slideIn 0.3s ease';

        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    } else {
        // Fallback: create error message if element doesn't exist
        console.error('❌ ERROR:', message);
        createTemporaryMessage(message, 'error');
    }
}

// Create temporary message if elements don't exist
function createTemporaryMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        animation: slideIn 0.3s ease;
        background: ${type === 'success' ? 'linear-gradient(135deg, #4caf50, #45a049)' : 'linear-gradient(135deg, #f44336, #d32f2f)'};
    `;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}


// Clear cache function
function clearCache() {
    if (confirm('هل أنت متأكد من مسح ذاكرة التخزين المؤقت؟ قد يؤثر هذا على أداء التطبيق مؤقتاً.')) {
        try {
            // Clear specific cache items
            const cacheKeys = [
                'modsCache',
                'imagesCache',
                'bannersCache',
                'categoriesCache',
                'lastFetchTime'
            ];

            cacheKeys.forEach(key => {
                localStorage.removeItem(key);
            });

            // Clear any cached data with timestamps
            Object.keys(localStorage).forEach(key => {
                if (key.includes('_cache_') || key.includes('_timestamp_')) {
                    localStorage.removeItem(key);
                }
            });

            showSuccess('تم مسح ذاكرة التخزين المؤقت بنجاح!');

            // Suggest app reload
            setTimeout(() => {
                if (confirm('يُنصح بإعادة تحميل التطبيق لتطبيق التغييرات. هل تريد إعادة التحميل الآن؟')) {
                    location.reload();
                }
            }, 2000);

        } catch (error) {
            console.error('Error clearing cache:', error);
            showError('حدث خطأ أثناء مسح ذاكرة التخزين المؤقت');
        }
    }
}

// Export settings function
function exportSettings() {
    try {
        const allSettings = {};

        // Get all settings from localStorage
        Object.keys(localStorage).forEach(key => {
            if (!key.includes('userId') && !key.includes('downloaded_')) {
                allSettings[key] = localStorage.getItem(key);
            }
        });

        const exportData = {
            settings: allSettings,
            exportDate: new Date().toISOString(),
            version: '1.0',
            appVersion: 'Mod Etaris v2.0'
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `mod_etaris_settings_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showSuccess('تم تصدير الإعدادات بنجاح!');

    } catch (error) {
        console.error('Error exporting settings:', error);
        showError('حدث خطأ أثناء تصدير الإعدادات');
    }
}

// Import settings function
function importSettings(input) {
    const file = input.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importData = JSON.parse(e.target.result);

            if (!importData.settings) {
                showError('ملف الإعدادات غير صالح');
                return;
            }

            if (confirm('هل أنت متأكد من استيراد هذه الإعدادات؟ سيتم استبدال الإعدادات الحالية.')) {
                // Import settings
                Object.keys(importData.settings).forEach(key => {
                    localStorage.setItem(key, importData.settings[key]);
                });

                showSuccess('تم استيراد الإعدادات بنجاح! سيتم إعادة تحميل الصفحة...');

                setTimeout(() => {
                    location.reload();
                }, 2000);
            }

        } catch (error) {
            console.error('Error importing settings:', error);
            showError('حدث خطأ أثناء استيراد الإعدادات. تأكد من صحة الملف.');
        }
    };

    reader.readAsText(file);
    input.value = ''; // Reset file input
}

// Save all settings function
async function saveAllSettings() {
    try {
        // تم إزالة حالة التحميل لتحسين سرعة التنقل
        const saveBtn = document.getElementById('saveSettingsBtn');
        const originalText = saveBtn ? saveBtn.textContent : '';
        // لا نغير النص أو نعطل الزر لتحسين السرعة

        // Collect all current settings
        const allSettings = {
            selectedLanguage: localStorage.getItem('selectedLanguage') || 'ar',
            descriptionType: localStorage.getItem('descriptionType') || 'official',
            contentFilter: localStorage.getItem('contentFilter') === 'true',
            dataSaving: getDataSavingSettingsFromStorage()
        };

        // Update data saving settings specifically
        updateDataSavingSettings();

        // Save timestamp
        localStorage.setItem('settingsLastSaved', new Date().toISOString());

        // Apply all settings to the main app
        applyAllSettingsToMainApp();

        // Sync with server if available
        if (supabaseClient && userId) {
            await syncSettingsWithServer();
        }

        // تم إزالة استعادة حالة الزر (لأنه لم يتم تغييره)
        // الزر يبقى في حالته الطبيعية

        showSuccess('تم حفظ جميع الإعدادات بنجاح! ✅');
        console.log('All settings saved:', allSettings);

    } catch (error) {
        console.error('Error saving all settings:', error);

        // Restore button state
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            saveBtn.textContent = '💾 Save Changes';
            saveBtn.disabled = false;
        }

        showError('حدث خطأ أثناء حفظ الإعدادات');
    }
}

// Reset all settings function
function resetAllSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التخصيصات الحالية.')) {
        try {
            // تم إزالة حالة التحميل لتحسين سرعة التنقل
            const resetBtn = document.getElementById('resetSettingsBtn');
            // لا نغير النص أو نعطل الزر لتحسين السرعة

            // List of settings to reset (keep user data)
            const settingsToReset = [
                'selectedLanguage',
                'descriptionType',
                'contentFilter',
                'settingsLastSaved',
                'dataSavingSettings',
                'dataSaving_enabled',
                'dataSaving_aggressiveMode',
                'dataSaving_lowDataMode',
                'dataSaving_autoRefreshOnWifi',
                'dataSaving_showDataUsageStats',
                'dataSaving_maxCacheSize'
            ];

            // Create backup before reset
            const backup = {};
            settingsToReset.forEach(setting => {
                const value = localStorage.getItem(setting);
                if (value !== null) {
                    backup[setting] = value;
                }
            });

            // Store backup
            localStorage.setItem('settingsBackup', JSON.stringify({
                backup: backup,
                timestamp: new Date().toISOString()
            }));

            // Reset settings
            settingsToReset.forEach(setting => {
                localStorage.removeItem(setting);
            });

            // Reset data saving settings to defaults
            const defaultDataSavingSettings = {
                enabled: true,
                aggressiveMode: false,
                lowDataMode: false,
                autoRefreshOnWifi: true,
                showDataUsageStats: true,
                maxCacheSize: 50
            };
            localStorage.setItem('dataSavingSettings', JSON.stringify(defaultDataSavingSettings));

            showSuccess('تم إعادة تعيين جميع الإعدادات! سيتم إعادة تحميل الصفحة...');

            setTimeout(() => {
                location.reload();
            }, 2000);

        } catch (error) {
            console.error('Error resetting settings:', error);

            // Restore button state
            const resetBtn = document.getElementById('resetSettingsBtn');
            if (resetBtn) {
                resetBtn.textContent = '🔄 Reset All Settings';
                resetBtn.disabled = false;
            }

            showError('حدث خطأ أثناء إعادة تعيين الإعدادات');
        }
    }
}

// Clear all data function
function clearAllData() {
    if (confirm('تحذير: هذا سيمسح جميع البيانات بما في ذلك التحميلات والتفضيلات. هل أنت متأكد؟')) {
        if (confirm('هذا الإجراء لا يمكن التراجع عنه. هل تريد المتابعة؟')) {
            try {
                // Clear all localStorage except critical app data
                const keysToKeep = ['userId']; // Keep user ID for continuity

                Object.keys(localStorage).forEach(key => {
                    if (!keysToKeep.includes(key)) {
                        localStorage.removeItem(key);
                    }
                });

                showSuccess('تم مسح جميع البيانات بنجاح! سيتم إعادة تحميل التطبيق...');

                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);

            } catch (error) {
                console.error('Error clearing all data:', error);
                showError('حدث خطأ أثناء مسح البيانات');
            }
        }
    }
}

// Sync settings with server (if available)
async function syncSettingsWithServer() {
    try {
        if (!supabaseClient || !userId) return;

        const settingsToSync = {
            selectedLanguage: localStorage.getItem('selectedLanguage'),
            descriptionType: localStorage.getItem('descriptionType')
        };

        const { error } = await supabaseClient
            .from('user_settings')
            .upsert([
                {
                    user_id: userId,
                    settings: settingsToSync,
                    updated_at: new Date().toISOString()
                }
            ], {
                onConflict: 'user_id'
            });

        if (error) {
            console.error('Error syncing settings with server:', error);
        } else {
            console.log('Settings synced with server successfully');
        }

    } catch (error) {
        console.error('Error in syncSettingsWithServer:', error);
    }
}

// Create floating particles animation
function createFloatingParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;

    setInterval(() => {
        if (Math.random() < 0.2) { // 20% chance every interval
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';

            particlesContainer.appendChild(particle);

            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 10000);
        }
    }, 800);
}

// Apply all settings to main app
function applyAllSettingsToMainApp() {
    try {
        if (window.parent && window.parent !== window) {
            const settings = {
                selectedLanguage: localStorage.getItem('selectedLanguage') || 'ar',
                descriptionType: localStorage.getItem('descriptionType') || 'official',
                contentFilter: localStorage.getItem('contentFilter') === 'true'
            };

            window.parent.postMessage({
                type: 'APPLY_ALL_SETTINGS',
                settings: settings
            }, '*');

            console.log('Settings applied to main app:', settings);
        }
    } catch (error) {
        console.error('Error applying settings to main app:', error);
    }
}

// Listen for messages from main app
window.addEventListener('message', function(event) {
    try {
        if (event.data && event.data.type) {
            switch (event.data.type) {
                case 'REQUEST_SETTINGS':
                    applyAllSettingsToMainApp();
                    break;
                case 'SETTINGS_APPLIED':
                    const currentLang = localStorage.getItem('selectedLanguage') || 'ar';
                    const successMsg = currentLang === 'ar' ?
                        'تم تطبيق الإعدادات على التطبيق الرئيسي' :
                        'Settings applied to main app';
                    showSuccess(successMsg);
                    break;
                case 'SETTINGS_ERROR':
                    const currentLang2 = localStorage.getItem('selectedLanguage') || 'ar';
                    const errorMsg = currentLang2 === 'ar' ?
                        'حدث خطأ أثناء تطبيق الإعدادات على التطبيق الرئيسي' :
                        'Error applying settings to main app';
                    showError(errorMsg);
                    break;
                case 'SET_LANGUAGE':
                    if (event.data.language) {
                        const langSelect = document.getElementById('appLanguage');
                        if (langSelect) {
                            langSelect.value = event.data.language;
                            changeLanguage(event.data.language);
                        }
                    }
                    break;
                case 'TEST_LANGUAGE_SWITCH':
                    if (event.data.language) {
                        changeLanguage(event.data.language);
                    }
                    break;
                case 'TEST_BACK_BUTTON':
                    goBack();
                    break;
                case 'TEST_SETTINGS_SAVE':
                    saveAllSettings();
                    break;
            }
        }
    } catch (error) {
        console.error('Error handling message from main app:', error);
    }
});

// Auto-apply settings when they change
function autoApplySettings() {
    // Apply settings to main app whenever they change
    applyAllSettingsToMainApp();

    // Also trigger a custom event for other parts of the settings page
    const event = new CustomEvent('settingsChanged', {
        detail: {
            timestamp: Date.now()
        }
    });
    document.dispatchEvent(event);
}

// Apply all settings to main app
function applyAllSettingsToMainApp() {
    try {
        // Apply language settings
        const selectedLanguage = localStorage.getItem('selectedLanguage');
        if (selectedLanguage) {
            if (window.translationManager) {
                window.translationManager.setLanguage(selectedLanguage);
            }

            // Communicate with parent window if in iframe
            if (window.parent && window.parent !== window) {
                try {
                    window.parent.postMessage({
                        type: 'LANGUAGE_CHANGED',
                        language: selectedLanguage
                    }, '*');
                } catch (e) {
                    console.log('Could not communicate with parent window');
                }
            }
        }

        // Apply data saving settings
        updateDataSavingSettings();

        // Apply other settings
        const descriptionType = localStorage.getItem('descriptionType');
        const contentFilter = localStorage.getItem('contentFilter');

        console.log('All settings applied to main app:', {
            language: selectedLanguage,
            descriptionType: descriptionType,
            contentFilter: contentFilter,
            dataSaving: getDataSavingSettingsFromStorage()
        });

    } catch (error) {
        console.error('Error applying all settings to main app:', error);
    }
}

// Additional utility functions for better user experience

// Check if settings are properly loaded
function validateSettingsIntegrity() {
    try {
        const requiredElements = [
            'appLanguage', 'descriptionType', 'contentFilter', 'darkMode',
            'displayMode', 'visualEffects', 'backgroundOpacity', 'imageQuality',
            'wifiOnlyDownload', 'autoDeleteFiles', 'downloadNotifications',
            'shareUsageStats', 'saveBrowsingHistory', 'newModsNotifications',
            'specialOffersNotifications', 'notificationTiming', 'developerMode',
            'saveSettingsBtn', 'resetSettingsBtn'
        ];

        const missingElements = [];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                missingElements.push(id);
            }
        });

        if (missingElements.length > 0) {
            console.warn('Missing UI elements:', missingElements);
            // لا نظهر رسالة خطأ للمستخدم، نكتفي بتسجيل الخطأ في وحدة التحكم
            return true; // نعيد true بدلاً من false للسماح بمواصلة التشغيل
        }

        return true;
    } catch (error) {
        console.error('Error validating settings integrity:', error);
        return false;
    }
}

// Enhanced initialization with error handling
function enhancedInitialization() {
    try {
        // Validate UI elements
        if (!validateSettingsIntegrity()) {
            console.warn('Settings page has missing elements, but continuing...');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save settings
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                saveAllSettings();
            }

            // Ctrl+R to reset settings (with confirmation)
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                resetAllSettings();
            }
        });

        // Add visual feedback for interactions
        const settingSections = document.querySelectorAll('.settings-section');
        settingSections.forEach(section => {
            section.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
            });

            section.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });

        // Auto-save settings periodically
        setInterval(() => {
            const lastSaved = localStorage.getItem('settingsLastSaved');
            const now = new Date().getTime();
            const lastSavedTime = lastSaved ? new Date(lastSaved).getTime() : 0;

            // Auto-save every 5 minutes if there are changes
            if (now - lastSavedTime > 300000) { // 5 minutes
                console.log('Auto-saving settings...');
                saveAllSettings();
            }
        }, 60000); // Check every minute

        console.log('Enhanced settings initialization completed');

    } catch (error) {
        console.error('Error in enhanced initialization:', error);
    }
}

// Call enhanced initialization after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(enhancedInitialization, 1000);
});

// Back button functionality
function goBack() {
    try {
        // تم إزالة حالة التحميل لتحسين سرعة التنقل
        const backBtn = document.getElementById('backButton');
        // لا نغير مظهر الزر لتحسين السرعة

        // Try different methods to go back
        if (window.parent && window.parent !== window) {
            // If in iframe, try to communicate with parent
            try {
                window.parent.postMessage({
                    type: 'CLOSE_SETTINGS',
                    action: 'back'
                }, '*');
                return;
            } catch (e) {
                console.log('Could not communicate with parent window');
            }
        }

        // Check if there's a referrer
        if (document.referrer && document.referrer !== window.location.href) {
            window.location.href = document.referrer;
            return;
        }

        // Try to go back in history
        if (window.history.length > 1) {
            window.history.back();

            // Fallback if history.back() doesn't work
            setTimeout(() => {
                if (window.location.href === window.location.href) {
                    window.location.href = 'index.html';
                }
            }, 1000);
        } else {
            // Direct fallback to main page
            window.location.href = 'index.html';
        }

    } catch (error) {
        console.error('Error going back:', error);
        // Final fallback
        window.location.href = 'index.html';
    }
}

// Mobile optimizations
function setupMobileOptimizations() {
    try {
        // Add touch-friendly interactions
        const settingSections = document.querySelectorAll('.settings-section');
        settingSections.forEach(section => {
            section.addEventListener('touchstart', function() {
                this.style.background = 'rgba(255, 255, 255, 0.08)';
            });

            section.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.background = 'rgba(255, 255, 255, 0.05)';
                }, 150);
            });
        });

        // Optimize for mobile viewport
        const viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            const meta = document.createElement('meta');
            meta.name = 'viewport';
            meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
            document.head.appendChild(meta);
        }

        // Add mobile-specific styles
        if (window.innerWidth <= 768) {
            document.body.style.fontSize = '16px'; // Prevent zoom on iOS
        }

        console.log('Mobile optimizations applied');
    } catch (error) {
        console.error('Error setting up mobile optimizations:', error);
    }
}

// Translation system
function setupTranslationSystem() {
    try {
        // Check URL parameters for language
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');

        // Get current language (URL param takes priority)
        let currentLanguage = urlLang || localStorage.getItem('selectedLanguage') || 'ar';

        // Validate language
        if (!['ar', 'en'].includes(currentLanguage)) {
            currentLanguage = 'ar';
        }

        // Update localStorage if URL param was used
        if (urlLang && urlLang !== localStorage.getItem('selectedLanguage')) {
            localStorage.setItem('selectedLanguage', currentLanguage);
        }

        // Update language selector
        const langSelect = document.getElementById('appLanguage');
        if (langSelect) {
            langSelect.value = currentLanguage;
        }

        // Apply translations immediately
        applyTranslations(currentLanguage);

        console.log('Translation system initialized for language:', currentLanguage);
    } catch (error) {
        console.error('Error setting up translation system:', error);
    }
}

// Display user ID in the settings
function displayUserId() {
    try {
        // Get user ID from localStorage
        const userIdValue = localStorage.getItem('userId');
        const userIdField = document.getElementById('userId');
        
        if (userIdField) {
            if (userIdValue) {
                userIdField.value = userIdValue;
            } else {
                // Generate a new ID if none exists
                const newUserId = generateUserId();
                userIdField.value = newUserId;
            }
        }
    } catch (error) {
        console.error('Error displaying user ID:', error);
    }
}

// Copy user ID to clipboard
function copyUserIdToClipboard() {
    try {
        const userIdField = document.getElementById('userId');
        if (!userIdField) return;
        
        // Select the text
        userIdField.select();
        userIdField.setSelectionRange(0, 99999); // For mobile devices
        
        // Copy to clipboard
        document.execCommand('copy');
        
        // Deselect the text
        window.getSelection().removeAllRanges();
        
        // Show success message
        const currentLang = localStorage.getItem('selectedLanguage') || 'ar';
        const successMsg = currentLang === 'ar' ? 
            'تم نسخ معرف المستخدم بنجاح!' : 
            'User ID copied successfully!';
        
        showSuccess(successMsg);
        
        // Visual feedback on the button
        const copyBtn = document.getElementById('copyUserId');
        if (copyBtn) {
            const originalBg = copyBtn.style.backgroundColor;
            copyBtn.style.backgroundColor = '#4ade80'; // Green success color
            
            setTimeout(() => {
                copyBtn.style.backgroundColor = originalBg;
            }, 1000);
        }
    } catch (error) {
        console.error('Error copying user ID:', error);
        showError('حدث خطأ أثناء نسخ معرف المستخدم');
    }
}

// Apply translations to all elements
function applyTranslations(language) {
    try {
        const elementsToTranslate = document.querySelectorAll('[data-en][data-ar]');

        elementsToTranslate.forEach(element => {
            const englishText = element.getAttribute('data-en');
            const arabicText = element.getAttribute('data-ar');

            if (language === 'ar' && arabicText) {
                element.textContent = arabicText;
            } else if (language === 'en' && englishText) {
                element.textContent = englishText;
            }
        });

        // Update select options
        const selectElements = document.querySelectorAll('select option[data-en][data-ar]');
        selectElements.forEach(option => {
            const englishText = option.getAttribute('data-en');
            const arabicText = option.getAttribute('data-ar');

            if (language === 'ar' && arabicText) {
                option.textContent = arabicText;
            } else if (language === 'en' && englishText) {
                option.textContent = englishText;
            }
        });

        // Update document direction
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        document.documentElement.lang = language;

        console.log('Translations applied for language:', language);
    } catch (error) {
        console.error('Error applying translations:', error);
    }
}
