<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أخطاء التحميل - Download Error Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="download-error-styles.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .error-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }
        
        .error-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .error-critical { border-left-color: #dc3545; }
        .error-high { border-left-color: #fd7e14; }
        .error-medium { border-left-color: #ffc107; }
        .error-low { border-left-color: #28a745; }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            margin: 3px;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .action-btn.btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .action-btn.btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }
        
        .action-btn.btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stats-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-unresolved {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-resolved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-in-progress {
            background: #fff3cd;
            color: #856404;
        }
        
        .url-input {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        
        .url-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .timeline-item {
            border-left: 3px solid #667eea;
            padding-left: 20px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 0;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-exclamation-triangle"></i> إدارة أخطاء التحميل</h1>
                <p class="mb-0">مراقبة وإصلاح مشاكل تحميل المودات</p>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="totalErrors">-</div>
                        <div class="stats-label">إجمالي الأخطاء</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="unresolvedErrors">-</div>
                        <div class="stats-label">أخطاء غير محلولة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="successRate">-</div>
                        <div class="stats-label">معدل النجاح %</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="backupUsage">-</div>
                        <div class="stats-label">استخدام الاحتياطي %</div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filter-section">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">نوع الخطأ:</label>
                        <select class="form-select" id="errorTypeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="complete_failure">فشل كامل</option>
                            <option value="original_failed_backup_success">فشل الأصلي نجح الاحتياطي</option>
                            <option value="timeout">انتهاء المهلة</option>
                            <option value="network_error">خطأ شبكة</option>
                            <option value="file_corrupted">ملف تالف</option>
                            <option value="url_not_found">رابط غير موجود</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة:</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="false">غير محلول</option>
                            <option value="true">محلول</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الفترة الزمنية:</label>
                        <select class="form-select" id="timeFilter">
                            <option value="24h">آخر 24 ساعة</option>
                            <option value="7d">آخر 7 أيام</option>
                            <option value="30d">آخر 30 يوم</option>
                            <option value="all">جميع الفترات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="action-btn" onclick="applyFilters()">
                                <i class="fas fa-filter"></i> تطبيق الفلاتر
                            </button>
                            <button class="action-btn btn-warning" onclick="refreshData()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-4">
                <button class="action-btn btn-success" onclick="showBulkFixModal()">
                    <i class="fas fa-tools"></i> إصلاح جماعي
                </button>
                <button class="action-btn btn-warning" onclick="exportErrorReport()">
                    <i class="fas fa-download"></i> تصدير التقرير
                </button>
                <button class="action-btn" onclick="showAddBackupModal()">
                    <i class="fas fa-plus"></i> إضافة رابط احتياطي
                </button>
                <button class="action-btn btn-danger" onclick="cleanupOldErrors()">
                    <i class="fas fa-trash"></i> تنظيف الأخطاء القديمة
                </button>
            </div>

            <!-- Errors List -->
            <div id="errorsList">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل أخطاء التحميل...</p>
                </div>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- سيتم ملؤها ديناميكياً -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- Fix URL Modal -->
    <div class="modal fade" id="fixUrlModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إصلاح رابط التحميل</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المود:</label>
                        <input type="text" class="form-control" id="fixModName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرابط الحالي (المعطل):</label>
                        <input type="url" class="form-control url-input" id="fixCurrentUrl" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرابط الجديد:</label>
                        <input type="url" class="form-control url-input" id="fixNewUrl" placeholder="أدخل الرابط الجديد">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رابط احتياطي (اختياري):</label>
                        <input type="url" class="form-control url-input" id="fixBackupUrl" placeholder="أدخل رابط احتياطي">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات الإصلاح:</label>
                        <textarea class="form-control" id="fixNotes" rows="3" placeholder="اكتب ملاحظات حول الإصلاح..."></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="testUrlBeforeFix">
                        <label class="form-check-label" for="testUrlBeforeFix">
                            اختبار الرابط قبل الحفظ
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="action-btn" onclick="testUrl()">
                        <i class="fas fa-vial"></i> اختبار الرابط
                    </button>
                    <button type="button" class="action-btn btn-success" onclick="applyFix()">
                        <i class="fas fa-check"></i> تم الإصلاح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Backup URL Modal -->
    <div class="modal fade" id="addBackupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة رابط احتياطي</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اختر المود:</label>
                        <select class="form-select" id="backupModSelect">
                            <option value="">اختر مود...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رابط التحميل الاحتياطي:</label>
                        <input type="url" class="form-control url-input" id="newBackupUrl" placeholder="أدخل رابط التحميل الاحتياطي">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الرابط:</label>
                        <select class="form-select" id="backupType">
                            <option value="firebase">Firebase Storage</option>
                            <option value="external">رابط خارجي</option>
                            <option value="mirror">مرآة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الأولوية (1-10):</label>
                        <input type="number" class="form-control" id="backupPriority" value="1" min="1" max="10">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="action-btn btn-success" onclick="addBackupUrl()">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Fix Modal -->
    <div class="modal fade" id="bulkFixModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الإصلاح الجماعي للروابط</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        يمكنك إصلاح عدة روابط معطلة في نفس الوقت. اختر الأخطاء التي تريد إصلاحها.
                    </div>
                    <div id="bulkFixList">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="action-btn btn-success" onclick="applyBulkFix()">
                        <i class="fas fa-tools"></i> تطبيق الإصلاح الجماعي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Custom JavaScript -->
    <script src="download-error-manager.js"></script>
</body>
</html>
