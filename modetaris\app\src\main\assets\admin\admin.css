/* Variables */
:root {
    --primary-color: #1e1e1e;
    --secondary-color: #333;
    --accent-color: #ffd700;
    --text-color: #ffffff;
    --card-background: #2a2a2a;
    --error-color: #f44336;
    --success-color: #4caf50;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    direction: rtl;
}

.header {
    background-color: var(--secondary-color);
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--accent-color);
}

/* Admin-specific styles */
.admin-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.admin-section {
    background-color: var(--card-background);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid var(--accent-color);
}

.admin-section h2 {
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.admin-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    color: var(--text-color);
    font-weight: bold;
}

.form-group input, .form-group textarea {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #555;
    background-color: #222;
    color: white;
}

.form-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-button {
    background: linear-gradient(45deg, #ffd700, #ffcc00);
    color: black;
    border: none;
    padding: 12px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.admin-button:hover {
    transform: scale(1.05);
}

.banner-list {
    margin-top: 20px;
}

.banner-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border-bottom: 1px solid #444;
    margin-bottom: 10px;
}

.banner-image {
    width: 100px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
}

.banner-info {
    flex-grow: 1;
}

.banner-title {
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 5px;
}

.banner-actions {
    display: flex;
    gap: 10px;
}

.banner-actions button {
    background-color: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.2s ease;
}

.edit-button:hover {
    color: #4caf50;
}

.delete-button:hover {
    color: #f44336;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    margin-right: 10px;
}

.status-active {
    background-color: #4caf50;
    color: white;
}

.status-inactive {
    background-color: #f44336;
    color: white;
}

.preview-container {
    width: 100%;
    height: 150px;
    border: 2px dashed #555;
    border-radius: 10px;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.back-to-app {
    display: block;
    text-align: center;
    margin-top: 20px;
    color: var(--accent-color);
    text-decoration: none;
}

/* Loading indicator */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #aaa;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Modal styles */
.preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;
}

.preview-modal-content {
    background-color: #1e1e1e;
    width: 100%;
    max-width: 600px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.preview-modal-header {
    background-color: #333;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
}

.preview-modal-title {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0;
}

.preview-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.preview-modal-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.preview-device-frame {
    border: 2px solid #444;
    border-radius: 10px;
    padding: 10px;
    background-color: #111;
}

.preview-device-header {
    color: #aaa;
    font-size: 0.9rem;
    margin-bottom: 10px;
    text-align: center;
}

.preview-device-content {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 5px;
    background-color: #000;
}

.preview-banner-container {
    width: 100%;
    height: 150px;
    position: relative;
    overflow: hidden;
    border-radius: 5px;
}

.preview-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-modal-footer {
    padding: 15px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #444;
}

.preview-modal-button {
    background: linear-gradient(45deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
}
