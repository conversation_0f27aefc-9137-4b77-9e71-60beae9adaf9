<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المربعات المخصصة</title>
    <link rel="stylesheet" href="admin_style.css">
    <style>
        .dialog-form {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #ffcc00;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #ffcc00;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #444;
            border-radius: 8px;
            background: #1a1a2e;
            color: white;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ffcc00;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffcc00, #ff9800);
            color: #000;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #666, #888);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .dialogs-list {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #ffcc00;
        }

        .dialog-item {
            background: #1a1a2e;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #444;
        }

        .dialog-item h3 {
            color: #ffcc00;
            margin-bottom: 10px;
        }

        .dialog-item p {
            color: #ccc;
            margin-bottom: 10px;
        }

        .dialog-item .dialog-meta {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
        }

        .dialog-actions {
            display: flex;
            gap: 10px;
        }

        .dialog-actions .btn {
            padding: 8px 15px;
            font-size: 0.9rem;
        }

        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .preview-content {
            background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            border: 3px solid #ffcc00;
            box-shadow: 0 0 30px rgba(255, 204, 0, 0.5);
        }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .preview-title {
            color: #ffcc00;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .preview-description {
            color: white;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #22c55e;
            color: white;
        }

        .status-inactive {
            background: #ef4444;
            color: white;
        }

        .loading {
            text-align: center;
            color: #ffcc00;
            font-size: 1.2rem;
            padding: 20px;
        }

        .error-message {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 2px solid #fca5a5;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .error-message h3 {
            color: #fef2f2;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .error-message textarea {
            background: #1a1a2e;
            border: 1px solid #444;
            color: #e5e7eb;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
        }

        .error-message ol {
            margin: 10px 0;
            padding-right: 20px;
        }

        .error-message li {
            margin-bottom: 5px;
        }

        /* تنسيق العناصر ثنائية اللغة */
        .dialog-titles h3 {
            margin: 0 0 5px 0;
            color: #2c3e50;
        }

        .dialog-titles h4 {
            margin: 0 0 10px 0;
            color: #34495e;
            font-size: 1.1em;
            font-weight: normal;
        }

        .dialog-descriptions {
            margin: 10px 0;
        }

        .description-ar, .description-en {
            margin: 5px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #3498db;
        }

        .description-en {
            direction: ltr;
            text-align: left;
        }

        .button-texts {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin: 5px 0;
        }

        .button-texts span {
            font-size: 0.9em;
            color: #666;
        }

        /* تحسين أزرار المعاينة */
        .dialog-actions .btn-success {
            margin: 0 2px;
            font-size: 0.85em;
        }

        /* تنسيق النموذج ثنائي اللغة */
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .form-group input[placeholder*="English"],
        .form-group textarea[placeholder*="English"] {
            direction: ltr;
            text-align: left;
        }

        .success-message {
            background: #22c55e;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>إدارة المربعات المخصصة</h1>
            <p>إنشاء وإدارة المربعات التي تظهر قبل عرض تفاصيل المودات</p>
        </div>

        <div id="messageContainer"></div>

        <!-- نموذج إنشاء/تعديل المربع -->
        <div class="dialog-form">
            <h2 id="formTitle">إنشاء مربع جديد</h2>
            <form id="dialogForm">
                <input type="hidden" id="dialogId" value="">

                <!-- العنوان بالعربية -->
                <div class="form-group">
                    <label for="dialogTitle">عنوان المربع (عربي) *</label>
                    <input type="text" id="dialogTitle" required placeholder="أدخل عنوان المربع بالعربية">
                </div>

                <!-- العنوان بالإنجليزية -->
                <div class="form-group">
                    <label for="dialogTitleEn">عنوان المربع (إنجليزي)</label>
                    <input type="text" id="dialogTitleEn" placeholder="Enter dialog title in English">
                </div>

                <!-- الوصف بالعربية -->
                <div class="form-group">
                    <label for="dialogDescription">وصف المربع (عربي)</label>
                    <textarea id="dialogDescription" placeholder="أدخل وصف المربع بالعربية (اختياري)"></textarea>
                </div>

                <!-- الوصف بالإنجليزية -->
                <div class="form-group">
                    <label for="dialogDescriptionEn">وصف المربع (إنجليزي)</label>
                    <textarea id="dialogDescriptionEn" placeholder="Enter dialog description in English (optional)"></textarea>
                </div>

                <div class="form-group">
                    <label for="dialogImage">رابط الصورة</label>
                    <input type="url" id="dialogImage" placeholder="https://example.com/image.jpg">
                </div>

                <!-- نص الزر بالعربية -->
                <div class="form-group">
                    <label for="buttonText">نص الزر (عربي)</label>
                    <input type="text" id="buttonText" value="تم" placeholder="تم">
                </div>

                <!-- نص الزر بالإنجليزية -->
                <div class="form-group">
                    <label for="buttonTextEn">نص الزر (إنجليزي)</label>
                    <input type="text" id="buttonTextEn" value="OK" placeholder="OK">
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="showDontShowAgain" checked>
                        <label for="showDontShowAgain">إظهار خيار "عدم الظهور مجدداً"</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="isActive" checked>
                        <label for="isActive">مفعل</label>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">حفظ المربع</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="previewDialog()">معاينة</button>
                </div>
            </form>
        </div>

        <!-- قائمة المربعات الموجودة -->
        <div class="dialogs-list">
            <h2>المربعات الموجودة</h2>
            <div id="dialogsList" class="loading">جاري تحميل المربعات...</div>
        </div>
    </div>

    <!-- نافذة المعاينة -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <img id="previewImage" class="preview-image" style="display: none;">
            <div id="previewTitle" class="preview-title"></div>
            <div id="previewDescription" class="preview-description"></div>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button class="btn btn-primary" onclick="closePreview()">
                    <span id="previewButtonText">تم</span>
                </button>
                <button class="btn btn-secondary" onclick="closePreview()">عدم الظهور مجدداً</button>
            </div>
            <button onclick="closePreview()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: #ffcc00; font-size: 1.5rem; cursor: pointer;">&times;</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // تحقق من تحميل Supabase
        if (typeof supabase === 'undefined') {
            console.error('❌ Supabase library not loaded');
            document.getElementById('dialogsList').innerHTML = '<div class="error-message">خطأ: مكتبة Supabase غير محملة</div>';
        } else {
            console.log('✅ Supabase library loaded successfully');
        }
    </script>
    <script src="../supabase-manager.js" onerror="console.error('❌ Failed to load supabase-manager.js')"></script>
    <script>
        // تحقق من تحميل Supabase Manager
        setTimeout(() => {
            if (typeof window.supabaseManager === 'undefined') {
                console.error('❌ Supabase Manager not loaded after 1 second');
                document.getElementById('dialogsList').innerHTML = '<div class="error-message">خطأ: Supabase Manager غير محمل. تحقق من مسار الملف.</div>';
            } else {
                console.log('✅ Supabase Manager loaded successfully');
            }
        }, 1000);
    </script>
    <script src="custom_dialogs.js"></script>
</body>
</html>
