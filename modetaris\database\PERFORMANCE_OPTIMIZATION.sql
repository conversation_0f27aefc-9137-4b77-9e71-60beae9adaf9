-- تحسينات الأداء لقاعدة البيانات
-- Performance Optimizations for Database

-- إنشاء فهارس محسنة للاستعلامات الشائعة
-- Create optimized indexes for common queries

-- فهرس مركب للفئة والتاريخ
CREATE INDEX IF NOT EXISTS idx_mods_category_created_at 
ON mods(category, created_at DESC);

-- فهرس للتحميلات والإعجابات
CREATE INDEX IF NOT EXISTS idx_mods_downloads_likes 
ON mods(downloads DESC, likes DESC);

-- فهرس للمودات المميزة والشائعة
CREATE INDEX IF NOT EXISTS idx_mods_featured_popular 
ON mods(is_featured, is_popular, created_at DESC);

-- فهر<PERSON> للبحث النصي
CREATE INDEX IF NOT EXISTS idx_mods_name_search 
ON mods USING gin(to_tsvector('english', name));

-- فهرس للوصف العربي
CREATE INDEX IF NOT EXISTS idx_mods_description_ar_search 
ON mods USING gin(to_tsvector('arabic', description_ar));

-- تحسين جدول المودات المقترحة
CREATE INDEX IF NOT EXISTS idx_suggested_mods_display_order 
ON suggested_mods(display_order ASC, created_at DESC);

-- تحسين جدول الإضافات المجانية
CREATE INDEX IF NOT EXISTS idx_free_addons_display_order 
ON free_addons(display_order ASC, created_at DESC);

-- تحسين جدول الإضافات المميزة
CREATE INDEX IF NOT EXISTS idx_featured_addons_display_order 
ON featured_addons(display_order ASC, created_at DESC);

-- دالة محسنة لجلب المودات بحد أقصى
-- Optimized function to fetch mods with limit
CREATE OR REPLACE FUNCTION get_mods_optimized(
    p_category TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0,
    p_sort_by TEXT DEFAULT 'created_at',
    p_sort_order TEXT DEFAULT 'DESC'
)
RETURNS TABLE(
    id INTEGER,
    name TEXT,
    description TEXT,
    description_ar TEXT,
    image_url TEXT,
    image_urls TEXT[],
    category TEXT,
    downloads INTEGER,
    likes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    creator_name TEXT,
    creator_social_media JSONB,
    is_featured BOOLEAN,
    is_popular BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    EXECUTE format('
        SELECT m.id, m.name, m.description, m.description_ar, m.image_url, 
               m.image_urls, m.category, m.downloads, m.likes, m.created_at,
               m.creator_name, m.creator_social_media, m.is_featured, m.is_popular
        FROM mods m
        WHERE ($1 IS NULL OR m.category = $1)
        ORDER BY %I %s
        LIMIT $2 OFFSET $3',
        p_sort_by, p_sort_order
    ) USING p_category, p_limit, p_offset;
END;
$$ LANGUAGE plpgsql;

-- دالة محسنة للبحث
-- Optimized search function
CREATE OR REPLACE FUNCTION search_mods_optimized(
    p_search_term TEXT,
    p_category TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE(
    id INTEGER,
    name TEXT,
    description TEXT,
    description_ar TEXT,
    image_url TEXT,
    category TEXT,
    downloads INTEGER,
    likes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    relevance REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT m.id, m.name, m.description, m.description_ar, m.image_url,
           m.category, m.downloads, m.likes, m.created_at,
           (ts_rank(to_tsvector('english', m.name), plainto_tsquery('english', p_search_term)) +
            ts_rank(to_tsvector('arabic', COALESCE(m.description_ar, '')), plainto_tsquery('arabic', p_search_term))) as relevance
    FROM mods m
    WHERE (
        to_tsvector('english', m.name) @@ plainto_tsquery('english', p_search_term) OR
        to_tsvector('arabic', COALESCE(m.description_ar, '')) @@ plainto_tsquery('arabic', p_search_term) OR
        m.name ILIKE '%' || p_search_term || '%'
    )
    AND (p_category IS NULL OR m.category = p_category)
    ORDER BY relevance DESC, m.downloads DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- دالة محسنة لجلب المودات الشائعة
-- Optimized function to get popular mods
CREATE OR REPLACE FUNCTION get_popular_mods_optimized(
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    id INTEGER,
    name TEXT,
    description TEXT,
    image_url TEXT,
    category TEXT,
    downloads INTEGER,
    likes INTEGER,
    popularity_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT m.id, m.name, m.description, m.image_url, m.category,
           m.downloads, m.likes,
           (m.downloads * 0.7 + m.likes * 0.3 + 
            EXTRACT(EPOCH FROM (NOW() - m.created_at)) / 86400 * -0.1)::NUMERIC as popularity_score
    FROM mods m
    WHERE m.created_at > NOW() - INTERVAL '30 days'
    ORDER BY popularity_score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف البيانات القديمة
-- Function to cleanup old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- حذف الإحصائيات القديمة (أكثر من 6 أشهر)
    DELETE FROM user_languages 
    WHERE updated_at < NOW() - INTERVAL '6 months';
    
    -- تنظيف جلسات المستخدمين القديمة
    DELETE FROM auth.sessions 
    WHERE expires_at < NOW() - INTERVAL '1 month';
    
    -- تحديث إحصائيات الجداول
    ANALYZE mods;
    ANALYZE suggested_mods;
    ANALYZE free_addons;
    ANALYZE featured_addons;
    
    RAISE NOTICE 'Database cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- جدولة تنظيف البيانات (يتطلب pg_cron extension)
-- Schedule data cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * 0', 'SELECT cleanup_old_data();');

-- إعدادات تحسين الأداء
-- Performance optimization settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- تحسين إعدادات الاتصال
-- Connection optimization settings
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';

-- تفعيل تجميع الإحصائيات
-- Enable statistics collection
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- دالة مراقبة الأداء
-- Performance monitoring function
CREATE OR REPLACE FUNCTION get_performance_stats()
RETURNS TABLE(
    query TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    rows BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pss.query,
        pss.calls,
        pss.total_exec_time as total_time,
        pss.mean_exec_time as mean_time,
        pss.rows
    FROM pg_stat_statements pss
    ORDER BY pss.total_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- دالة تحسين الجداول
-- Table optimization function
CREATE OR REPLACE FUNCTION optimize_tables()
RETURNS void AS $$
DECLARE
    table_name TEXT;
BEGIN
    -- قائمة الجداول المهمة
    FOR table_name IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('mods', 'suggested_mods', 'free_addons', 'featured_addons')
    LOOP
        -- تحديث الإحصائيات
        EXECUTE 'ANALYZE ' || table_name;
        
        -- إعادة فهرسة إذا لزم الأمر
        EXECUTE 'REINDEX TABLE ' || table_name;
        
        RAISE NOTICE 'Optimized table: %', table_name;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- دالة فحص صحة قاعدة البيانات
-- Database health check function
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS TABLE(
    metric TEXT,
    value TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Database Size'::TEXT,
        pg_size_pretty(pg_database_size(current_database()))::TEXT,
        CASE 
            WHEN pg_database_size(current_database()) > ********** THEN 'WARNING'
            ELSE 'OK'
        END::TEXT
    UNION ALL
    SELECT 
        'Active Connections'::TEXT,
        count(*)::TEXT,
        CASE 
            WHEN count(*) > 80 THEN 'WARNING'
            ELSE 'OK'
        END::TEXT
    FROM pg_stat_activity
    WHERE state = 'active'
    UNION ALL
    SELECT 
        'Cache Hit Ratio'::TEXT,
        round((sum(blks_hit) * 100.0 / sum(blks_hit + blks_read)), 2)::TEXT || '%',
        CASE 
            WHEN round((sum(blks_hit) * 100.0 / sum(blks_hit + blks_read)), 2) < 95 THEN 'WARNING'
            ELSE 'OK'
        END::TEXT
    FROM pg_stat_database;
END;
$$ LANGUAGE plpgsql;

-- تطبيق التحسينات
-- Apply optimizations
SELECT optimize_tables();

-- عرض تقرير الصحة
-- Show health report
SELECT * FROM database_health_check();
