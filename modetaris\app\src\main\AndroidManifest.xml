<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

     <uses-permission android:name="android.permission.INTERNET" />
     <!-- Add storage permissions -->
     <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
     <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />



    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Modetaris"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        tools:targetApi="31">

        <!-- AdMob Activity Configuration -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:configChanges,android:theme"/> <!-- Added android:theme to replace -->

        <!-- Sample AdMob App ID. Replace with your actual App ID from AdMob UI. -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-4373910379376809~4188966917"/>

        <!-- Removed SplashActivity declaration -->

        <!-- Main Activity (Restored as launcher, apply splash theme) -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.App.Starting"> <!-- Apply splash theme -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Define FileProvider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <!-- Define the DownloadReceiver -->
        <receiver
            android:name=".DownloadReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE"/>
            </intent-filter>
        </receiver>

        <!-- Placeholder for DropDataProvider to resolve ANR -->
        <provider
            android:name=".DropDataProvider"
            android:authorities="${applicationId}.DropDataProvider"
            android:exported="false"
            android:grantUriPermissions="true" />

    </application>

</manifest>
