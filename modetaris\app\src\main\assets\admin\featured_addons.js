// DOM Elements
const searchInput = document.getElementById('mod-search');
const searchResults = document.getElementById('search-results');
const featuredModsList = document.getElementById('featured-mods-list');

// Global variables
let allMods = [];
let featuredMods = [];
let searchTimeout;

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    // Add event listener for search input
    searchInput.addEventListener('input', handleSearch);

    // Load featured mods
    loadFeaturedMods();

    // Load all mods for search
    loadAllMods();

    // Add animated orbs to preview
    addAnimatedOrbsToPreview();
});

// Function to add animated orbs to the preview
function addAnimatedOrbsToPreview() {
    const previewElement = document.querySelector('.featured-addon-preview');

    // Create 5 orbs with random positions
    for (let i = 0; i < 5; i++) {
        const orb = document.createElement('div');
        orb.className = 'orb';
        orb.style.position = 'absolute';
        orb.style.width = '10px';
        orb.style.height = '10px';
        orb.style.backgroundColor = 'rgba(255, 204, 0, 0.8)';
        orb.style.borderRadius = '50%';
        orb.style.pointerEvents = 'none';
        orb.style.boxShadow = '0 0 10px 2px rgba(255, 204, 0, 0.5)';
        orb.style.opacity = '0';

        // Random position within the element
        const leftPos = Math.random() * 100; // Random percentage across width
        orb.style.left = `${leftPos}%`;
        orb.style.bottom = '0';

        // Random animation delay and duration for more natural effect
        const delay = Math.random() * 2; // 0-2s delay
        const duration = 2 + Math.random() * 2; // 2-4s duration

        orb.style.animation = `orbFloat ${duration}s ease-in-out ${delay}s infinite`;

        previewElement.appendChild(orb);
    }

    // Add keyframes for orb animation if not already in the document
    if (!document.getElementById('orb-animation-style')) {
        const style = document.createElement('style');
        style.id = 'orb-animation-style';
        style.textContent = `
            @keyframes orbFloat {
                0% {
                    opacity: 0;
                    transform: translateY(0) scale(0.8);
                }
                50% {
                    opacity: 1;
                    transform: translateY(-15px) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-30px) scale(0.8);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Load all featured mods
async function loadFeaturedMods() {
    try {
        // Fetch featured mods with their details
        const { data: featuredEntries, error: featuredError } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .select('*')
            .order('created_at', { ascending: false });

        if (featuredError) {
            console.error('Error fetching featured mods:', featuredError);
            featuredModsList.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل المودات المميزة</div>';
            return;
        }

        if (!featuredEntries || featuredEntries.length === 0) {
            featuredModsList.innerHTML = '<div class="empty-message">لا توجد مودات مميزة حالياً</div>';
            featuredMods = [];
            return;
        }

        // Extract mod IDs
        const modIds = featuredEntries.map(entry => entry.mod_id);

        // Fetch mod details
        const { data: mods, error: modsError } = await supabaseClient
            .from(MODS_TABLE)
            .select('*')
            .in('id', modIds);

        if (modsError) {
            console.error('Error fetching mod details:', modsError);
            featuredModsList.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل تفاصيل المودات</div>';
            return;
        }

        // Combine data
        featuredMods = featuredEntries.map(featuredEntry => {
            const modDetails = mods.find(mod => mod.id === featuredEntry.mod_id);
            return {
                ...featuredEntry,
                modDetails
            };
        }).filter(item => item.modDetails); // Filter out any entries where mod details weren't found

        // Render featured mods
        renderFeaturedMods();
    } catch (error) {
        console.error('Unexpected error in loadFeaturedMods:', error);
        featuredModsList.innerHTML = '<div class="error-message">حدث خطأ غير متوقع</div>';
    }
}

// Render featured mods list
function renderFeaturedMods() {
    if (featuredMods.length === 0) {
        featuredModsList.innerHTML = '<div class="empty-message">لا توجد مودات مميزة حالياً</div>';
        return;
    }

    let html = '';

    featuredMods.forEach(item => {
        const mod = item.modDetails;
        const mainImage = mod.images && mod.images.length > 0 ? mod.images[0] : '../image/placeholder.svg';

        html += `
            <div class="featured-item" data-id="${item.id}" data-mod-id="${mod.id}">
                <img src="${mainImage}" alt="${mod.name || 'Unnamed Mod'}">
                <div class="featured-item-info">
                    <div class="featured-item-name">${mod.name || 'Unnamed Mod'}</div>
                    <div class="featured-item-category">${mod.category || 'غير مصنف'}</div>
                </div>
                <div class="featured-item-actions">
                    <button class="toggle-status ${item.is_active ? 'active' : 'inactive'}"
                            onclick="toggleFeaturedStatus('${item.id}', ${!item.is_active})">
                        ${item.is_active ? 'مفعل' : 'معطل'}
                    </button>
                    <button class="remove-featured" onclick="removeFeaturedMod('${item.id}')">حذف</button>
                </div>
            </div>
        `;
    });

    featuredModsList.innerHTML = html;
}

// Load all mods for search
async function loadAllMods() {
    try {
        const { data, error } = await supabaseClient
            .from(MODS_TABLE)
            .select('*')
            .eq('category', 'Addons') // Only fetch Addons category
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching mods:', error);
            return;
        }

        allMods = data || [];
    } catch (error) {
        console.error('Unexpected error in loadAllMods:', error);
    }
}

// Handle search input
function handleSearch() {
    const query = searchInput.value.trim();

    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // Set new timeout to avoid too many searches while typing
    searchTimeout = setTimeout(() => {
        if (query.length < 2) {
            searchResults.innerHTML = '<div class="empty-message">ابدأ البحث لعرض النتائج</div>';
            return;
        }

        // Filter mods based on search query
        const filteredMods = allMods.filter(mod => {
            return mod.name && mod.name.toLowerCase().includes(query.toLowerCase());
        });

        renderSearchResults(filteredMods);
    }, 300);
}

// Render search results
function renderSearchResults(mods) {
    if (mods.length === 0) {
        searchResults.innerHTML = '<div class="empty-message">لا توجد نتائج مطابقة</div>';
        return;
    }

    let html = '';

    mods.forEach(mod => {
        const mainImage = mod.images && mod.images.length > 0 ? mod.images[0] : '../image/placeholder.svg';

        // Check if mod is already in featured list
        const isAlreadyFeatured = featuredMods.some(item => item.mod_id === mod.id);

        if (!isAlreadyFeatured) {
            html += `
                <div class="mod-item" onclick="addToFeatured('${mod.id}')">
                    <img src="${mainImage}" alt="${mod.name || 'Unnamed Mod'}">
                    <div class="mod-item-info">
                        <div class="mod-item-name">${mod.name || 'Unnamed Mod'}</div>
                        <div class="mod-item-category">${mod.category || 'غير مصنف'}</div>
                    </div>
                </div>
            `;
        }
    });

    if (html === '') {
        searchResults.innerHTML = '<div class="empty-message">جميع المودات المعروضة مضافة بالفعل للقائمة المميزة</div>';
    } else {
        searchResults.innerHTML = html;
    }
}

// Add mod to featured list
async function addToFeatured(modId) {
    try {
        // Insert into featured_addons table
        const { data, error } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .insert([
                { mod_id: modId, is_active: true }
            ])
            .select();

        if (error) {
            console.error('Error adding mod to featured list:', error);
            alert('حدث خطأ أثناء إضافة المود للقائمة المميزة');
            return;
        }

        // Reload featured mods
        await loadFeaturedMods();

        // Clear search input and results
        searchInput.value = '';
        searchResults.innerHTML = '<div class="empty-message">ابدأ البحث لعرض النتائج</div>';

        // Show success message
        alert('تمت إضافة المود للقائمة المميزة بنجاح');
    } catch (error) {
        console.error('Unexpected error in addToFeatured:', error);
        alert('حدث خطأ غير متوقع');
    }
}

// Toggle featured status (active/inactive)
async function toggleFeaturedStatus(id, newStatus) {
    try {
        const { error } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .update({ is_active: newStatus })
            .eq('id', id);

        if (error) {
            console.error('Error updating featured status:', error);
            alert('حدث خطأ أثناء تحديث حالة المود');
            return;
        }

        // Update local data
        const index = featuredMods.findIndex(item => item.id === id);
        if (index !== -1) {
            featuredMods[index].is_active = newStatus;
        }

        // Re-render featured mods
        renderFeaturedMods();
    } catch (error) {
        console.error('Unexpected error in toggleFeaturedStatus:', error);
        alert('حدث خطأ غير متوقع');
    }
}

// Remove mod from featured list
async function removeFeaturedMod(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المود من القائمة المميزة؟')) {
        return;
    }

    try {
        const { error } = await supabaseClient
            .from(FEATURED_ADDONS_TABLE)
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error removing featured mod:', error);
            alert('حدث خطأ أثناء حذف المود من القائمة المميزة');
            return;
        }

        // Reload featured mods
        await loadFeaturedMods();

        // Show success message
        alert('تم حذف المود من القائمة المميزة بنجاح');
    } catch (error) {
        console.error('Unexpected error in removeFeaturedMod:', error);
        alert('حدث خطأ غير متوقع');
    }
}
