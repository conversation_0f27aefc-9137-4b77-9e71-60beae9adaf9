<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B700%3B900&family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
      /* Custom styles for user settings */
      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, #ffd700, #ff8c00);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        opacity: 0.7;
      }

      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
        10% { opacity: 0.7; }
        50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
        90% { opacity: 0.7; }
      }

      .settings-section {
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        margin-bottom: 8px;
      }

      .settings-section:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }

      /* Mobile responsive improvements */
      @media (max-width: 768px) {
        .settings-section {
          margin-bottom: 6px;
          border-radius: 8px;
        }

        .settings-section:hover {
          transform: none;
        }

        .advanced-buttons {
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
        }

        .advanced-btn {
          padding: 8px 12px;
          font-size: 0.8rem;
        }
      }

      @media (max-width: 480px) {
        .advanced-buttons {
          grid-template-columns: 1fr;
        }
      }

      .success-message {
        background: linear-gradient(135deg, #4caf50, #45a049);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        margin: 10px 0;
        display: none;
        animation: slideIn 0.3s ease;
      }

      .error-message {
        background: linear-gradient(135deg, #f44336, #d32f2f);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        margin: 10px 0;
        display: none;
        animation: slideIn 0.3s ease;
      }

      @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .advanced-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin-top: 20px;
      }

      .advanced-btn {
        background: linear-gradient(135deg, #363636, #2a2a2a);
        border: 1px solid rgba(255, 215, 0, 0.3);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
      }

      .advanced-btn:hover {
        background: linear-gradient(135deg, #ffd700, #ffb300);
        color: #000;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
      }

      .developer-mode {
        border: 2px solid #ffd700 !important;
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
      }
    </style>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#1a1a1a] dark group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div id="particles" class="absolute inset-0 pointer-events-none"></div>
      <div class="layout-container flex h-full grow flex-col">
        <!-- شريط إضافي فوق شريط الإعدادات -->
        <div class="flex items-center whitespace-nowrap border-b border-solid border-b-[#363636] px-4 md:px-10 py-4 bg-[#1a1a1a]">
          <!-- هذا الشريط لا يحتوي على أي أزرار، فقط مساحة فارغة -->
        </div>
        
        <!-- شريط الإعدادات الأصلي -->
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#363636] px-4 md:px-10 py-3">
          <div class="flex items-center gap-4 text-white">
            <h2 class="text-white text-lg md:text-xl font-bold leading-tight tracking-[-0.015em]" data-en="Settings" data-ar="الإعدادات">Settings</h2>
          </div>
          <div class="flex justify-end">
            <button
              id="backButton"
              onclick="goBack()"
              class="flex cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#363636] text-white gap-2 text-sm font-bold leading-normal tracking-[0.015em] px-3 md:px-4 hover:bg-[#4a4a4a] transition-colors"
            >
              <div class="text-white" data-icon="ArrowLeft" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"
                  ></path>
                </svg>
              </div>
              <span data-en="Back" data-ar="رجوع">Back</span>
            </button>
          </div>
        </header>
        <div class="px-4 md:px-20 lg:px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col w-full max-w-4xl py-5 flex-1">

            <!-- Success and Error Messages -->
            <div id="successMessage" class="success-message mx-4"></div>
            <div id="errorMessage" class="error-message mx-4"></div>

            <h3 id="userSettingsHeader" class="text-white text-xl md:text-2xl font-bold leading-tight tracking-[-0.015em] px-4 pb-4 pt-4" data-en="User Settings" data-ar="إعدادات المستخدم">User Settings</h3>
            
            <!-- User ID Section -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="IdentificationCard" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M200,48H56A16,16,0,0,0,40,64V192a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V64A16,16,0,0,0,200,48Zm0,144H56V64H200V192ZM80,160a8,8,0,0,1,0-16h96a8,8,0,0,1,0,16Zm0-32a8,8,0,0,1,0-16h96a8,8,0,0,1,0,16Zm56-32a24,24,0,1,1-24-24A24,24,0,0,1,136,96Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center flex-1">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="User ID" data-ar="معرف المستخدم">User ID</p>
                  <div class="flex items-center mt-1">
                    <input id="userId" type="text" readonly class="bg-[#2a2a2a] text-[#adadad] text-sm font-mono leading-normal py-1 px-2 rounded border border-[#363636] flex-1 focus:outline-none" value="Loading...">
                    <button id="copyUserId" class="ml-2 bg-[#363636] hover:bg-[#4a4a4a] text-white p-1 rounded transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32ZM160,208H48V96H160Zm48-48H176V88a8,8,0,0,0-8-8H96V48H208Z"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Language Section -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Globe" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24ZM101.63,168h52.74C149,186.34,140,202.87,128,215.89,116,202.87,107,186.34,101.63,168ZM98,152a145.72,145.72,0,0,1,0-48h60a145.72,145.72,0,0,1,0,48ZM40,128a87.61,87.61,0,0,1,3.33-24H81.79a161.79,161.79,0,0,0,0,48H43.33A87.61,87.61,0,0,1,40,128ZM154.37,88H101.63C107,69.66,116,53.13,128,40.11,140,53.13,149,69.66,154.37,88Zm19.84,16h38.46a88.15,88.15,0,0,1,0,48H174.21a161.79,161.79,0,0,0,0-48Zm32.16-16H170.94a142.39,142.39,0,0,0-20.26-45A88.37,88.37,0,0,1,206.37,88ZM105.32,43A142.39,142.39,0,0,0,85.06,88H49.63A88.37,88.37,0,0,1,105.32,43ZM49.63,168H85.06a142.39,142.39,0,0,0,20.26,45A88.37,88.37,0,0,1,49.63,168Zm101.05,45a142.39,142.39,0,0,0,20.26-45h35.43A88.37,88.37,0,0,1,150.68,213Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Language" data-ar="اللغة">Language</p>
                  <select id="appLanguage" class="bg-[#1a1a1a] text-[#adadad] text-sm font-normal leading-normal line-clamp-2 focus:outline-none">
                    <option value="en">English</option>
                    <option value="ar">العربية</option>
                  </select>
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <!-- New: Description Type Setting -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="TextAa" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M216,48H40A16,16,0,0,0,24,64V192a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V64A16,16,0,0,0,216,48ZM40,64H216V192H40ZM128,80a8,8,0,0,1,8,8v80a8,8,0,0,1-16,0V88A8,8,0,0,1,128,80Zm40,0a8,8,0,0,1,8,8v80a8,8,0,0,1-16,0V88A8,8,0,0,1,168,80Zm-80,0a8,8,0,0,1,8,8v80a8,8,0,0,1-16,0V88A8,8,0,0,1,88,80Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Description Type" data-ar="نوع الوصف">Description Type</p>
                  <select id="descriptionType" class="bg-[#1a1a1a] text-[#adadad] text-sm font-normal leading-normal line-clamp-2 focus:outline-none">
                    <option value="official" data-en="Official" data-ar="رسمي">Official</option>
                    <option value="community" data-en="Community" data-ar="مجتمعي">Community</option>
                  </select>
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <h3 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4" data-en="Content Filtering" data-ar="فلترة المحتوى">Content Filtering</h3>
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="FilmSlate" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M216,104H102.09L210,75.51a8,8,0,0,0,5.68-9.84l-8.16-30a15.93,15.93,0,0,0-19.42-11.13L35.81,64.74a15.75,15.75,0,0,0-9.7,7.4,15.51,15.51,0,0,0-1.55,12L32,111.56c0,.14,0,.29,0,.44v88a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V112A8,8,0,0,0,216,104ZM192.16,40l6,22.07-22.62,6L147.42,51.83Zm-66.69,17.6,28.12,16.24-36.94,9.75L88.53,67.37Zm-79.4,44.62-6-22.08,26.5-7L94.69,89.4ZM208,200H48V120H208v80Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Content Filtering" data-ar="فلترة المحتوى">Content Filtering</p>
                  <input type="checkbox" id="contentFilter" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <h3 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4" data-en="Download Settings" data-ar="إعدادات التحميل">Download Settings</h3>
            <div class="flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="DownloadSimple" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M224,152v56a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V152a8,8,0,0,1,16,0v56H208V152a8,8,0,0,1,16,0Zm-101.66,5.66a8,8,0,0,0,11.32,0l40-40a8,8,0,0,0-11.32-11.32L136,132.69V40a8,8,0,0,0-16,0v92.69L93.66,106.34a8,8,0,0,0-11.32,11.32Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Download Quality" data-ar="جودة التحميل">Download Quality</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="Auto" data-ar="تلقائي">Auto</p>
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Folder" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M216,72H131.31L104,44.69A15.86,15.86,0,0,0,92.69,40H40A16,16,0,0,0,24,56V200.62A15.4,15.4,0,0,0,39.38,216H216.89A15.13,15.13,0,0,0,232,200.89V88A16,16,0,0,0,216,72ZM40,56H92.69l16,16H40ZM216,200H40V88H216Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1">Download Location</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2">Auto</p>
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <!-- New: WiFi Only Download Setting -->
            <div class="flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="WifiHigh" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M224,104a8,8,0,0,0-8,8v8a8,8,0,0,1-16,0v-8a8,8,0,0,0-8-8H56a8,8,0,0,0-8,8v8a8,8,0,0,1-16,0v-8a8,8,0,0,0-8-8A128.1,128.1,0,0,1,128,32,128.1,128.1,0,0,1,224,104ZM128,120a48,48,0,0,0-48,48v8a8,8,0,0,0,16,0v-8a32,32,0,0,1,64,0v8a8,8,0,0,0,16,0v-8A48,48,0,0,0,128,120Zm-40,48a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-8A8,8,0,0,0,88,168Zm80,0a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-8A8,8,0,0,0,168,168Zm-40,48a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-8A8,8,0,0,0,128,216Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="WiFi Only Download" data-ar="التحميل عبر الواي فاي فقط">WiFi Only Download</p>
                  <input type="checkbox" id="wifiOnlyDownload" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <!-- New: Auto Delete Files Setting -->
            <div class="flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Trash" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M216,48H176V40a24,24,0,0,0-24-24H104A24,24,0,0,0,80,40v8H40a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16ZM96,40a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96ZM200,80H56V208a16,16,0,0,0,16,16H184a16,16,0,0,0,16-16ZM72,96v96a8,8,0,0,1-16,0V96a8,8,0,0,1,16,0Zm40,0v96a8,8,0,0,1-16,0V96a8,8,0,0,1,16,0Zm40,0v96a8,8,0,0,1-16,0V96a8,8,0,0,1,16,0Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1">Auto Delete Files</p>
                  <input type="checkbox" id="autoDeleteFiles" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
                </div>
              </div>
              <div class="shrink-0">
                <div class="text-white flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Data Saving Settings Section -->
            <h3 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4" data-en="Data Saving Settings" data-ar="إعدادات توفير البيانات">Data Saving Settings</h3>

            <!-- Enable Data Saving -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Database" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M128,24C74.17,24,32,48.6,32,80v96c0,31.4,42.17,56,96,56s96-24.6,96-56V80C224,48.6,181.83,24,128,24ZM208,80c0,9.62-18.89,24.54-80,24.54S48,89.62,48,80s18.89-24.54,80-24.54S208,70.38,208,80ZM48,105.34c14.2,8.78,37.86,15.21,80,15.21s65.8-6.43,80-15.21V128c0,9.62-18.89,24.54-80,24.54S48,137.62,48,128Zm160,71.32c-14.2,8.78-37.86,15.21-80,15.21s-65.8-6.43-80-15.21V200c0,9.62,18.89,24.54,80,24.54s80-14.92,80-24.54Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Enable Data Saving" data-ar="تفعيل توفير البيانات">Enable Data Saving</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="Reduces data usage by up to 70%" data-ar="يقلل استهلاك البيانات بنسبة تصل إلى 70%">Reduces data usage by up to 70%</p>
                </div>
              </div>
              <div class="shrink-0">
                <input type="checkbox" id="enableDataSaving" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
              </div>
            </div>

            <!-- Aggressive Mode -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Lightning" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M215.79,118.17a8,8,0,0,0-5-5.66L153.18,90.9l14.66-73.33a8,8,0,0,0-13.69-7l-112,120a8,8,0,0,0,3,13l57.63,21.61L88.16,238.43a8,8,0,0,0,13.69,7l112-120A8,8,0,0,0,215.79,118.17ZM109.37,214l10.47-52.38a8,8,0,0,0-5-9.06L62,132.71l84.62-90.66L136.16,94.43a8,8,0,0,0,5,9.06l52.8,19.8Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Aggressive Mode" data-ar="الوضع المتقدم">Aggressive Mode</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="Maximum data saving for limited plans" data-ar="توفير أقصى للباقات المحدودة">Maximum data saving for limited plans</p>
                </div>
              </div>
              <div class="shrink-0">
                <input type="checkbox" id="aggressiveMode" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
              </div>
            </div>

            <!-- Low Data Mode -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="DeviceMobile" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M176,16H80A24,24,0,0,0,56,40V216a24,24,0,0,0,24,24h96a24,24,0,0,0,24-24V40A24,24,0,0,0,176,16ZM72,64H184V192H72ZM80,32h96a8,8,0,0,1,8,8V48H72V40A8,8,0,0,1,80,32ZM176,224H80a8,8,0,0,1-8-8v-8H184v8A8,8,0,0,1,176,224Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Low Data Mode" data-ar="وضع البيانات المنخفضة">Low Data Mode</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="For slow connections and very limited data" data-ar="للاتصالات البطيئة والباقات المحدودة جداً">For slow connections and very limited data</p>
                </div>
              </div>
              <div class="shrink-0">
                <input type="checkbox" id="lowDataMode" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
              </div>
            </div>

            <!-- Auto Refresh on WiFi -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="WifiHigh" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M140,204a12,12,0,1,1-12-12A12,12,0,0,1,140,204ZM237.08,80.61a8,8,0,0,1-11.31.87C207.86,68.84,169.33,52,128,52S48.14,68.84,30.23,81.48a8,8,0,0,1-10.44-12.18C40.17,56.34,82.79,36,128,36s87.83,20.34,108.21,33.3A8,8,0,0,1,237.08,80.61ZM201.38,120.46a8,8,0,1,0-10.76,11.88C202.91,142.24,216,156.75,216,172a8,8,0,0,0,16,0C232,147.81,215.07,129.3,201.38,120.46ZM65.38,132.34a8,8,0,1,0-10.76-11.88C40.93,129.3,24,147.81,24,172a8,8,0,0,0,16,0C40,156.75,53.09,142.24,65.38,132.34ZM176,172a8,8,0,0,1-8,8H88a8,8,0,0,1,0-16h80A8,8,0,0,1,176,172Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Auto Refresh on WiFi" data-ar="تحديث تلقائي مع الواي فاي">Auto Refresh on WiFi</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="Automatically refresh data when connected to WiFi" data-ar="تحديث البيانات تلقائياً عند الاتصال بالواي فاي">Automatically refresh data when connected to WiFi</p>
                </div>
              </div>
              <div class="shrink-0">
                <input type="checkbox" id="autoRefreshWifi" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
              </div>
            </div>

            <!-- Show Data Usage Stats -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="ChartBar" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M224,200h-8V40a8,8,0,0,0-8-8H152a8,8,0,0,0-8,8V80H96a8,8,0,0,0-8,8v40H40a8,8,0,0,0-8,8v64H24a8,8,0,0,0,0,16H224a8,8,0,0,0,0-16ZM160,48h40V200H160ZM104,96h40V200H104ZM48,144H88v56H48Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Show Usage Statistics" data-ar="عرض إحصائيات الاستهلاك">Show Usage Statistics</p>
                  <p class="text-[#adadad] text-sm font-normal leading-normal line-clamp-2" data-en="Display data saving statistics in console" data-ar="عرض إحصائيات توفير البيانات في وحدة التحكم">Display data saving statistics in console</p>
                </div>
              </div>
              <div class="shrink-0">
                <input type="checkbox" id="showDataStats" class="form-checkbox h-5 w-5 text-blue-600 bg-[#363636] border-[#363636] rounded focus:ring-blue-500">
              </div>
            </div>

            <!-- Cache Size Slider -->
            <div class="settings-section flex items-center gap-4 bg-[#1a1a1a] px-4 min-h-[72px] py-2 justify-between">
              <div class="flex items-center gap-4">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="HardDrives" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M224,64H32A16,16,0,0,0,16,80v48a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V80A16,16,0,0,0,224,64ZM32,128V80H224v48ZM224,160H32a16,16,0,0,0-16,16v48a16,16,0,0,0,16,16H224a16,16,0,0,0,16-16V176A16,16,0,0,0,224,160ZM32,224V176H224v48ZM192,96a8,8,0,1,1-8,8A8,8,0,0,1,192,96Zm0,96a8,8,0,1,1-8,8A8,8,0,0,1,192,192Z"></path>
                  </svg>
                </div>
                <div class="flex flex-col justify-center flex-1">
                  <p class="text-white text-base font-medium leading-normal line-clamp-1" data-en="Cache Size Limit" data-ar="حد حجم التخزين المؤقت">Cache Size Limit</p>
                  <div class="flex items-center gap-3 mt-2">
                    <input type="range" id="maxCacheSize" min="10" max="100" value="50" class="flex-1 h-2 bg-[#363636] rounded-lg appearance-none cursor-pointer">
                    <span id="cacheValue" class="text-[#ffd700] font-bold text-sm min-w-[60px]">50 MB</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Data Saving Statistics Display -->
            <div class="settings-section bg-[#1a1a1a] px-4 py-4">
              <div class="flex items-center gap-4 mb-3">
                <div class="text-white flex items-center justify-center rounded-lg bg-[#363636] shrink-0 size-12" data-icon="Info" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm16-40a8,8,0,0,1-8,8,16,16,0,0,1-16-16V128a8,8,0,0,1,0-16,16,16,0,0,1,16,16v40A8,8,0,0,1,144,176ZM112,84a12,12,0,1,1,12,12A12,12,0,0,1,112,84Z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-white text-base font-medium leading-normal" data-en="Data Saving Statistics" data-ar="إحصائيات توفير البيانات">Data Saving Statistics</p>
                </div>
              </div>
              <div id="dataSavingStats" class="bg-[#2a2a2a] rounded-lg p-4 border border-[#363636]">
                <p class="text-[#adadad] text-sm" data-en="Loading statistics..." data-ar="جاري تحميل الإحصائيات...">Loading statistics...</p>
              </div>
            </div>

            <!-- Advanced Action Buttons -->
            <div class="px-4 pb-4">
              <h3 class="text-white text-lg font-bold leading-tight tracking-[-0.015em] pb-2 pt-4" data-en="Advanced Actions" data-ar="الإجراءات المتقدمة">Advanced Actions</h3>
              <div class="advanced-buttons">
                <button onclick="clearCache()" class="advanced-btn">
                  <span data-en="🗑️ Clear Cache" data-ar="🗑️ مسح الذاكرة">🗑️ Clear Cache</span>
                </button>
                <button onclick="exportSettings()" class="advanced-btn">
                  <span data-en="📤 Export Settings" data-ar="📤 تصدير الإعدادات">📤 Export Settings</span>
                </button>
                <button onclick="document.getElementById('importFile').click()" class="advanced-btn">
                  <span data-en="📥 Import Settings" data-ar="📥 استيراد الإعدادات">📥 Import Settings</span>
                </button>
                <button onclick="clearAllData()" class="advanced-btn">
                  <span data-en="⚠️ Clear All Data" data-ar="⚠️ مسح جميع البيانات">⚠️ Clear All Data</span>
                </button>
              </div>
              <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importSettings(this)">
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row justify-end gap-3 p-4">
              <button id="saveSettingsBtn" class="flex items-center justify-center rounded-xl h-12 bg-[#363636] text-white gap-2 text-sm font-bold leading-normal tracking-[0.015em] px-6 hover:bg-[#4a4a4a] transition-colors">
                <span data-en="💾 Save Changes" data-ar="💾 حفظ التغييرات">💾 Save Changes</span>
              </button>
              <button id="resetSettingsBtn" class="flex items-center justify-center rounded-xl h-12 bg-red-600 text-white gap-2 text-sm font-bold leading-normal tracking-[0.015em] px-6 hover:bg-red-700 transition-colors">
                <span data-en="🔄 Reset All Settings" data-ar="🔄 إعادة تعيين جميع الإعدادات">🔄 Reset All Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="user-settings.js"></script>
  </body>
</html>
