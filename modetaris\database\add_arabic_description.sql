-- SQL script to add Arabic description column to mods table
-- This allows storing both English and Arabic descriptions for each mod

-- Add Arabic description column to mods table
ALTER TABLE mods 
ADD COLUMN IF NOT EXISTS description_ar TEXT;

-- Add comment for documentation
COMMENT ON COLUMN mods.description_ar IS 'Arabic description of the mod';

-- Update existing mods with sample Arabic descriptions (optional)
-- You can customize these or remove this section if you prefer to add descriptions manually

-- Example updates for common mod types (customize as needed)
UPDATE mods 
SET description_ar = CASE 
    WHEN category = 'Shaders' THEN 
        COALESCE(description_ar, 'شيدر جميل يحسن من جودة الرسوميات في ماين كرافت. يتطلب إصدار ماين كرافت المُعدّل للعمل بشكل صحيح.')
    WHEN category = 'Addons' THEN 
        COALESCE(description_ar, 'إضافة رائعة تضيف محتوى جديد ومثير إلى عالم ماين كرافت. سهلة التثبيت والاستخدام.')
    WHEN category = 'Texture Pack' THEN 
        COALESCE(description_ar, 'حزمة نسيج عالية الجودة تغير مظهر الكتل والعناصر في اللعبة لتجربة بصرية محسنة.')
    WHEN category = 'Maps' THEN 
        COALESCE(description_ar, 'خريطة مصممة بعناية توفر تجربة لعب فريدة ومليئة بالمغامرات والتحديات.')
    WHEN category = 'Seeds' THEN 
        COALESCE(description_ar, 'بذرة عالم مميزة تولد تضاريس وهياكل رائعة لاستكشافها في ماين كرافت.')
    ELSE 
        COALESCE(description_ar, description, 'وصف غير متوفر باللغة العربية.')
END
WHERE description_ar IS NULL;

-- Create a function to get mod description based on language preference
CREATE OR REPLACE FUNCTION get_mod_description(
    p_mod_id UUID,
    p_language VARCHAR(10) DEFAULT 'en'
)
RETURNS TEXT AS $$
DECLARE
    mod_desc_en TEXT;
    mod_desc_ar TEXT;
BEGIN
    -- Get both descriptions
    SELECT description, description_ar 
    INTO mod_desc_en, mod_desc_ar
    FROM mods 
    WHERE id = p_mod_id;
    
    -- Return appropriate description based on language
    IF p_language = 'ar' AND mod_desc_ar IS NOT NULL AND mod_desc_ar != '' THEN
        RETURN mod_desc_ar;
    ELSE
        RETURN COALESCE(mod_desc_en, 'No description available.');
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create a view that includes localized descriptions
CREATE OR REPLACE VIEW mods_with_localized_descriptions AS
SELECT 
    id,
    name,
    description as description_en,
    description_ar,
    category,
    image_urls,
    version,
    size,
    download_url,
    downloads,
    likes,
    clicks,
    creator_name,
    creator_contact_info,
    creator_social_channels,
    custom_social_site_name,
    custom_social_site_url,
    created_at,
    -- Helper columns for easy access
    CASE 
        WHEN description_ar IS NOT NULL AND description_ar != '' 
        THEN description_ar 
        ELSE description 
    END as description_ar_fallback,
    
    CASE 
        WHEN description IS NOT NULL AND description != '' 
        THEN description 
        ELSE 'No description available.' 
    END as description_en_fallback
FROM mods;

-- Function to get mods with descriptions in specified language
CREATE OR REPLACE FUNCTION get_mods_by_language(
    p_language VARCHAR(10) DEFAULT 'en',
    p_category VARCHAR(50) DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    category TEXT,
    image_urls JSONB,
    version TEXT,
    size TEXT,
    download_url TEXT,
    downloads BIGINT,
    likes BIGINT,
    clicks BIGINT,
    creator_name TEXT,
    creator_contact_info TEXT,
    creator_social_channels JSONB,
    custom_social_site_name TEXT,
    custom_social_site_url TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.name,
        CASE 
            WHEN p_language = 'ar' AND m.description_ar IS NOT NULL AND m.description_ar != ''
            THEN m.description_ar
            ELSE COALESCE(m.description, 'No description available.')
        END as description,
        m.category,
        m.image_urls,
        m.version,
        m.size,
        m.download_url,
        m.downloads,
        m.likes,
        m.clicks,
        m.creator_name,
        m.creator_contact_info,
        m.creator_social_channels,
        m.custom_social_site_name,
        m.custom_social_site_url,
        m.created_at
    FROM mods m
    WHERE (p_category IS NULL OR m.category = p_category)
    ORDER BY m.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function to update mod descriptions (both languages)
CREATE OR REPLACE FUNCTION update_mod_descriptions(
    p_mod_id UUID,
    p_description_en TEXT DEFAULT NULL,
    p_description_ar TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE mods 
    SET 
        description = COALESCE(p_description_en, description),
        description_ar = COALESCE(p_description_ar, description_ar)
    WHERE id = p_mod_id;
    
    RETURN FOUND;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance on the new column
CREATE INDEX IF NOT EXISTS idx_mods_description_ar 
ON mods USING gin(to_tsvector('arabic', description_ar));

-- Create a trigger to automatically update search vectors when descriptions change
CREATE OR REPLACE FUNCTION update_mod_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    -- This can be extended to update search functionality
    -- For now, it's a placeholder for future search improvements
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger (optional, for future search enhancements)
-- DROP TRIGGER IF EXISTS trigger_update_mod_search_vector ON mods;
-- CREATE TRIGGER trigger_update_mod_search_vector
--     BEFORE UPDATE OF description, description_ar ON mods
--     FOR EACH ROW
--     EXECUTE FUNCTION update_mod_search_vector();

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, UPDATE ON mods TO your_app_user;
-- GRANT SELECT ON mods_with_localized_descriptions TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_mod_description(UUID, VARCHAR) TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_mods_by_language(VARCHAR, VARCHAR, INTEGER, INTEGER) TO your_app_user;
-- GRANT EXECUTE ON FUNCTION update_mod_descriptions(UUID, TEXT, TEXT) TO your_app_user;
