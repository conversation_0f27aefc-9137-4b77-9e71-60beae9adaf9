-- جدول الإعلانات الاحتياطية
-- Backup Ads Table for fallback when <PERSON><PERSON><PERSON> fails

CREATE TABLE IF NOT EXISTS backup_ads (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    ad_type VARCHAR(20) NOT NULL CHECK (ad_type IN ('image', 'video')),
    media_url TEXT NOT NULL,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
    duration INTEGER DEFAULT 5 CHECK (duration >= 3 AND duration <= 30),
    click_action VARCHAR(20) DEFAULT 'none' CHECK (click_action IN ('none', 'url', 'close')),
    click_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Statistics
    views_count INTEGER DEFAULT 0,
    clicks_count INTEGER DEFAULT 0,
    
    -- Targeting (optional for future use)
    target_categories TEXT[], -- Array of categories to show this ad for
    target_user_types TEXT[], -- Array of user types (new, returning, premium, etc.)
    
    -- Scheduling (optional for future use)
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    file_size INTEGER, -- File size in bytes
    file_format VARCHAR(10), -- File format (jpg, png, mp4, etc.)
    dimensions VARCHAR(20), -- Image/video dimensions (e.g., "1920x1080")
    
    CONSTRAINT valid_click_url CHECK (
        (click_action = 'url' AND click_url IS NOT NULL) OR 
        (click_action != 'url')
    )
);

-- إنشاء فهارس لتحسين الأداء
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_backup_ads_active ON backup_ads(is_active);
CREATE INDEX IF NOT EXISTS idx_backup_ads_priority ON backup_ads(priority DESC);
CREATE INDEX IF NOT EXISTS idx_backup_ads_type ON backup_ads(ad_type);
CREATE INDEX IF NOT EXISTS idx_backup_ads_created ON backup_ads(created_at DESC);

-- إنشاء فهرس مركب للاستعلامات الشائعة
-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_backup_ads_active_priority ON backup_ads(is_active, priority DESC, created_at DESC);

-- إنشاء trigger لتحديث updated_at تلقائياً
-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_backup_ads_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_backup_ads_updated_at
    BEFORE UPDATE ON backup_ads
    FOR EACH ROW
    EXECUTE FUNCTION update_backup_ads_updated_at();

-- إضافة تعليقات للجدول والأعمدة
-- Add comments for table and columns
COMMENT ON TABLE backup_ads IS 'جدول الإعلانات الاحتياطية التي تظهر عند فشل إعلانات AdMob';
COMMENT ON COLUMN backup_ads.id IS 'المعرف الفريد للإعلان';
COMMENT ON COLUMN backup_ads.title IS 'عنوان الإعلان';
COMMENT ON COLUMN backup_ads.description IS 'وصف الإعلان (اختياري)';
COMMENT ON COLUMN backup_ads.ad_type IS 'نوع الإعلان: image أو video';
COMMENT ON COLUMN backup_ads.media_url IS 'رابط ملف الوسائط (صورة أو فيديو)';
COMMENT ON COLUMN backup_ads.priority IS 'أولوية عرض الإعلان (1-10، 10 الأعلى)';
COMMENT ON COLUMN backup_ads.duration IS 'مدة عرض الإعلان بالثواني (3-30)';
COMMENT ON COLUMN backup_ads.click_action IS 'إجراء النقر: none, url, close';
COMMENT ON COLUMN backup_ads.click_url IS 'رابط الوجهة عند النقر (مطلوب إذا كان click_action = url)';
COMMENT ON COLUMN backup_ads.is_active IS 'حالة تفعيل الإعلان';
COMMENT ON COLUMN backup_ads.views_count IS 'عدد مرات عرض الإعلان';
COMMENT ON COLUMN backup_ads.clicks_count IS 'عدد مرات النقر على الإعلان';
COMMENT ON COLUMN backup_ads.target_categories IS 'فئات المودات المستهدفة (اختياري)';
COMMENT ON COLUMN backup_ads.target_user_types IS 'أنواع المستخدمين المستهدفة (اختياري)';

-- إنشاء جدول إحصائيات الإعلانات الاحتياطية
-- Create backup ads statistics table
CREATE TABLE IF NOT EXISTS backup_ads_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ad_id UUID NOT NULL REFERENCES backup_ads(id) ON DELETE CASCADE,
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('view', 'click', 'close', 'error')),
    user_id VARCHAR(255), -- User identifier (if available)
    session_id VARCHAR(255), -- Session identifier
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Context information
    mod_id UUID, -- Which mod was being downloaded when ad was shown
    mod_category VARCHAR(100), -- Category of the mod
    user_agent TEXT, -- Browser/app user agent
    ip_address INET, -- User IP address (for analytics)
    
    -- Additional metadata
    metadata JSONB -- Flexible field for additional data
);

-- إنشاء فهارس لجدول الإحصائيات
-- Create indexes for statistics table
CREATE INDEX IF NOT EXISTS idx_backup_ads_stats_ad_id ON backup_ads_stats(ad_id);
CREATE INDEX IF NOT EXISTS idx_backup_ads_stats_event_type ON backup_ads_stats(event_type);
CREATE INDEX IF NOT EXISTS idx_backup_ads_stats_timestamp ON backup_ads_stats(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_backup_ads_stats_mod_id ON backup_ads_stats(mod_id);

-- إنشاء view لإحصائيات سريعة
-- Create view for quick statistics
CREATE OR REPLACE VIEW backup_ads_summary AS
SELECT 
    ba.id,
    ba.title,
    ba.ad_type,
    ba.priority,
    ba.is_active,
    ba.created_at,
    COALESCE(stats.total_views, 0) as total_views,
    COALESCE(stats.total_clicks, 0) as total_clicks,
    CASE 
        WHEN COALESCE(stats.total_views, 0) > 0 
        THEN ROUND((COALESCE(stats.total_clicks, 0)::DECIMAL / stats.total_views) * 100, 2)
        ELSE 0 
    END as click_rate_percentage
FROM backup_ads ba
LEFT JOIN (
    SELECT 
        ad_id,
        COUNT(CASE WHEN event_type = 'view' THEN 1 END) as total_views,
        COUNT(CASE WHEN event_type = 'click' THEN 1 END) as total_clicks
    FROM backup_ads_stats
    GROUP BY ad_id
) stats ON ba.id = stats.ad_id
ORDER BY ba.priority DESC, ba.created_at DESC;

-- إنشاء function للحصول على إعلان احتياطي عشوائي
-- Create function to get random backup ad
CREATE OR REPLACE FUNCTION get_random_backup_ad(
    p_ad_type VARCHAR(20) DEFAULT NULL,
    p_mod_category VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    description TEXT,
    ad_type VARCHAR(20),
    media_url TEXT,
    duration INTEGER,
    click_action VARCHAR(20),
    click_url TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ba.id,
        ba.title,
        ba.description,
        ba.ad_type,
        ba.media_url,
        ba.duration,
        ba.click_action,
        ba.click_url
    FROM backup_ads ba
    WHERE ba.is_active = true
        AND (p_ad_type IS NULL OR ba.ad_type = p_ad_type)
        AND (p_mod_category IS NULL OR ba.target_categories IS NULL OR p_mod_category = ANY(ba.target_categories))
        AND (ba.start_date IS NULL OR ba.start_date <= NOW())
        AND (ba.end_date IS NULL OR ba.end_date >= NOW())
    ORDER BY ba.priority DESC, RANDOM()
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- إنشاء function لتسجيل إحصائية
-- Create function to log ad event
CREATE OR REPLACE FUNCTION log_backup_ad_event(
    p_ad_id UUID,
    p_event_type VARCHAR(20),
    p_user_id VARCHAR(255) DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_mod_id UUID DEFAULT NULL,
    p_mod_category VARCHAR(100) DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO backup_ads_stats (
        ad_id, event_type, user_id, session_id, mod_id, 
        mod_category, user_agent, ip_address, metadata
    ) VALUES (
        p_ad_id, p_event_type, p_user_id, p_session_id, p_mod_id,
        p_mod_category, p_user_agent, p_ip_address, p_metadata
    );
    
    -- Update counters in main table for quick access
    IF p_event_type = 'view' THEN
        UPDATE backup_ads SET views_count = views_count + 1 WHERE id = p_ad_id;
    ELSIF p_event_type = 'click' THEN
        UPDATE backup_ads SET clicks_count = clicks_count + 1 WHERE id = p_ad_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- إدراج بعض البيانات التجريبية (اختياري)
-- Insert some sample data (optional)
/*
INSERT INTO backup_ads (title, description, ad_type, media_url, priority, duration, click_action) VALUES
('إعلان تجريبي 1', 'هذا إعلان تجريبي للصور', 'image', 'https://example.com/ad1.jpg', 5, 5, 'none'),
('إعلان تجريبي 2', 'هذا إعلان تجريبي للفيديو', 'video', 'https://example.com/ad2.mp4', 7, 10, 'none');
*/
