-- Fix Missing Tables and Columns
-- This SQL script creates all missing tables and columns that are causing 400 errors

-- 1. Create error_reports table if it doesn't exist
CREATE TABLE IF NOT EXISTS error_reports (
    id SERIAL PRIMARY KEY,
    category TEXT,
    "errorCode" TEXT,
    "errorMessage" TEXT,
    timestamp TIMESTAMP DEFAULT NOW(),
    "userAgent" TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for error_reports table
CREATE INDEX IF NOT EXISTS idx_error_reports_category ON error_reports(category);
CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_error_reports_created_at ON error_reports(created_at);

-- 2. Ensure mods table exists with all required columns
CREATE TABLE IF NOT EXISTS mods (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    image_url TEXT,
    image_urls TEXT[],
    category TEXT,
    downloads INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    creator_name <PERSON>EX<PERSON>,
    creator_social_media JSONB,
    is_featured BOOLEAN DEFAULT FALSE,
    is_popular BOOLEAN DEFAULT FALSE,
    download_link TEXT,
    backup_download_link TEXT,
    file_size TEXT,
    version TEXT,
    minecraft_version TEXT,
    tags TEXT[],
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT NOW()
);

-- Add missing columns to existing mods table
ALTER TABLE mods 
ADD COLUMN IF NOT EXISTS description_ar TEXT,
ADD COLUMN IF NOT EXISTS image_urls TEXT[],
ADD COLUMN IF NOT EXISTS creator_name TEXT,
ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS backup_download_link TEXT,
ADD COLUMN IF NOT EXISTS file_size TEXT,
ADD COLUMN IF NOT EXISTS version TEXT,
ADD COLUMN IF NOT EXISTS minecraft_version TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS review_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT NOW();

-- Create indexes for mods table
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at);
CREATE INDEX IF NOT EXISTS idx_mods_is_featured ON mods(is_featured);
CREATE INDEX IF NOT EXISTS idx_mods_is_popular ON mods(is_popular);
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes);
CREATE INDEX IF NOT EXISTS idx_mods_rating ON mods(rating);

-- 3. Create download_errors table for tracking download issues
CREATE TABLE IF NOT EXISTS download_errors (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    mod_name TEXT,
    original_url TEXT,
    fallback_url TEXT,
    error_message TEXT,
    error_code TEXT,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP DEFAULT NOW(),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP,
    admin_notes TEXT
);

-- Create indexes for download_errors table
CREATE INDEX IF NOT EXISTS idx_download_errors_mod_id ON download_errors(mod_id);
CREATE INDEX IF NOT EXISTS idx_download_errors_created_at ON download_errors(created_at);
CREATE INDEX IF NOT EXISTS idx_download_errors_resolved ON download_errors(resolved);

-- 4. Create system_health table for monitoring
CREATE TABLE IF NOT EXISTS system_health (
    id SERIAL PRIMARY KEY,
    check_type TEXT NOT NULL,
    status TEXT NOT NULL, -- 'healthy', 'warning', 'error'
    message TEXT,
    details JSONB,
    checked_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for system_health table
CREATE INDEX IF NOT EXISTS idx_system_health_check_type ON system_health(check_type);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health(status);
CREATE INDEX IF NOT EXISTS idx_system_health_checked_at ON system_health(checked_at);

-- 5. Create database_backups table for backup tracking
CREATE TABLE IF NOT EXISTS database_backups (
    id SERIAL PRIMARY KEY,
    backup_name TEXT NOT NULL,
    backup_type TEXT NOT NULL, -- 'full', 'incremental', 'table'
    file_path TEXT,
    file_size BIGINT,
    tables_included TEXT[],
    status TEXT DEFAULT 'in_progress', -- 'in_progress', 'completed', 'failed'
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Create indexes for database_backups table
CREATE INDEX IF NOT EXISTS idx_database_backups_backup_type ON database_backups(backup_type);
CREATE INDEX IF NOT EXISTS idx_database_backups_status ON database_backups(status);
CREATE INDEX IF NOT EXISTS idx_database_backups_created_at ON database_backups(created_at);

-- 6. Create performance_metrics table for monitoring
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name TEXT NOT NULL,
    metric_value DECIMAL,
    metric_unit TEXT,
    context JSONB,
    recorded_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance_metrics table
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recorded_at ON performance_metrics(recorded_at);

-- 7. Ensure all essential tables exist
CREATE TABLE IF NOT EXISTS user_languages (
    id SERIAL PRIMARY KEY,
    user_id TEXT,
    language TEXT DEFAULT 'ar',
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS banner_ads (
    id SERIAL PRIMARY KEY,
    title TEXT,
    image_url TEXT,
    click_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS featured_addons (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    position INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS free_addons (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    position INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS suggested_mods (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    position INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title TEXT,
    title_ar TEXT,
    content TEXT,
    content_ar TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER REFERENCES custom_mod_dialogs(id),
    mod_id INTEGER REFERENCES mods(id),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    copyright_text TEXT,
    copyright_text_ar TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS entry_subscription_ads (
    id SERIAL PRIMARY KEY,
    title TEXT,
    title_ar TEXT,
    description TEXT,
    description_ar TEXT,
    image_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS custom_sections (
    id SERIAL PRIMARY KEY,
    name TEXT,
    name_ar TEXT,
    icon_url TEXT,
    position INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS update_notifications (
    id SERIAL PRIMARY KEY,
    title TEXT,
    title_ar TEXT,
    message TEXT,
    message_ar TEXT,
    version TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS app_announcements (
    id SERIAL PRIMARY KEY,
    title TEXT,
    title_ar TEXT,
    message TEXT,
    message_ar TEXT,
    type TEXT DEFAULT 'info',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS drawer_links (
    id SERIAL PRIMARY KEY,
    title TEXT,
    title_ar TEXT,
    url TEXT,
    icon_url TEXT,
    position INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 8. Create RPC functions for common operations
CREATE OR REPLACE FUNCTION increment_clicks(mod_id_in INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET downloads = downloads + 1 
    WHERE id = mod_id_in;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_likes(mod_id_in INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE mods 
    SET likes = likes + 1 
    WHERE id = mod_id_in;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS VOID AS $$
BEGIN
    EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql;

-- 9. Insert some sample data to prevent empty table errors
INSERT INTO error_reports (category, "errorCode", "errorMessage", "userAgent") 
VALUES ('system', 'INIT', 'System initialization', 'System') 
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon;

-- Final message
SELECT 'All missing tables and columns have been created successfully!' as status;
