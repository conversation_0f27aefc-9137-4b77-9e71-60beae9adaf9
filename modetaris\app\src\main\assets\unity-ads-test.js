// Unity Ads Test Functions
// Add this to your webpage to test Unity Ads functionality

function testUnityAds() {
    console.log('🧪 Testing Unity Ads...');
    if (window.Android && window.Android.testUnityAds) {
        window.Android.testUnityAds();
    } else {
        console.error('❌ Android interface not available');
        alert('Android interface not available');
    }
}

function testRewardedAd() {
    console.log('🎬 Testing Rewarded Ad...');
    if (window.Android && window.Android.requestModDownloadWithAd) {
        // Test with dummy data
        window.Android.requestModDownloadWithAd('test_mod_id', 'Test Mod', 'https://example.com/test.zip');
    } else {
        console.error('❌ Android interface not available');
        alert('Android interface not available');
    }
}

function testShowRewardedAdDirect() {
    console.log('🎬 Testing Show Rewarded Ad Directly...');
    if (window.Android && window.Android.testShowRewardedAd) {
        window.Android.testShowRewardedAd();
    } else {
        console.error('❌ Android interface not available');
        alert('Android interface not available');
    }
}

// Add test buttons to the page
function addTestButtons() {
    const testContainer = document.createElement('div');
    testContainer.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: rgba(0,0,0,0.8);
        padding: 10px;
        border-radius: 5px;
        color: white;
        font-family: Arial, sans-serif;
        font-size: 12px;
    `;
    
    const testButton = document.createElement('button');
    testButton.textContent = 'Test Unity Ads';
    testButton.style.cssText = `
        background: #4CAF50;
        color: white;
        border: none;
        padding: 5px 10px;
        margin: 2px;
        border-radius: 3px;
        cursor: pointer;
    `;
    testButton.onclick = testUnityAds;
    
    const adButton = document.createElement('button');
    adButton.textContent = 'Test Rewarded Ad';
    adButton.style.cssText = `
        background: #2196F3;
        color: white;
        border: none;
        padding: 5px 10px;
        margin: 2px;
        border-radius: 3px;
        cursor: pointer;
    `;
    adButton.onclick = testRewardedAd;

    const directAdButton = document.createElement('button');
    directAdButton.textContent = 'Show Ad Direct';
    directAdButton.style.cssText = `
        background: #FF9800;
        color: white;
        border: none;
        padding: 5px 10px;
        margin: 2px;
        border-radius: 3px;
        cursor: pointer;
    `;
    directAdButton.onclick = testShowRewardedAdDirect;

    testContainer.appendChild(testButton);
    testContainer.appendChild(document.createElement('br'));
    testContainer.appendChild(adButton);
    testContainer.appendChild(document.createElement('br'));
    testContainer.appendChild(directAdButton);

    document.body.appendChild(testContainer);
    
    console.log('✅ Test buttons added to page');
}

// Auto-add test buttons when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addTestButtons);
} else {
    addTestButtons();
}

// Console commands for manual testing
console.log('🧪 Unity Ads Test Functions Available:');
console.log('- testUnityAds() - Test Unity Ads status');
console.log('- testRewardedAd() - Test rewarded ad flow');
console.log('- testShowRewardedAdDirect() - Show rewarded ad directly');
console.log('- addTestButtons() - Add test buttons to page');
