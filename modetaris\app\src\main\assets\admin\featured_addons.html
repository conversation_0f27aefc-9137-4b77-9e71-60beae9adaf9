<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المودات المميزة</title>
    <link rel="stylesheet" href="admin_style.css">
    <style>
        /* أنماط خاصة بصفحة المودات المميزة */
        .featured-addon-preview {
            background-color: #000;
            border: 2px solid #ffcc00;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 15px;
            position: relative;
            box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
            animation: previewGlow 2s infinite alternate;
        }

        @keyframes previewGlow {
            0% {
                box-shadow: 0 0 10px rgba(255, 204, 0, 0.3);
            }
            100% {
                box-shadow: 0 0 20px rgba(255, 204, 0, 0.7);
            }
        }

        .mod-search-container {
            margin-bottom: 20px;
        }

        .mod-search-results {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
            background-color: #222;
            border-radius: 5px;
            padding: 10px;
        }

        .mod-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #333;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .mod-item:hover {
            background-color: #333;
        }

        .mod-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
            margin-left: 10px;
        }

        .mod-item-info {
            flex: 1;
        }

        .mod-item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .mod-item-category {
            font-size: 0.8rem;
            color: #aaa;
        }

        .featured-list {
            margin-top: 30px;
        }

        .featured-item {
            display: flex;
            align-items: center;
            background-color: #222;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            position: relative;
        }

        .featured-item img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
            margin-left: 15px;
        }

        .featured-item-info {
            flex: 1;
        }

        .featured-item-actions {
            display: flex;
            gap: 10px;
        }

        .toggle-status {
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .toggle-status.active {
            background-color: #4CAF50;
            color: white;
        }

        .toggle-status.inactive {
            background-color: #f44336;
            color: white;
        }

        .remove-featured {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }

        .empty-message {
            text-align: center;
            padding: 20px;
            color: #aaa;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .preview-label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #ffcc00;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>إدارة المودات المميزة</h1>
        <p class="description">هنا يمكنك إضافة وإدارة المودات التي ستظهر بتأثيرات خاصة في قسم الإضافات</p>

        <div class="section">
            <h2>إضافة مود جديد للقائمة المميزة</h2>
            
            <div class="preview-container">
                <span class="preview-label">معاينة التأثير:</span>
                <div class="featured-addon-preview">
                    <div style="display: flex; align-items: center;">
                        <img src="../image/icon_Addons.png" alt="Mod Preview" style="width: 80px; height: 80px; object-fit: cover; border-radius: 5px; margin-left: 15px;">
                        <div>
                            <div style="font-weight: bold; margin-bottom: 5px;">مثال على مود مميز</div>
                            <div style="font-size: 0.8rem; color: #aaa;">هكذا سيظهر المود مع تأثير التوهج الأصفر والكرات المتحركة</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mod-search-container">
                <h3>البحث عن مود</h3>
                <input type="text" id="mod-search" class="input-field" placeholder="اكتب اسم المود للبحث...">
                <div class="mod-search-results" id="search-results">
                    <div class="empty-message">ابدأ البحث لعرض النتائج</div>
                </div>
            </div>
        </div>

        <div class="section featured-list">
            <h2>قائمة المودات المميزة الحالية</h2>
            <div id="featured-mods-list">
                <div class="loading">جاري التحميل...</div>
            </div>
        </div>

        <div class="admin-footer">
            <a href="index.html" class="back-button">العودة للقائمة الرئيسية</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="featured_addons.js"></script>
</body>
</html>
