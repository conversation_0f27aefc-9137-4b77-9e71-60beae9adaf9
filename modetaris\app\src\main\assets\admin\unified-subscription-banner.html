<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراكات والبانرات الموحدة</title>
    <link rel="stylesheet" href="unified-admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <style>
        .unified-creator {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #8a2be2;
        }

        .creator-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .creator-header h2 {
            color: #ffd700;
            font-size: 2rem;
            margin: 0;
        }

        .creator-header p {
            color: #e0e0e0;
            margin: 10px 0 0 0;
        }

        .creation-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .step {
            flex: 1;
            text-align: center;
            position: relative;
            padding: 20px;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -50%;
            width: 100%;
            height: 2px;
            background: #4CAF50;
            transform: translateY(-50%);
            z-index: 1;
        }

        .step:last-child::after {
            display: none;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }

        .step.active .step-number {
            background: #ffd700;
            color: #000;
        }

        .step.completed .step-number {
            background: #28a745;
        }

        .step-title {
            color: #fff;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step-description {
            color: #ccc;
            font-size: 0.9rem;
        }

        .form-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        .bilingual-input {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .language-label {
            font-size: 0.9rem;
            color: #ffd700;
            margin-bottom: 5px;
        }

        .image-upload-section {
            border: 2px dashed #4CAF50;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: rgba(76, 175, 80, 0.1);
            margin: 20px 0;
        }

        .image-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 10px;
            margin: 10px;
        }

        .preview-container {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .preview-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.5);
        }

        .preview-item img {
            width: 150px;
            height: 100px;
            object-fit: cover;
        }

        .preview-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px;
            font-size: 0.8rem;
            text-align: center;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn-unified {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-unified:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .btn-secondary:hover {
            box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }

        .success-animation {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .success-icon {
            font-size: 4rem;
            color: #4CAF50;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        .summary-section {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid #ffd700;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            color: #ffd700;
            font-weight: bold;
        }

        .summary-value {
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1><i class="fas fa-magic"></i> إنشاء حملة اشتراك مع بانر موحد</h1>
                <div class="header-actions">
                    <button onclick="window.location.href='index.html'" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة الرئيسية
                    </button>
                </div>
            </div>
        </header>

        <!-- Unified Creator -->
        <div class="unified-creator">
            <div class="creator-header">
                <h2><i class="fas fa-rocket"></i> منشئ الحملات الموحد</h2>
                <p>أنشئ حملة اشتراك مجاني مع بانر إعلاني في خطوة واحدة</p>
            </div>

            <!-- Progress Steps -->
            <div class="creation-steps">
                <div class="step active" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-title">معلومات الحملة</div>
                    <div class="step-description">العنوان والوصف</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-title">إعدادات الاشتراك</div>
                    <div class="step-description">المدة والحدود</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-title">تصميم البانر</div>
                    <div class="step-description">الصور والتخطيط</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-title">المراجعة والنشر</div>
                    <div class="step-description">التأكيد والتفعيل</div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Step 1: Campaign Information -->
            <div class="form-container" id="step1Content">
                <h3><i class="fas fa-info-circle"></i> معلومات الحملة الأساسية</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>عنوان الحملة</label>
                        <div class="bilingual-input">
                            <div>
                                <div class="language-label">العربية</div>
                                <input type="text" id="campaignTitleAr" placeholder="عنوان الحملة بالعربية" required>
                            </div>
                            <div>
                                <div class="language-label">English</div>
                                <input type="text" id="campaignTitleEn" placeholder="Campaign title in English" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row single">
                    <div class="form-group">
                        <label>وصف الحملة</label>
                        <div class="bilingual-input">
                            <div>
                                <div class="language-label">العربية</div>
                                <textarea id="campaignDescAr" rows="4" placeholder="وصف الحملة بالعربية" required></textarea>
                            </div>
                            <div>
                                <div class="language-label">English</div>
                                <textarea id="campaignDescEn" rows="4" placeholder="Campaign description in English" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Subscription Settings -->
            <div class="form-container" id="step2Content" style="display: none;">
                <h3><i class="fas fa-cog"></i> إعدادات الاشتراك</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="subscriptionDuration">مدة الاشتراك (بالأيام)</label>
                        <input type="number" id="subscriptionDuration" min="1" max="365" value="30" required>
                    </div>
                    <div class="form-group">
                        <label for="maxUsers">الحد الأقصى للمستخدمين (اختياري)</label>
                        <input type="number" id="maxUsers" min="1" placeholder="اتركه فارغاً لعدد غير محدود">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="campaignEndDate">تاريخ انتهاء الحملة (اختياري)</label>
                        <input type="date" id="campaignEndDate">
                    </div>
                    <div class="form-group">
                        <label for="campaignPriority">أولوية الحملة</label>
                        <select id="campaignPriority">
                            <option value="1">منخفضة</option>
                            <option value="2" selected>متوسطة</option>
                            <option value="3">عالية</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 3: Banner Design -->
            <div class="form-container" id="step3Content" style="display: none;">
                <h3><i class="fas fa-paint-brush"></i> تصميم البانر الإعلاني</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="bannerTitle">عنوان البانر</label>
                        <input type="text" id="bannerTitle" placeholder="عنوان البانر الإعلاني" required>
                    </div>
                    <div class="form-group">
                        <label for="bannerDisplayOrder">ترتيب العرض</label>
                        <input type="number" id="bannerDisplayOrder" min="1" value="1">
                    </div>
                </div>

                <div class="form-row single">
                    <div class="form-group">
                        <label for="bannerDescription">وصف البانر</label>
                        <textarea id="bannerDescription" rows="3" placeholder="وصف مختصر للبانر"></textarea>
                    </div>
                </div>

                <!-- Banner Image Upload -->
                <div class="image-upload-section">
                    <h4><i class="fas fa-image"></i> صورة البانر الرئيسية</h4>
                    <input type="file" id="bannerImage" accept="image/*" style="display: none;">
                    <button type="button" onclick="document.getElementById('bannerImage').click()" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> اختر صورة البانر
                    </button>
                    <p>الحد الأقصى: 2 ميجابايت | الأبعاد المفضلة: 1200x400 بكسل</p>
                </div>

                <!-- Popup Image Upload -->
                <div class="image-upload-section">
                    <h4><i class="fas fa-window-maximize"></i> صورة النافذة المنبثقة (اختياري)</h4>
                    <input type="file" id="popupImage" accept="image/*" style="display: none;">
                    <button type="button" onclick="document.getElementById('popupImage').click()" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> اختر صورة النافذة المنبثقة
                    </button>
                    <p>الحد الأقصى: 2 ميجابايت | الأبعاد المفضلة: 600x800 بكسل</p>
                </div>

                <!-- Image Previews -->
                <div class="preview-container" id="imagePreviewContainer">
                    <!-- Previews will be added here dynamically -->
                </div>
            </div>

            <!-- Step 4: Review and Publish -->
            <div class="form-container" id="step4Content" style="display: none;">
                <h3><i class="fas fa-check-circle"></i> مراجعة وتأكيد الحملة</h3>
                
                <div class="summary-section" id="campaignSummary">
                    <!-- Summary will be populated here -->
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="campaignActive" checked>
                            تفعيل الحملة فور الإنشاء
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="bannerActive" checked>
                            تفعيل البانر فور الإنشاء
                        </label>
                    </div>
                </div>
            </div>

            <!-- Success Animation -->
            <div class="success-animation" id="successAnimation">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>تم إنشاء الحملة والبانر بنجاح!</h3>
                <p>تم حفظ جميع البيانات وتفعيل الحملة والبانر الإعلاني</p>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" id="prevBtn" onclick="previousStep()" class="btn-secondary" style="display: none;">
                    <i class="fas fa-arrow-right"></i> السابق
                </button>
                <button type="button" id="nextBtn" onclick="nextStep()" class="btn-unified">
                    التالي <i class="fas fa-arrow-left"></i>
                </button>
                <button type="button" id="createBtn" onclick="createUnifiedCampaign()" class="btn-unified" style="display: none;">
                    <i class="fas fa-rocket"></i> إنشاء الحملة والبانر
                </button>
                <button type="button" id="finishBtn" onclick="finishCreation()" class="btn-unified" style="display: none;">
                    <i class="fas fa-home"></i> العودة للوحة الرئيسية
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <i class="fas fa-spinner fa-spin"></i>
            <p id="loadingText">جاري المعالجة...</p>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification" style="display: none;">
        <span id="notificationText"></span>
        <button onclick="hideNotification()"><i class="fas fa-times"></i></button>
    </div>

    <script src="unified-subscription-banner.js"></script>
</body>
</html>
