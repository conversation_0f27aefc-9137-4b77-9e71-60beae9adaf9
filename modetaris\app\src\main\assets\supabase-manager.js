/**
 * Supabase Manager البسيط - النسخة المبسطة
 * إدارة بسيطة لاتصال Supabase بدون تعقيدات
 */

(function() {
    'use strict';
    
    console.log('🚀 تهيئة Supabase Manager البسيط...');

    // إعدادات Supabase - المفتاح الصحيح
    const SUPABASE_CONFIG = {
        url: 'https://ytqxxodyecdeosnqoure.supabase.co',
        key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
    };

    // متغير عام لعميل Supabase
    let supabaseClient = null;
    let isInitialized = false;

    // دالة تهيئة عميل Supabase البسيطة
    function initializeSupabaseClient() {
        try {
            if (typeof supabase === 'undefined') {
                console.error('❌ مكتبة Supabase غير محملة');
                return false;
            }

            // إنشاء عميل Supabase
            supabaseClient = supabase.createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
            
            if (supabaseClient) {
                console.log('✅ تم إنشاء عميل Supabase بنجاح');
                isInitialized = true;
                
                // جعل العميل متاحاً عالمياً
                window.supabaseClient = supabaseClient;
                
                return true;
            } else {
                console.error('❌ فشل في إنشاء عميل Supabase');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة Supabase:', error);
            return false;
        }
    }

    // دالة الحصول على عميل Supabase
    function getSupabaseClient() {
        if (!isInitialized) {
            console.warn('⚠️ Supabase غير مهيأ، محاولة التهيئة...');
            initializeSupabaseClient();
        }
        return supabaseClient;
    }

    // إنشاء كائن SupabaseManager البسيط
    const SupabaseManager = {
        // الخصائص الأساسية
        isInitialized: () => isInitialized,
        getClient: getSupabaseClient,
        
        // الدوال الأساسية
        initialize: initializeSupabaseClient,
        
        // دوال مساعدة
        isReady: () => isInitialized && supabaseClient !== null,
        getConfig: () => SUPABASE_CONFIG
    };

    // جعل SupabaseManager متاحاً عالمياً
    window.supabaseManager = SupabaseManager;
    window.SupabaseManager = SupabaseManager;

    // تهيئة فورية
    function initializeImmediately() {
        if (typeof supabase !== 'undefined') {
            initializeSupabaseClient();
        } else {
            // انتظار تحميل مكتبة Supabase
            let attempts = 0;
            const maxAttempts = 50; // 5 ثوان
            
            const checkSupabase = setInterval(() => {
                attempts++;
                
                if (typeof supabase !== 'undefined') {
                    clearInterval(checkSupabase);
                    initializeSupabaseClient();
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkSupabase);
                    console.error('❌ انتهت مهلة انتظار تحميل مكتبة Supabase');
                }
            }, 100);
        }
    }

    // تشغيل التهيئة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeImmediately);
    } else {
        initializeImmediately();
    }

    // تشغيل إضافي بعد ثانية واحدة للتأكد
    setTimeout(() => {
        if (!isInitialized) {
            console.warn('⚠️ إعادة محاولة تهيئة Supabase...');
            initializeImmediately();
        }
    }, 1000);

    console.log('✅ تم تحميل Supabase Manager البسيط');

})();
