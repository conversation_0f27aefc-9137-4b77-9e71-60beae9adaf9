-- ========================================
-- إنشاء دوال RPC المفقودة في Supabase
-- Create Missing RPC Functions in Supabase
-- ========================================

-- 1. دالة increment_clicks لزيادة عدد النقرات
-- Function to increment clicks count
CREATE OR REPLACE FUNCTION increment_clicks(mod_id_in UUID)
RETURNS VOID AS $$
BEGIN
    -- تحديث عدد النقرات للمود المحدد
    UPDATE mods 
    SET clicks = COALESCE(clicks, 0) + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = mod_id_in;
    
    -- إذا لم يتم العثور على المود، لا نفعل شيئاً
    -- If mod not found, do nothing (no error)
    
    -- تسجيل العملية (اختياري)
    INSERT INTO mod_analytics (mod_id, action_type, created_at)
    VALUES (mod_id_in, 'click', CURRENT_TIMESTAMP)
    ON CONFLICT DO NOTHING;
    
EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ دون إيقاف العملية
        RAISE WARNING 'Error incrementing clicks for mod %: %', mod_id_in, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. دالة increment_downloads لزيادة عدد التحميلات (إذا لم تكن موجودة)
-- Function to increment downloads count (if not exists)
CREATE OR REPLACE FUNCTION increment_downloads(mod_id_in UUID)
RETURNS VOID AS $$
BEGIN
    -- تحديث عدد التحميلات للمود المحدد
    UPDATE mods 
    SET downloads = COALESCE(downloads, 0) + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = mod_id_in;
    
    -- تسجيل العملية (اختياري)
    INSERT INTO mod_analytics (mod_id, action_type, created_at)
    VALUES (mod_id_in, 'download', CURRENT_TIMESTAMP)
    ON CONFLICT DO NOTHING;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error incrementing downloads for mod %: %', mod_id_in, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. دالة toggle_like للإعجاب (إذا لم تكن موجودة)
-- Function to toggle like status (if not exists)
CREATE OR REPLACE FUNCTION toggle_like(mod_id_in UUID, user_id_in TEXT)
RETURNS JSON AS $$
DECLARE
    current_like_record RECORD;
    new_like_count INTEGER;
    is_liked BOOLEAN;
BEGIN
    -- البحث عن الإعجاب الحالي
    SELECT * INTO current_like_record
    FROM mod_likes 
    WHERE mod_id = mod_id_in AND user_id = user_id_in;
    
    IF FOUND THEN
        -- إزالة الإعجاب
        DELETE FROM mod_likes 
        WHERE mod_id = mod_id_in AND user_id = user_id_in;
        is_liked := FALSE;
    ELSE
        -- إضافة الإعجاب
        INSERT INTO mod_likes (mod_id, user_id, created_at)
        VALUES (mod_id_in, user_id_in, CURRENT_TIMESTAMP);
        is_liked := TRUE;
    END IF;
    
    -- حساب العدد الجديد
    SELECT COUNT(*) INTO new_like_count
    FROM mod_likes 
    WHERE mod_id = mod_id_in;
    
    -- تحديث عدد الإعجابات في جدول المودات
    UPDATE mods 
    SET likes = new_like_count,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = mod_id_in;
    
    -- إرجاع النتيجة
    RETURN json_build_object(
        'liked', is_liked,
        'new_count', new_like_count
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error toggling like for mod %: %', mod_id_in, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. إنشاء جدول mod_analytics إذا لم يكن موجوداً
-- Create mod_analytics table if not exists
CREATE TABLE IF NOT EXISTS mod_analytics (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    action_type VARCHAR(50) NOT NULL, -- 'click', 'download', 'like', etc.
    user_id TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_mod_analytics_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE
);

-- 5. إنشاء جدول mod_likes إذا لم يكن موجوداً
-- Create mod_likes table if not exists
CREATE TABLE IF NOT EXISTS mod_likes (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    user_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_mod_likes_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_mod_user_like UNIQUE (mod_id, user_id)
);

-- 6. إضافة أعمدة إحصائية إذا لم تكن موجودة
-- Add statistical columns if not exists
ALTER TABLE mods 
ADD COLUMN IF NOT EXISTS clicks INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS views INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;

-- 7. إنشاء فهارس للأداء
-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_mod_analytics_mod_id ON mod_analytics(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_analytics_action_type ON mod_analytics(action_type);
CREATE INDEX IF NOT EXISTS idx_mod_analytics_created_at ON mod_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_mod_likes_mod_id ON mod_likes(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_likes_user_id ON mod_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_mods_clicks ON mods(clicks);
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes);

-- 8. إنشاء trigger لتحديث updated_at تلقائياً
-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق trigger على جدول mods
-- Apply trigger to mods table
DROP TRIGGER IF EXISTS update_mods_updated_at ON mods;
CREATE TRIGGER update_mods_updated_at
    BEFORE UPDATE ON mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 9. دالة للحصول على إحصائيات المود
-- Function to get mod statistics
CREATE OR REPLACE FUNCTION get_mod_stats(mod_id_in UUID)
RETURNS JSON AS $$
DECLARE
    mod_stats RECORD;
    analytics_stats RECORD;
BEGIN
    -- الحصول على الإحصائيات الأساسية
    SELECT 
        clicks,
        downloads,
        likes,
        views,
        created_at,
        updated_at
    INTO mod_stats
    FROM mods 
    WHERE id = mod_id_in;
    
    IF NOT FOUND THEN
        RETURN json_build_object('error', 'Mod not found');
    END IF;
    
    -- الحصول على إحصائيات التحليلات
    SELECT 
        COUNT(*) FILTER (WHERE action_type = 'click') as total_clicks,
        COUNT(*) FILTER (WHERE action_type = 'download') as total_downloads,
        COUNT(*) FILTER (WHERE action_type = 'like') as total_likes,
        COUNT(DISTINCT user_id) as unique_users,
        MAX(created_at) as last_activity
    INTO analytics_stats
    FROM mod_analytics 
    WHERE mod_id = mod_id_in;
    
    -- إرجاع النتيجة المجمعة
    RETURN json_build_object(
        'mod_id', mod_id_in,
        'clicks', COALESCE(mod_stats.clicks, 0),
        'downloads', COALESCE(mod_stats.downloads, 0),
        'likes', COALESCE(mod_stats.likes, 0),
        'views', COALESCE(mod_stats.views, 0),
        'analytics', json_build_object(
            'total_clicks', COALESCE(analytics_stats.total_clicks, 0),
            'total_downloads', COALESCE(analytics_stats.total_downloads, 0),
            'total_likes', COALESCE(analytics_stats.total_likes, 0),
            'unique_users', COALESCE(analytics_stats.unique_users, 0),
            'last_activity', analytics_stats.last_activity
        ),
        'created_at', mod_stats.created_at,
        'updated_at', mod_stats.updated_at
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. منح الصلاحيات للدوال
-- Grant permissions to functions
GRANT EXECUTE ON FUNCTION increment_clicks(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION increment_downloads(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION toggle_like(UUID, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_mod_stats(UUID) TO anon, authenticated;

-- 11. إضافة تعليقات توضيحية
-- Add documentation comments
COMMENT ON FUNCTION increment_clicks(UUID) IS 'Increments click count for a mod';
COMMENT ON FUNCTION increment_downloads(UUID) IS 'Increments download count for a mod';
COMMENT ON FUNCTION toggle_like(UUID, TEXT) IS 'Toggles like status for a mod by a user';
COMMENT ON FUNCTION get_mod_stats(UUID) IS 'Returns comprehensive statistics for a mod';
COMMENT ON TABLE mod_analytics IS 'Stores detailed analytics for mod interactions';
COMMENT ON TABLE mod_likes IS 'Stores user likes for mods';

-- ========================================
-- انتهى إنشاء الدوال المفقودة
-- End of Missing RPC Functions Creation
-- ========================================
