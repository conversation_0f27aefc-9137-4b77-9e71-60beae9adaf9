<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Use Theme.MaterialComponents instead of android:Theme.Material -->
    <style name="Theme.Modetaris" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Disable activity transition animations -->
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowActivityTransitions">false</item>
        <!-- Set default window background to dark gray -->
        <item name="android:windowBackground">@color/dark_gray_background</item>
    </style>

    <!-- Splash Screen Theme (Restored) -->
    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <!-- Set the splash screen background, animated icon, and animation duration. -->
        <item name="windowSplashScreenBackground">@color/splash_background_orange</item> <!-- Use the orange color -->

        <!-- Use windowSplashScreenAnimatedIcon to add either a drawable or an
             animated drawable. One of these is required. -->
        <item name="windowSplashScreenAnimatedIcon">@drawable/splash_background</item> <!-- Point back to the drawable -->
        <!-- Required for animated icons -->
        <item name="windowSplashScreenAnimationDuration">0</item> <!-- Disable exit animation -->

        <!-- Set the theme of the Activity that directly follows your splash screen. -->
        <!-- Required -->
        <item name="postSplashScreenTheme">@style/Theme.Modetaris</item>
    </style>
</resources>
