// Backup Ads Manager JavaScript
// إدارة الإعلانات الاحتياطية

// Supabase configuration
// Using the centralized SupabaseManager for client instance
let supabaseClient;

// Global variables
let backupAds = [];
let currentEditingId = null;
let isEditing = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    console.log('Backup Ads Manager loaded');
    // Ensure SupabaseManager is initialized before getting the client
    if (window.SupabaseManager) {
        const manager = new window.SupabaseManager();
        supabaseClient = manager.getMainClient();
        console.log('Supabase client obtained from SupabaseManager.');
    } else {
        console.error('SupabaseManager not found. Ensure supabase-manager.js is loaded before this script.');
        showNotification('خطأ: SupabaseManager غير متاح', 'error');
        return;
    }
    initializeManager();
});

// Initialize manager
async function initializeManager() {
    try {
        setupEventListeners();
        await loadBackupAds();
        updateStats();
        console.log('✅ تم تهيئة إدارة الإعلانات الاحتياطية بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة إدارة الإعلانات الاحتياطية:', error);
        showNotification('فشل في تهيئة إدارة الإعلانات الاحتياطية', 'error');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Form submission
    const form = document.getElementById('backupAdForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }

    // Ad type change
    const adType = document.getElementById('adType');
    if (adType) {
        adType.addEventListener('change', handleAdTypeChange);
    }

    // Priority slider
    const prioritySlider = document.getElementById('adPriority');
    if (prioritySlider) {
        prioritySlider.addEventListener('input', function() {
            document.getElementById('priorityValue').textContent = this.value;
        });
    }

    // Click action change
    const clickAction = document.getElementById('clickAction');
    if (clickAction) {
        clickAction.addEventListener('change', handleClickActionChange);
    }

    // Media upload
    const mediaUploadArea = document.getElementById('mediaUploadArea');
    const mediaFile = document.getElementById('mediaFile');
    
    if (mediaUploadArea && mediaFile) {
        // Click to upload
        mediaUploadArea.addEventListener('click', () => mediaFile.click());
        
        // File selection
        mediaFile.addEventListener('change', handleMediaUpload);
        
        // Drag and drop
        mediaUploadArea.addEventListener('dragover', handleDragOver);
        mediaUploadArea.addEventListener('dragleave', handleDragLeave);
        mediaUploadArea.addEventListener('drop', handleDrop);
    }
}

// Handle ad type change
function handleAdTypeChange() {
    const adType = document.getElementById('adType').value;
    const mediaFile = document.getElementById('mediaFile');
    
    if (adType === 'image') {
        mediaFile.accept = 'image/*';
    } else if (adType === 'video') {
        mediaFile.accept = 'video/*';
    } else {
        mediaFile.accept = 'image/*,video/*';
    }
    
    // Reset media preview
    resetMediaPreview();
}

// Handle click action change
function handleClickActionChange() {
    const clickAction = document.getElementById('clickAction').value;
    const clickUrlGroup = document.getElementById('clickUrlGroup');
    
    if (clickAction === 'url') {
        clickUrlGroup.style.display = 'block';
        document.getElementById('clickUrl').required = true;
    } else {
        clickUrlGroup.style.display = 'none';
        document.getElementById('clickUrl').required = false;
    }
}

// Handle drag over
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

// Handle drag leave
function handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
}

// Handle drop
function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('mediaFile').files = files;
        handleMediaUpload({ target: { files: files } });
    }
}

// Handle media upload
function handleMediaUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file
    if (!validateMediaFile(file)) return;

    // Show preview
    showMediaPreview(file);
}

// Validate media file
function validateMediaFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'];

    if (file.size > maxSize) {
        showNotification('حجم الملف يجب أن يكون أقل من 10 ميجابايت', 'error');
        return false;
    }

    if (!allowedTypes.includes(file.type)) {
        showNotification('نوع الملف غير مدعوم. يرجى اختيار صورة أو فيديو صالح', 'error');
        return false;
    }

    return true;
}

// Show media preview
function showMediaPreview(file) {
    const preview = document.getElementById('mediaPreview');
    const imagePreview = document.getElementById('imagePreview');
    const videoPreview = document.getElementById('videoPreview');
    const mediaSize = document.getElementById('mediaSize');
    const mediaDimensions = document.getElementById('mediaDimensions');

    // Reset previews
    imagePreview.style.display = 'none';
    videoPreview.style.display = 'none';

    const reader = new FileReader();
    reader.onload = function(e) {
        if (file.type.startsWith('image/')) {
            imagePreview.src = e.target.result;
            imagePreview.style.display = 'block';
            
            imagePreview.onload = function() {
                mediaDimensions.textContent = `${this.naturalWidth}x${this.naturalHeight}`;
            };
        } else if (file.type.startsWith('video/')) {
            videoPreview.src = e.target.result;
            videoPreview.style.display = 'block';
            
            videoPreview.onloadedmetadata = function() {
                mediaDimensions.textContent = `${this.videoWidth}x${this.videoHeight}`;
            };
        }
    };

    reader.readAsDataURL(file);
    mediaSize.textContent = formatFileSize(file.size);
    preview.style.display = 'block';
}

// Reset media preview
function resetMediaPreview() {
    const preview = document.getElementById('mediaPreview');
    const imagePreview = document.getElementById('imagePreview');
    const videoPreview = document.getElementById('videoPreview');
    
    preview.style.display = 'none';
    imagePreview.src = '';
    videoPreview.src = '';
    document.getElementById('mediaFile').value = '';
}

// Handle form submit
async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!validateForm()) return;
    
    showLoading('جاري حفظ الإعلان الاحتياطي...');
    
    try {
        // Upload media file
        const mediaFile = document.getElementById('mediaFile').files[0];
        let mediaUrl = '';
        
        if (mediaFile) {
            mediaUrl = await uploadMedia(mediaFile);
        }
        
        // Prepare backup ad data
        const backupAdData = {
            title: document.getElementById('adTitle').value.trim(),
            description: document.getElementById('adDescription').value.trim(),
            ad_type: document.getElementById('adType').value,
            media_url: mediaUrl,
            priority: parseInt(document.getElementById('adPriority').value),
            duration: parseInt(document.getElementById('adDuration').value),
            click_action: document.getElementById('clickAction').value,
            click_url: document.getElementById('clickUrl').value.trim() || null,
            is_active: true,
            created_at: new Date().toISOString()
        };
        
        // Save to database
        let result;
        if (isEditing && currentEditingId) {
            result = await supabaseClient
                .from('backup_ads')
                .update(backupAdData)
                .eq('id', currentEditingId);
        } else {
            result = await supabaseClient
                .from('backup_ads')
                .insert([backupAdData]);
        }
        
        if (result.error) throw result.error;
        
        hideLoading();
        showNotification(isEditing ? 'تم تحديث الإعلان الاحتياطي بنجاح' : 'تم إنشاء الإعلان الاحتياطي بنجاح', 'success');
        
        // Reset form and reload data
        resetForm();
        await loadBackupAds();
        updateStats();
        
    } catch (error) {
        console.error('❌ خطأ في حفظ الإعلان الاحتياطي:', error);
        hideLoading();
        showNotification('فشل في حفظ الإعلان الاحتياطي: ' + error.message, 'error');
    }
}

// Validate form
function validateForm() {
    const title = document.getElementById('adTitle').value.trim();
    const adType = document.getElementById('adType').value;
    const mediaFile = document.getElementById('mediaFile').files[0];
    const clickAction = document.getElementById('clickAction').value;
    const clickUrl = document.getElementById('clickUrl').value.trim();
    
    if (!title) {
        showNotification('يرجى إدخال عنوان الإعلان', 'error');
        return false;
    }
    
    if (!adType) {
        showNotification('يرجى اختيار نوع الإعلان', 'error');
        return false;
    }
    
    if (!mediaFile && !isEditing) {
        showNotification('يرجى اختيار ملف الوسائط', 'error');
        return false;
    }
    
    if (clickAction === 'url' && !clickUrl) {
        showNotification('يرجى إدخال رابط الوجهة', 'error');
        return false;
    }
    
    return true;
}

// Upload media
async function uploadMedia(file) {
    try {
        const fileName = `backup_ad_${Date.now()}_${file.name}`;
        const { data, error } = await supabaseClient.storage
            .from('backup-ads')
            .upload(fileName, file);
        
        if (error) throw error;
        
        // Get public URL
        const { data: urlData } = supabaseClient.storage
            .from('backup-ads')
            .getPublicUrl(fileName);
        
        return urlData.publicUrl;
    } catch (error) {
        console.error('❌ خطأ في رفع الملف:', error);
        throw new Error('فشل في رفع الملف: ' + error.message);
    }
}

// Load backup ads
async function loadBackupAds() {
    try {
        const { data, error } = await supabaseClient
            .from('backup_ads')
            .select('*')
            .order('priority', { ascending: false })
            .order('created_at', { ascending: false });
        
        if (error) throw error;
        
        backupAds = data || [];
        displayBackupAds();
        
        console.log(`✅ تم تحميل ${backupAds.length} إعلان احتياطي`);
    } catch (error) {
        console.error('❌ خطأ في تحميل الإعلانات الاحتياطية:', error);
        document.getElementById('backupAdsList').innerHTML = '<div class="error">فشل في تحميل الإعلانات الاحتياطية</div>';
    }
}

// Display backup ads
function displayBackupAds() {
    const container = document.getElementById('backupAdsList');
    
    if (backupAds.length === 0) {
        container.innerHTML = '<div class="empty-state">لا توجد إعلانات احتياطية حالياً</div>';
        return;
    }
    
    let html = '';
    backupAds.forEach(ad => {
        html += createBackupAdCard(ad);
    });
    
    container.innerHTML = html;
}

// Create backup ad card
function createBackupAdCard(ad) {
    const statusClass = ad.is_active ? 'success' : 'error';
    const statusText = ad.is_active ? 'نشط' : 'غير نشط';
    const typeClass = ad.ad_type === 'image' ? 'ad-type-image' : 'ad-type-video';
    const typeText = ad.ad_type === 'image' ? 'صورة' : 'فيديو';
    
    return `
        <div class="backup-ad-card" data-id="${ad.id}">
            <div class="ad-preview">
                <div class="ad-media">
                    ${ad.ad_type === 'image' 
                        ? `<img src="${ad.media_url}" alt="${ad.title}" loading="lazy">`
                        : `<video src="${ad.media_url}" muted></video>`
                    }
                </div>
                <div class="ad-info">
                    <h4>${ad.title}</h4>
                    <p>${ad.description || 'بدون وصف'}</p>
                    <div class="ad-meta">
                        <span class="${typeClass}">${typeText}</span>
                        <span class="${statusClass}">${statusText}</span>
                        <span>أولوية: ${ad.priority}</span>
                        <span>مدة: ${ad.duration}ث</span>
                        ${ad.click_action !== 'none' ? `<span>إجراء: ${getClickActionText(ad.click_action)}</span>` : ''}
                    </div>
                </div>
            </div>
            <div class="ad-actions">
                <button onclick="editBackupAd('${ad.id}')" class="action-btn edit" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="duplicateBackupAd('${ad.id}')" class="action-btn duplicate" title="نسخ">
                    <i class="fas fa-copy"></i>
                </button>
                <button onclick="toggleBackupAdStatus('${ad.id}')" class="action-btn toggle" title="تفعيل/إلغاء">
                    <i class="fas fa-power-off"></i>
                </button>
                <button onclick="deleteBackupAd('${ad.id}')" class="action-btn delete" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
}

// Get click action text
function getClickActionText(action) {
    const actions = {
        'none': 'لا يوجد',
        'url': 'فتح رابط',
        'close': 'إغلاق'
    };
    return actions[action] || 'غير محدد';
}

// Update stats
function updateStats() {
    const total = backupAds.length;
    const active = backupAds.filter(ad => ad.is_active).length;
    const images = backupAds.filter(ad => ad.ad_type === 'image').length;
    const videos = backupAds.filter(ad => ad.ad_type === 'video').length;
    
    document.getElementById('totalBackupAds').textContent = total;
    document.getElementById('activeBackupAds').textContent = active;
    document.getElementById('imageAds').textContent = images;
    document.getElementById('videoAds').textContent = videos;
}

// Edit backup ad
function editBackupAd(id) {
    const ad = backupAds.find(a => a.id === id);
    if (!ad) return;
    
    isEditing = true;
    currentEditingId = id;
    
    // Fill form with ad data
    document.getElementById('adTitle').value = ad.title;
    document.getElementById('adDescription').value = ad.description || '';
    document.getElementById('adType').value = ad.ad_type;
    document.getElementById('adPriority').value = ad.priority;
    document.getElementById('priorityValue').textContent = ad.priority;
    document.getElementById('adDuration').value = ad.duration;
    document.getElementById('clickAction').value = ad.click_action;
    
    if (ad.click_action === 'url') {
        document.getElementById('clickUrlGroup').style.display = 'block';
        document.getElementById('clickUrl').value = ad.click_url || '';
    }
    
    // Scroll to form
    document.getElementById('backupAdForm').scrollIntoView({ behavior: 'smooth' });
    
    showNotification('تم تحميل بيانات الإعلان للتعديل', 'info');
}

// Toggle backup ad status
async function toggleBackupAdStatus(id) {
    const ad = backupAds.find(a => a.id === id);
    if (!ad) return;
    
    try {
        const { error } = await supabaseClient
            .from('backup_ads')
            .update({ is_active: !ad.is_active })
            .eq('id', id);
        
        if (error) throw error;
        
        await loadBackupAds();
        updateStats();
        
        showNotification(`تم ${ad.is_active ? 'إلغاء تفعيل' : 'تفعيل'} الإعلان بنجاح`, 'success');
    } catch (error) {
        console.error('❌ خطأ في تغيير حالة الإعلان:', error);
        showNotification('فشل في تغيير حالة الإعلان', 'error');
    }
}

// Delete backup ad
async function deleteBackupAd(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الإعلان الاحتياطي؟')) return;
    
    try {
        const { error } = await supabaseClient
            .from('backup_ads')
            .delete()
            .eq('id', id);
        
        if (error) throw error;
        
        await loadBackupAds();
        updateStats();
        
        showNotification('تم حذف الإعلان الاحتياطي بنجاح', 'success');
    } catch (error) {
        console.error('❌ خطأ في حذف الإعلان:', error);
        showNotification('فشل في حذف الإعلان الاحتياطي', 'error');
    }
}

// Duplicate backup ad
function duplicateBackupAd(id) {
    const ad = backupAds.find(a => a.id === id);
    if (!ad) return;
    
    // Fill form with ad data (without ID for new creation)
    document.getElementById('adTitle').value = ad.title + ' (نسخة)';
    document.getElementById('adDescription').value = ad.description || '';
    document.getElementById('adType').value = ad.ad_type;
    document.getElementById('adPriority').value = ad.priority;
    document.getElementById('priorityValue').textContent = ad.priority;
    document.getElementById('adDuration').value = ad.duration;
    document.getElementById('clickAction').value = ad.click_action;
    
    if (ad.click_action === 'url') {
        document.getElementById('clickUrlGroup').style.display = 'block';
        document.getElementById('clickUrl').value = ad.click_url || '';
    }
    
    // Scroll to form
    document.getElementById('backupAdForm').scrollIntoView({ behavior: 'smooth' });
    
    showNotification('تم تحميل بيانات الإعلان للنسخ', 'info');
}

// Reset form
function resetForm() {
    document.getElementById('backupAdForm').reset();
    document.getElementById('priorityValue').textContent = '5';
    document.getElementById('clickUrlGroup').style.display = 'none';
    resetMediaPreview();
    
    isEditing = false;
    currentEditingId = null;
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notificationText');
    
    notificationText.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';
    
    setTimeout(() => {
        hideNotification();
    }, 5000);
}

function hideNotification() {
    document.getElementById('notification').style.display = 'none';
}
