// Database Error Fixes and Improvements
// This file contains fixes for common database errors and improvements

class DatabaseErrorHandler {
    constructor() {
        this.errorLog = [];
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
    }

    // Initialize error handling system
    init() {
        console.log('🔧 Initializing Database Error Handler...');
        
        // Load previous error log
        this.loadErrorLog();
        
        // Set up periodic health checks
        this.startHealthChecks();
        
        // Set up error recovery mechanisms
        this.setupErrorRecovery();
        
        console.log('✅ Database Error Handler initialized');
    }

    // Load error log from localStorage
    loadErrorLog() {
        try {
            const savedLog = localStorage.getItem('databaseErrorLog');
            if (savedLog) {
                this.errorLog = JSON.parse(savedLog);
                console.log(`📊 Loaded ${this.errorLog.length} previous database errors`);
            }
        } catch (error) {
            console.warn('⚠️ Failed to load error log:', error);
            this.errorLog = [];
        }
    }

    // Save error log to localStorage
    saveErrorLog() {
        try {
            // Keep only last 100 errors
            if (this.errorLog.length > 100) {
                this.errorLog = this.errorLog.slice(-100);
            }
            
            localStorage.setItem('databaseErrorLog', JSON.stringify(this.errorLog));
        } catch (error) {
            console.warn('⚠️ Failed to save error log:', error);
        }
    }

    // Log database error
    logError(error, context = {}) {
        const errorEntry = {
            timestamp: new Date().toISOString(),
            error: {
                code: error.code,
                message: error.message,
                name: error.name
            },
            context: context,
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.errorLog.push(errorEntry);
        this.saveErrorLog();

        console.error('📝 Database error logged:', errorEntry);
    }

    // Handle specific error types
    async handleError(error, context = {}) {
        this.logError(error, context);

        // Handle different error types
        switch (error.code) {
            case '400':
                return await this.handle400Error(error, context);
            case '406':
                return await this.handle406Error(error, context);
            case 'PGRST301':
                return await this.handleConnectionError(error, context);
            case 'PGRST116':
                return await this.handleNoDataError(error, context);
            case '42P01':
                return await this.handleTableNotFoundError(error, context);
            default:
                return await this.handleGenericError(error, context);
        }
    }

    // Handle HTTP 400 errors (Bad Request)
    async handle400Error(error, context) {
        console.log('🔧 Handling 400 error...');

        // Check if it's a table/column structure issue
        if (error.message && (error.message.includes('column') || error.message.includes('does not exist'))) {
            console.log('🔄 Column/table structure issue detected');
            await this.createMissingTables();
            return { retry: true, structureFixed: true };
        }

        // Common causes: invalid query parameters, malformed requests
        if (context.query) {
            // Try to fix common query issues
            const fixedQuery = this.fixQueryParameters(context.query);
            if (fixedQuery) {
                console.log('🔄 Retrying with fixed query parameters');
                return { retry: true, fixedQuery };
            }
        }

        // Try to create missing tables as a general fix for 400 errors
        console.log('🔄 Creating missing tables for 400 error...');
        await this.createMissingTables();

        return { retry: true, tablesCreated: true };
    }

    // Create missing tables that commonly cause 400 errors
    async createMissingTables() {
        const client = window.supabaseManager?.getMainClient();
        if (!client) return;

        try {
            // Create error_reports table if missing
            const { error: errorReportsError } = await client.rpc('execute_sql', {
                sql_query: `
                    CREATE TABLE IF NOT EXISTS error_reports (
                        id SERIAL PRIMARY KEY,
                        category TEXT,
                        "errorCode" TEXT,
                        "errorMessage" TEXT,
                        timestamp TIMESTAMP DEFAULT NOW(),
                        "userAgent" TEXT
                    );
                `
            });

            if (!errorReportsError) {
                console.log('✅ error_reports table ensured');
            }

            // Ensure mods table has all required columns
            const { error: modsError } = await client.rpc('execute_sql', {
                sql_query: `
                    ALTER TABLE mods
                    ADD COLUMN IF NOT EXISTS description_ar TEXT,
                    ADD COLUMN IF NOT EXISTS image_urls TEXT[],
                    ADD COLUMN IF NOT EXISTS creator_name TEXT,
                    ADD COLUMN IF NOT EXISTS creator_social_media JSONB,
                    ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS is_popular BOOLEAN DEFAULT FALSE;
                `
            });

            if (!modsError) {
                console.log('✅ mods table columns ensured');
            }

        } catch (error) {
            console.warn('⚠️ Could not create missing tables:', error);
        }
    }

    // Handle HTTP 406 errors (Not Acceptable)
    async handle406Error(error, context) {
        console.log('🔧 Handling 406 error...');
        
        // Common causes: content negotiation issues, header problems
        if (context.headers) {
            const fixedHeaders = this.fixHeaders(context.headers);
            console.log('🔄 Retrying with fixed headers');
            return { retry: true, fixedHeaders };
        }

        // Try simplified request
        return { retry: true, simplify: true };
    }

    // Handle connection errors
    async handleConnectionError(error, context) {
        console.log('🔧 Handling connection error...');
        
        // Check network connectivity
        const isOnline = navigator.onLine;
        if (!isOnline) {
            console.log('📡 No internet connection detected');
            return { retry: false, offline: true };
        }

        // Try backup database if available
        if (window.databaseBackupSystem) {
            console.log('🔄 Switching to backup database...');
            await window.databaseBackupSystem.switchToBackupDatabase();
            return { retry: true, usingBackup: true };
        }

        return { retry: true, delay: this.retryDelay * 2 };
    }

    // Handle no data errors
    async handleNoDataError(error, context) {
        console.log('🔧 Handling no data error...');
        
        // This is often not an actual error, just no results
        return { retry: false, emptyResult: true };
    }

    // Handle table not found errors
    async handleTableNotFoundError(error, context) {
        console.log('🔧 Handling table not found error...');
        
        // Try to create missing tables
        if (window.supabaseManager) {
            console.log('🔄 Attempting to create missing tables...');
            await window.supabaseManager.initializeTables();
            return { retry: true, tablesCreated: true };
        }

        return { retry: false, missingTable: true };
    }

    // Handle generic errors
    async handleGenericError(error, context) {
        console.log('🔧 Handling generic error...');
        
        const retryKey = `${context.operation || 'unknown'}_${error.code || 'unknown'}`;
        const attempts = this.retryAttempts.get(retryKey) || 0;

        if (attempts < this.maxRetries) {
            this.retryAttempts.set(retryKey, attempts + 1);
            console.log(`🔄 Retry attempt ${attempts + 1}/${this.maxRetries}`);
            return { retry: true, delay: this.retryDelay * (attempts + 1) };
        }

        console.log('❌ Max retries exceeded');
        return { retry: false, maxRetriesExceeded: true };
    }

    // Fix common query parameter issues
    fixQueryParameters(query) {
        try {
            // Remove invalid characters
            const cleanQuery = query.replace(/[^\w\s\-_.,=&?]/g, '');
            
            // Fix common parameter issues
            const fixes = {
                'category=eq.': 'category=eq.Addons',
                'limit=': 'limit=10',
                'order=': 'order=created_at.desc'
            };

            let fixedQuery = cleanQuery;
            for (const [invalid, valid] of Object.entries(fixes)) {
                fixedQuery = fixedQuery.replace(invalid, valid);
            }

            return fixedQuery !== query ? fixedQuery : null;
        } catch (error) {
            console.warn('⚠️ Failed to fix query parameters:', error);
            return null;
        }
    }

    // Fix common header issues
    fixHeaders(headers) {
        const fixedHeaders = { ...headers };
        
        // Ensure proper content type
        if (!fixedHeaders['Content-Type']) {
            fixedHeaders['Content-Type'] = 'application/json';
        }
        
        // Ensure proper accept header
        if (!fixedHeaders['Accept']) {
            fixedHeaders['Accept'] = 'application/json';
        }

        return fixedHeaders;
    }

    // Start periodic health checks
    startHealthChecks() {
        // Check database health every 5 minutes
        setInterval(async () => {
            await this.performHealthCheck();
        }, 5 * 60 * 1000);

        // Initial health check
        setTimeout(() => this.performHealthCheck(), 5000);
    }

    // Perform database health check
    async performHealthCheck() {
        try {
            if (!window.supabaseClient) {
                console.warn('⚠️ Supabase client not available for health check');
                return;
            }

            const { data, error } = await window.supabaseClient
                .from('mods')
                .select('count', { count: 'exact', head: true });

            if (error) {
                console.warn('⚠️ Database health check failed:', error);
                await this.handleError(error, { operation: 'health_check' });
            } else {
                console.log('✅ Database health check passed');
                // Clear retry attempts on successful health check
                this.retryAttempts.clear();
            }
        } catch (error) {
            console.error('❌ Health check error:', error);
        }
    }

    // Setup error recovery mechanisms
    setupErrorRecovery() {
        // Override fetch to add error handling
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                if (!response.ok && response.status >= 400) {
                    const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                    error.code = response.status.toString();
                    
                    const context = {
                        url: args[0],
                        method: args[1]?.method || 'GET',
                        operation: 'fetch'
                    };
                    
                    const result = await this.handleError(error, context);
                    
                    if (result.retry) {
                        // Wait before retry if specified
                        if (result.delay) {
                            await new Promise(resolve => setTimeout(resolve, result.delay));
                        }
                        
                        // Retry the request
                        return window.fetch(...args);
                    }
                }
                
                return response;
            } catch (error) {
                const context = {
                    url: args[0],
                    method: args[1]?.method || 'GET',
                    operation: 'fetch'
                };
                
                await this.handleError(error, context);
                throw error;
            }
        };
    }

    // Get error statistics
    getErrorStats() {
        const stats = {
            total: this.errorLog.length,
            byCode: {},
            recent: this.errorLog.filter(e => 
                new Date(e.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
            ).length
        };

        this.errorLog.forEach(entry => {
            const code = entry.error.code || 'unknown';
            stats.byCode[code] = (stats.byCode[code] || 0) + 1;
        });

        return stats;
    }
}

// Initialize the error handler
const databaseErrorHandler = new DatabaseErrorHandler();

// Make it globally available
window.databaseErrorHandler = databaseErrorHandler;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => databaseErrorHandler.init());
} else {
    databaseErrorHandler.init();
}

console.log('🔧 Database Error Fixes loaded');
