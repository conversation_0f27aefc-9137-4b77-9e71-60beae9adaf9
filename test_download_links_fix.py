#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح روابط التحميل في تطبيق Modetaris
Test script for download links fix in Modetaris app
"""

import os
import sys
import requests
import time
from urllib.parse import urlparse, quote

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from firebase_config import firebase_manager
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    print("❌ Firebase غير متوفر")

def test_url_formats(original_url):
    """اختبار تنسيقات مختلفة للرابط"""
    print(f"\n🔍 اختبار تنسيقات مختلفة للرابط:")
    print(f"الرابط الأصلي: {original_url}")
    
    # استخراج معلومات الرابط
    parsed = urlparse(original_url)
    
    if "firebasestorage.googleapis.com" in original_url:
        # استخراج bucket و file path
        if "/v0/b/" in original_url and "/o/" in original_url:
            parts = original_url.split("/v0/b/")[1].split("/o/")
            bucket_name = parts[0]
            encoded_path = parts[1].split("?")[0]
            
            # فك تشفير المسار
            from urllib.parse import unquote
            file_path = unquote(encoded_path)
            
            print(f"Bucket: {bucket_name}")
            print(f"File Path: {file_path}")
            
            # إنشاء تنسيقات مختلفة
            formats = {
                "Original Firebase API": original_url,
                "Direct Storage": f"https://storage.googleapis.com/{bucket_name}/{file_path}",
                "Firebase without encoding": f"https://firebasestorage.googleapis.com/v0/b/{bucket_name}/o/{file_path}?alt=media",
                "Direct with filename": f"https://storage.googleapis.com/{bucket_name}/{file_path}?filename={quote(os.path.basename(file_path))}"
            }
            
            return formats
    
    return {"Original": original_url}

def test_download_url(url, timeout=10):
    """اختبار رابط التحميل"""
    try:
        print(f"\n🔄 اختبار: {url[:80]}...")
        
        # اختبار HEAD request أولاً
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        
        if response.status_code == 200:
            content_length = response.headers.get('content-length', 'غير معروف')
            content_type = response.headers.get('content-type', 'غير معروف')
            
            print(f"✅ نجح الاختبار!")
            print(f"   - رمز الاستجابة: {response.status_code}")
            print(f"   - حجم الملف: {content_length}")
            print(f"   - نوع المحتوى: {content_type}")
            
            return True, response.status_code, content_length, content_type
        else:
            print(f"⚠️ رمز استجابة غير متوقع: {response.status_code}")
            return False, response.status_code, None, None
            
    except requests.exceptions.Timeout:
        print(f"⏰ انتهت مهلة الاختبار ({timeout}s)")
        return False, "Timeout", None, None
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False, "Error", None, None
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False, "Exception", None, None

def test_android_compatibility(url):
    """اختبار التوافق مع Android DownloadManager"""
    print(f"\n📱 اختبار التوافق مع Android:")
    
    # فحص خصائص الرابط
    checks = {
        "يحتوي على معاملات استعلام": "?" in url,
        "يحتوي على تشفير URL": "%" in url,
        "ينتهي بامتداد ملف": any(url.lower().endswith(ext) for ext in ['.mcpack', '.mcaddon', '.zip']),
        "يستخدم HTTPS": url.startswith("https://"),
        "رابط مباشر (بدون API)": not ("alt=media" in url or "/v0/b/" in url)
    }
    
    for check, result in checks.items():
        status = "✅" if result else "❌"
        print(f"   {status} {check}: {result}")
    
    # حساب نقاط التوافق
    compatibility_score = sum(1 for result in checks.values() if result)
    total_checks = len(checks)
    
    print(f"\n📊 نقاط التوافق: {compatibility_score}/{total_checks}")
    
    if compatibility_score >= 4:
        print("✅ توافق ممتاز مع Android")
    elif compatibility_score >= 3:
        print("⚠️ توافق جيد مع Android")
    else:
        print("❌ توافق ضعيف مع Android")
    
    return compatibility_score, total_checks

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔧 اختبار إصلاح روابط التحميل في تطبيق Modetaris")
    print("=" * 60)
    
    # رابط تجريبي من السجل
    test_url = "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2Ftest_mod_20250803_133629_1754230485_lt62ygox.mcpack?alt=media"
    
    print(f"🎯 اختبار الرابط التجريبي:")
    print(f"   {test_url}")
    
    # اختبار التنسيقات المختلفة
    formats = test_url_formats(test_url)
    
    print(f"\n📋 تم إنشاء {len(formats)} تنسيق للاختبار:")
    
    results = {}
    for name, url in formats.items():
        print(f"\n{'='*50}")
        print(f"🔍 اختبار تنسيق: {name}")
        
        # اختبار الرابط
        success, status, size, content_type = test_download_url(url)
        
        # اختبار التوافق مع Android
        compat_score, total_checks = test_android_compatibility(url)
        
        results[name] = {
            'url': url,
            'success': success,
            'status': status,
            'size': size,
            'content_type': content_type,
            'android_compatibility': compat_score / total_checks
        }
    
    # عرض النتائج النهائية
    print(f"\n{'='*60}")
    print("📊 ملخص النتائج:")
    print("=" * 60)
    
    best_format = None
    best_score = 0
    
    for name, result in results.items():
        success_icon = "✅" if result['success'] else "❌"
        compat_percent = int(result['android_compatibility'] * 100)
        
        print(f"\n{success_icon} {name}:")
        print(f"   - حالة الاختبار: {result['status']}")
        print(f"   - التوافق مع Android: {compat_percent}%")
        
        # حساب النقاط الإجمالية
        total_score = (1 if result['success'] else 0) + result['android_compatibility']
        
        if total_score > best_score:
            best_score = total_score
            best_format = name
    
    print(f"\n🏆 أفضل تنسيق: {best_format}")
    if best_format:
        print(f"   الرابط: {results[best_format]['url']}")
    
    # توصيات
    print(f"\n💡 التوصيات:")
    print("   1. استخدم الروابط المباشرة (storage.googleapis.com) للتطبيقات")
    print("   2. تجنب معاملات ?alt=media مع Android DownloadManager")
    print("   3. تأكد من وجود امتداد الملف في نهاية الرابط")
    print("   4. استخدم HTTPS دائماً")

if __name__ == "__main__":
    main()
