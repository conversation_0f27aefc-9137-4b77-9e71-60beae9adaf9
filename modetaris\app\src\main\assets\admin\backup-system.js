// Advanced Backup System
// نظام النسخ الاحتياطي المتقدم

// Global variables
let backupSchedule = null;
let backupHistory = [];
let backupSettings = {
    autoBackup: true,
    frequency: 'daily', // daily, weekly, monthly
    retentionDays: 30,
    includeImages: true,
    includeUserData: true,
    compression: true
};

// Initialize backup system
document.addEventListener('DOMContentLoaded', function() {
    console.log('Backup System loaded');
    initializeBackupSystem();
});

// Initialize backup system
async function initializeBackupSystem() {
    try {
        // Load backup settings
        await loadBackupSettings();
        
        // Load backup history
        await loadBackupHistory();
        
        // Setup automatic backups
        setupAutomaticBackups();
        
        // Setup backup UI
        setupBackupUI();
        
        console.log('✅ Backup system initialized');
    } catch (error) {
        console.error('Error initializing backup system:', error);
    }
}

// Load backup settings
async function loadBackupSettings() {
    try {
        const savedSettings = localStorage.getItem('backupSettings');
        if (savedSettings) {
            backupSettings = { ...backupSettings, ...JSON.parse(savedSettings) };
        }
        
        console.log('Backup settings loaded:', backupSettings);
    } catch (error) {
        console.error('Error loading backup settings:', error);
    }
}

// Save backup settings
function saveBackupSettings() {
    try {
        localStorage.setItem('backupSettings', JSON.stringify(backupSettings));
        console.log('Backup settings saved');
    } catch (error) {
        console.error('Error saving backup settings:', error);
    }
}

// Load backup history
async function loadBackupHistory() {
    try {
        const savedHistory = localStorage.getItem('backupHistory');
        if (savedHistory) {
            backupHistory = JSON.parse(savedHistory);
        }
        
        // Add some mock history if empty
        if (backupHistory.length === 0) {
            backupHistory = generateMockBackupHistory();
        }
        
        updateBackupHistoryUI();
        console.log('Backup history loaded:', backupHistory.length + ' entries');
    } catch (error) {
        console.error('Error loading backup history:', error);
    }
}

// Generate mock backup history
function generateMockBackupHistory() {
    const history = [];
    for (let i = 0; i < 10; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        history.push({
            id: 'backup_' + Date.now() + '_' + i,
            timestamp: date.toISOString(),
            type: i === 0 ? 'manual' : 'automatic',
            status: Math.random() > 0.1 ? 'completed' : 'failed',
            size: Math.floor(Math.random() * 500) + 50, // MB
            duration: Math.floor(Math.random() * 300) + 30, // seconds
            tables: ['mods', 'users', 'analytics', 'notifications'],
            compression: true
        });
    }
    return history;
}

// Setup automatic backups
function setupAutomaticBackups() {
    if (!backupSettings.autoBackup) return;
    
    // Clear existing schedule
    if (backupSchedule) {
        clearInterval(backupSchedule);
    }
    
    // Calculate interval based on frequency
    let intervalMs;
    switch (backupSettings.frequency) {
        case 'hourly':
            intervalMs = 60 * 60 * 1000; // 1 hour
            break;
        case 'daily':
            intervalMs = 24 * 60 * 60 * 1000; // 24 hours
            break;
        case 'weekly':
            intervalMs = 7 * 24 * 60 * 60 * 1000; // 7 days
            break;
        case 'monthly':
            intervalMs = 30 * 24 * 60 * 60 * 1000; // 30 days
            break;
        default:
            intervalMs = 24 * 60 * 60 * 1000; // Default to daily
    }
    
    // Setup scheduled backup
    backupSchedule = setInterval(async () => {
        await createAutomaticBackup();
    }, intervalMs);
    
    console.log(`Automatic backup scheduled every ${backupSettings.frequency}`);
}

// Create manual backup
async function createManualBackup() {
    return await createBackup('manual');
}

// Create automatic backup
async function createAutomaticBackup() {
    return await createBackup('automatic');
}

// Create backup
async function createBackup(type = 'manual') {
    try {
        showBackupProgress(true);
        updateBackupStatus('جاري إنشاء النسخة الاحتياطية...');
        
        const backupId = 'backup_' + Date.now();
        const startTime = Date.now();
        
        // Simulate backup process
        const backupData = await performBackupProcess();
        
        const endTime = Date.now();
        const duration = Math.floor((endTime - startTime) / 1000);
        
        // Create backup entry
        const backupEntry = {
            id: backupId,
            timestamp: new Date().toISOString(),
            type: type,
            status: 'completed',
            size: backupData.size,
            duration: duration,
            tables: backupData.tables,
            compression: backupSettings.compression,
            includeImages: backupSettings.includeImages,
            includeUserData: backupSettings.includeUserData
        };
        
        // Add to history
        backupHistory.unshift(backupEntry);
        
        // Keep only recent backups based on retention
        cleanupOldBackups();
        
        // Save history
        localStorage.setItem('backupHistory', JSON.stringify(backupHistory));
        
        // Update UI
        updateBackupHistoryUI();
        updateBackupStatus('تم إنشاء النسخة الاحتياطية بنجاح ✅');
        
        // Download backup file
        if (type === 'manual') {
            downloadBackupFile(backupEntry);
        }
        
        showBackupProgress(false);
        
        console.log('Backup created successfully:', backupEntry);
        return backupEntry;
        
    } catch (error) {
        console.error('Error creating backup:', error);
        
        // Add failed backup to history
        const failedBackup = {
            id: 'backup_failed_' + Date.now(),
            timestamp: new Date().toISOString(),
            type: type,
            status: 'failed',
            error: error.message
        };
        
        backupHistory.unshift(failedBackup);
        updateBackupHistoryUI();
        updateBackupStatus('فشل في إنشاء النسخة الاحتياطية ❌');
        showBackupProgress(false);
        
        throw error;
    }
}

// Perform backup process
async function performBackupProcess() {
    const tables = [];
    let totalSize = 0;
    
    try {
        if (!supabaseClient) {
            throw new Error('Supabase client not available');
        }
        
        // Backup mods table
        updateBackupStatus('جاري نسخ بيانات المودات...');
        const { data: mods, error: modsError } = await supabaseClient
            .from('mods')
            .select('*');
        
        if (!modsError && mods) {
            tables.push('mods');
            totalSize += estimateDataSize(mods);
        }
        
        // Backup user data if enabled
        if (backupSettings.includeUserData) {
            updateBackupStatus('جاري نسخ بيانات المستخدمين...');
            const { data: users, error: usersError } = await supabaseClient
                .from('user_languages')
                .select('*');
            
            if (!usersError && users) {
                tables.push('user_languages');
                totalSize += estimateDataSize(users);
            }
        }
        
        // Backup analytics data
        updateBackupStatus('جاري نسخ بيانات التحليلات...');
        // Simulate analytics backup
        tables.push('analytics');
        totalSize += Math.floor(Math.random() * 50) + 10;
        
        // Backup notifications
        updateBackupStatus('جاري نسخ بيانات الإشعارات...');
        tables.push('notifications');
        totalSize += Math.floor(Math.random() * 20) + 5;
        
        // Apply compression if enabled
        if (backupSettings.compression) {
            updateBackupStatus('جاري ضغط البيانات...');
            totalSize = Math.floor(totalSize * 0.7); // Simulate 30% compression
        }
        
        return {
            tables: tables,
            size: totalSize
        };
        
    } catch (error) {
        console.error('Error in backup process:', error);
        throw error;
    }
}

// Estimate data size in MB
function estimateDataSize(data) {
    const jsonString = JSON.stringify(data);
    const sizeInBytes = new Blob([jsonString]).size;
    return Math.floor(sizeInBytes / (1024 * 1024)); // Convert to MB
}

// Download backup file
function downloadBackupFile(backupEntry) {
    const backupData = {
        metadata: backupEntry,
        timestamp: new Date().toISOString(),
        version: '2.0',
        data: {
            // This would contain the actual backup data
            note: 'Backup data would be included here in a real implementation'
        }
    };
    
    const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup_${backupEntry.id}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
}

// Cleanup old backups
function cleanupOldBackups() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - backupSettings.retentionDays);
    
    backupHistory = backupHistory.filter(backup => {
        const backupDate = new Date(backup.timestamp);
        return backupDate > cutoffDate;
    });
}

// Restore from backup
async function restoreFromBackup(backupId) {
    if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        return;
    }
    
    try {
        showBackupProgress(true);
        updateBackupStatus('جاري استعادة النسخة الاحتياطية...');
        
        const backup = backupHistory.find(b => b.id === backupId);
        if (!backup) {
            throw new Error('Backup not found');
        }
        
        // Simulate restore process
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        updateBackupStatus('تم استعادة النسخة الاحتياطية بنجاح ✅');
        showBackupProgress(false);
        
        console.log('Backup restored successfully:', backup);
        
    } catch (error) {
        console.error('Error restoring backup:', error);
        updateBackupStatus('فشل في استعادة النسخة الاحتياطية ❌');
        showBackupProgress(false);
    }
}

// Delete backup
function deleteBackup(backupId) {
    if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        return;
    }
    
    backupHistory = backupHistory.filter(backup => backup.id !== backupId);
    localStorage.setItem('backupHistory', JSON.stringify(backupHistory));
    updateBackupHistoryUI();
    
    console.log('Backup deleted:', backupId);
}

// Setup backup UI
function setupBackupUI() {
    // Update settings UI
    updateBackupSettingsUI();
    
    // Setup event listeners
    setupBackupEventListeners();
}

// Update backup settings UI
function updateBackupSettingsUI() {
    const autoBackupToggle = document.getElementById('auto-backup-toggle');
    if (autoBackupToggle) {
        autoBackupToggle.checked = backupSettings.autoBackup;
    }
    
    const frequencySelect = document.getElementById('backup-frequency');
    if (frequencySelect) {
        frequencySelect.value = backupSettings.frequency;
    }
    
    const retentionInput = document.getElementById('backup-retention');
    if (retentionInput) {
        retentionInput.value = backupSettings.retentionDays;
    }
    
    const includeImagesToggle = document.getElementById('include-images');
    if (includeImagesToggle) {
        includeImagesToggle.checked = backupSettings.includeImages;
    }
    
    const includeUserDataToggle = document.getElementById('include-user-data');
    if (includeUserDataToggle) {
        includeUserDataToggle.checked = backupSettings.includeUserData;
    }
    
    const compressionToggle = document.getElementById('backup-compression');
    if (compressionToggle) {
        compressionToggle.checked = backupSettings.compression;
    }
}

// Setup backup event listeners
function setupBackupEventListeners() {
    // Auto backup toggle
    const autoBackupToggle = document.getElementById('auto-backup-toggle');
    if (autoBackupToggle) {
        autoBackupToggle.addEventListener('change', (e) => {
            backupSettings.autoBackup = e.target.checked;
            saveBackupSettings();
            setupAutomaticBackups();
        });
    }
    
    // Frequency change
    const frequencySelect = document.getElementById('backup-frequency');
    if (frequencySelect) {
        frequencySelect.addEventListener('change', (e) => {
            backupSettings.frequency = e.target.value;
            saveBackupSettings();
            setupAutomaticBackups();
        });
    }
    
    // Other settings
    ['backup-retention', 'include-images', 'include-user-data', 'backup-compression'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', (e) => {
                const setting = id.replace('backup-', '').replace('-', '');
                if (element.type === 'checkbox') {
                    backupSettings[setting] = e.target.checked;
                } else {
                    backupSettings[setting] = e.target.value;
                }
                saveBackupSettings();
            });
        }
    });
}

// Update backup history UI
function updateBackupHistoryUI() {
    const historyContainer = document.getElementById('backup-history-list');
    if (!historyContainer) return;
    
    historyContainer.innerHTML = backupHistory.map(backup => `
        <div class="backup-item ${backup.status}">
            <div class="backup-info">
                <div class="backup-header">
                    <span class="backup-id">${backup.id}</span>
                    <span class="backup-type ${backup.type}">${backup.type === 'manual' ? 'يدوي' : 'تلقائي'}</span>
                    <span class="backup-status ${backup.status}">
                        ${backup.status === 'completed' ? '✅ مكتمل' : '❌ فاشل'}
                    </span>
                </div>
                <div class="backup-details">
                    <span>التاريخ: ${new Date(backup.timestamp).toLocaleString('ar-SA')}</span>
                    ${backup.size ? `<span>الحجم: ${backup.size} MB</span>` : ''}
                    ${backup.duration ? `<span>المدة: ${backup.duration}s</span>` : ''}
                </div>
                ${backup.tables ? `
                    <div class="backup-tables">
                        الجداول: ${backup.tables.join(', ')}
                    </div>
                ` : ''}
            </div>
            <div class="backup-actions">
                ${backup.status === 'completed' ? `
                    <button class="backup-action-btn restore" onclick="restoreFromBackup('${backup.id}')">
                        <i class="fas fa-undo"></i> استعادة
                    </button>
                    <button class="backup-action-btn download" onclick="downloadBackupFile(${JSON.stringify(backup).replace(/"/g, '&quot;')})">
                        <i class="fas fa-download"></i> تحميل
                    </button>
                ` : ''}
                <button class="backup-action-btn delete" onclick="deleteBackup('${backup.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

// Show/hide backup progress
function showBackupProgress(show) {
    const progressElement = document.getElementById('backup-progress');
    if (progressElement) {
        progressElement.style.display = show ? 'block' : 'none';
    }
}

// Update backup status
function updateBackupStatus(status) {
    const statusElement = document.getElementById('backup-status');
    if (statusElement) {
        statusElement.textContent = status;
    }
}

// Export backup settings
function exportBackupSettings() {
    const data = {
        settings: backupSettings,
        history: backupHistory,
        timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-settings.json';
    a.click();
}

// Make functions globally available
window.createManualBackup = createManualBackup;
window.restoreFromBackup = restoreFromBackup;
window.deleteBackup = deleteBackup;
window.exportBackupSettings = exportBackupSettings;
