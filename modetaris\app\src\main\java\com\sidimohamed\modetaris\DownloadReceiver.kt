package com.sidimohamed.modetaris

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager // Import LocalBroadcastManager
import org.json.JSONObject

// Define keys publicly or pass them if needed
const val PENDING_DOWNLOADS_KEY = "pending_downloads"
const val PREFS_NAME = "ModetarisPrefs"

class DownloadReceiver : BroadcastReceiver() {

    // Keep track of active downloads (modId associated with downloadId)
    // This needs to be accessed statically or via a shared mechanism if MainActivity also needs it.
    // For simplicity now, assume MainActivity manages adding to this map.
    // A better approach might involve a database or singleton service.
    companion object {
        // Using ConcurrentHashMap requires careful synchronization if accessed across threads/instances
        // Let's simplify and assume MainActivity handles the mapping for now.
        // We'll retrieve the modId from SharedPreferences based on downloadId if needed,
        // or ideally, pass it via the intent if possible (though DownloadManager doesn't support this directly).
        // Let's stick to SharedPreferences for now.
        // We need a way to map downloadId back to modId. Let's store this mapping too.
        const val DOWNLOAD_ID_TO_MOD_ID_KEY = "download_id_map"

        // Action for local broadcast when download completes
        const val ACTION_DOWNLOAD_STATUS_UPDATE = "com.sidimohamed.modetaris.DOWNLOAD_STATUS_UPDATE"
        const val EXTRA_DOWNLOAD_ID = "downloadId" // Send downloadId instead of modId/filePath

        fun saveDownloadMapping(context: Context, downloadId: Long, modId: String) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val mappingJsonString = prefs.getString(DOWNLOAD_ID_TO_MOD_ID_KEY, "{}") ?: "{}"
            val mappingJson = JSONObject(mappingJsonString)
            mappingJson.put(downloadId.toString(), modId)
            prefs.edit().putString(DOWNLOAD_ID_TO_MOD_ID_KEY, mappingJson.toString()).apply()
            Log.d("DownloadReceiver", "Saved download ID mapping: $downloadId -> $modId")
        }

        fun getModIdForDownload(context: Context, downloadId: Long): String? {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val mappingJsonString = prefs.getString(DOWNLOAD_ID_TO_MOD_ID_KEY, "{}") ?: "{}"
            if (mappingJsonString == "{}") return null
            try {
                val mappingJson = JSONObject(mappingJsonString)
                return mappingJson.optString(downloadId.toString(), null)
            } catch (e: org.json.JSONException) {
                Log.e("DownloadReceiver", "Error parsing download ID mapping JSON", e)
                return null
            }
        }

         fun removeDownloadMapping(context: Context, downloadId: Long) {
             val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
             val mappingJsonString = prefs.getString(DOWNLOAD_ID_TO_MOD_ID_KEY, "{}") ?: "{}"
             if (mappingJsonString == "{}") return
             try {
                 val mappingJson = JSONObject(mappingJsonString)
                 if (mappingJson.has(downloadId.toString())) {
                     mappingJson.remove(downloadId.toString())
                     prefs.edit().putString(DOWNLOAD_ID_TO_MOD_ID_KEY, mappingJson.toString()).apply()
                     Log.d("DownloadReceiver", "Removed download ID mapping for: $downloadId")
                 }
             } catch (e: org.json.JSONException) {
                 Log.e("DownloadReceiver", "Error removing download ID mapping JSON", e)
             }
         }


        // Function to save completed download path (can be called from onReceive)
        fun saveCompletedDownloadPath(context: Context, modId: String, filePath: String) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val pendingJsonString = prefs.getString(PENDING_DOWNLOADS_KEY, "{}") ?: "{}"
            val pendingDownloads = JSONObject(pendingJsonString)
            pendingDownloads.put(modId, filePath)
            prefs.edit().putString(PENDING_DOWNLOADS_KEY, pendingDownloads.toString()).apply()
            Log.d("DownloadReceiver", "Saved completed download path to SharedPreferences: $modId -> $filePath")
        }
    }


    override fun onReceive(context: Context?, intent: Intent?) {
        Log.d("DownloadReceiver", ">>> onReceive triggered! Action: ${intent?.action}")
        if (context == null) {
             Log.e("DownloadReceiver", "Context is null in onReceive, cannot proceed.")
             return
        }

        if (intent?.action == DownloadManager.ACTION_DOWNLOAD_COMPLETE) {
            Log.d("DownloadReceiver", ">>> Received ACTION_DOWNLOAD_COMPLETE")
            val downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            Log.d("DownloadReceiver", ">>> Download ID from intent: $downloadId")

            if (downloadId == -1L) {
                 Log.e("DownloadReceiver", "Invalid download ID received.")
                 return
            }

            // Retrieve the modId associated with this downloadId
            val modId = getModIdForDownload(context, downloadId)
            if (modId == null) {
                 Log.e("DownloadReceiver", "Could not find modId mapping for download ID: $downloadId. Cannot save path.")
                 // Clean up potentially stale mapping if it exists but lookup failed? Maybe not needed.
                 return
            }

            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            val query = DownloadManager.Query().setFilterById(downloadId)
            val cursor = downloadManager.query(query)

            if (cursor != null && cursor.moveToFirst()) {
                val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                val status = cursor.getInt(statusIndex)

                if (status == DownloadManager.STATUS_SUCCESSFUL) {
                    val pathIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_FILENAME)
                    val localPath = cursor.getString(pathIndex)
                    val filenameIndex = cursor.getColumnIndex(DownloadManager.COLUMN_TITLE) // Get filename from title
                    val filename = cursor.getString(filenameIndex) ?: "Unknown File"

                    Log.d("DownloadReceiver", "Download successful. Path: $localPath")

                    if (localPath != null) {
                        Log.d("DownloadReceiver", "Download successful for mod $modId. Storing path.")
                        saveCompletedDownloadPath(context, modId, localPath)

                        // --- Send local broadcast with downloadId ---
                        val updateIntent = Intent(ACTION_DOWNLOAD_STATUS_UPDATE).apply {
                            putExtra(EXTRA_DOWNLOAD_ID, downloadId) // Send the downloadId
                        }
                        LocalBroadcastManager.getInstance(context).sendBroadcast(updateIntent)
                        Log.d("DownloadReceiver", "Sent local broadcast for completed downloadId $downloadId")
                        // --- End broadcast ---

                    } else {
                        Log.e("DownloadReceiver", "Could not get local path for downloaded file (modId: $modId).")
                        // Optionally send a failure broadcast here too
                    }
                } else {
                    val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
                    val reason = cursor.getInt(reasonIndex)
                    Log.e("DownloadReceiver", "Download failed for ID: $downloadId (modId: $modId), Status: $status, Reason: $reason")
                    // Optionally save failure state?
                }
                cursor.close()
            } else {
                Log.e("DownloadReceiver", "Could not query download manager for ID: $downloadId (modId: $modId)")
            }
            // Clean up the download ID mapping once processed
            removeDownloadMapping(context, downloadId)
        }
    }
}
