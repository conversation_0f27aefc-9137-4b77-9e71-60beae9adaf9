# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة بيانات Supabase
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_mods_table_schema():
    """فحص هيكل جدول mods"""
    print("🔍 فحص هيكل جدول mods في Supabase...")
    
    try:
        from supabase_config import get_supabase_client
        
        client = get_supabase_client()
        if not client:
            print("❌ لا يمكن الحصول على عميل Supabase")
            return False
        
        # محاولة جلب سجل واحد لمعرفة الأعمدة المتاحة
        try:
            response = client.table('mods').select('*').limit(1).execute()
            
            if response.data and len(response.data) > 0:
                print("✅ تم جلب بيانات من جدول mods")
                print("\n📋 الأعمدة المتاحة في جدول mods:")
                print("-" * 40)
                
                first_record = response.data[0]
                for column_name, value in first_record.items():
                    value_type = type(value).__name__
                    value_preview = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"  {column_name}: {value_type} = {value_preview}")
                
                return True
            else:
                print("⚠️ جدول mods فارغ، سنحاول فحص الهيكل بطريقة أخرى")
                
                # محاولة إدراج بيانات فارغة لمعرفة الأعمدة المطلوبة
                try:
                    client.table('mods').insert({}).execute()
                except Exception as insert_e:
                    error_msg = str(insert_e)
                    print(f"📋 معلومات من خطأ الإدراج: {error_msg}")
                    
                    # استخراج أسماء الأعمدة من رسالة الخطأ
                    if 'null value in column' in error_msg:
                        print("💡 يبدو أن هناك أعمدة مطلوبة (NOT NULL)")
                
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص جدول mods: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_simple_insert():
    """اختبار إدراج بسيط مع أقل البيانات الممكنة"""
    print("\n🔍 اختبار إدراج بسيط...")
    
    try:
        from supabase_config import get_supabase_client
        
        client = get_supabase_client()
        if not client:
            print("❌ لا يمكن الحصول على عميل Supabase")
            return False
        
        # بيانات أساسية جداً
        minimal_data = {
            'title': 'Test Minimal',
            'description': 'Test description'
        }
        
        print("🔄 محاولة إدراج بيانات أساسية...")
        try:
            response = client.table('mods').insert(minimal_data).execute()
            
            if response.data:
                print("✅ نجح الإدراج الأساسي")
                
                # حذف البيانات الاختبارية
                try:
                    client.table('mods').delete().eq('title', 'Test Minimal').execute()
                    print("🗑️ تم حذف البيانات الاختبارية")
                except:
                    print("⚠️ لم يتم حذف البيانات الاختبارية")
                
                return True
            else:
                print("❌ فشل الإدراج الأساسي")
                return False
                
        except Exception as insert_e:
            print(f"❌ خطأ في الإدراج: {insert_e}")
            
            # تحليل رسالة الخطأ لمعرفة الأعمدة المطلوبة
            error_msg = str(insert_e)
            if 'null value in column' in error_msg:
                print("💡 هناك أعمدة مطلوبة غير موجودة في البيانات")
            elif 'does not exist' in error_msg:
                print("💡 هناك أعمدة غير موجودة في الجدول")
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام في اختبار الإدراج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص هيكل قاعدة بيانات Supabase")
    print("=" * 50)
    
    # فحص هيكل الجدول
    schema_check = check_mods_table_schema()
    
    # اختبار إدراج بسيط
    insert_check = test_simple_insert()
    
    print("\n" + "=" * 50)
    print("📊 نتائج الفحص:")
    print("=" * 50)
    print(f"✅ فحص الهيكل: {'نجح' if schema_check else 'فشل'}")
    print(f"✅ اختبار الإدراج: {'نجح' if insert_check else 'فشل'}")
    
    if not schema_check and not insert_check:
        print("\n💡 نصائح:")
        print("1. تأكد من وجود جدول 'mods' في قاعدة البيانات")
        print("2. تحقق من صلاحيات المستخدم للوصول للجدول")
        print("3. تأكد من صحة مفاتيح API")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الفحص بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
