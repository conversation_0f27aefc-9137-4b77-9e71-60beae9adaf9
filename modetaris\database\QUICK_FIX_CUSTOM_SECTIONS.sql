-- ========================================
-- حل سريع لمشكلة custom_sections - Quick Fix for custom_sections Issue
-- نفذ هذا الكود في Supabase SQL Editor
-- Execute this code in Supabase SQL Editor
-- ========================================

-- إنشاء جدول custom_sections
CREATE TABLE IF NOT EXISTS custom_sections (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    section_type VARCHAR(50) DEFAULT 'custom',
    mod_selection_type VARCHAR(50) DEFAULT 'manual',
    auto_criteria JSONB,
    max_mods_display INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول custom_section_mods
CREATE TABLE IF NOT EXISTS custom_section_mods (
    id SERIAL PRIMARY KEY,
    section_id INTEGER REFERENCES custom_sections(id) ON DELETE CASCADE,
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    display_order INTEGER DEFAULT 0,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(section_id, mod_id)
);

-- تفعيل Row Level Security
ALTER TABLE custom_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_section_mods ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان
DROP POLICY IF EXISTS "Enable read access for all users" ON custom_sections;
CREATE POLICY "Enable read access for all users" ON custom_sections
FOR SELECT USING (TRUE);

DROP POLICY IF EXISTS "Enable read access for all users" ON custom_section_mods;
CREATE POLICY "Enable read access for all users" ON custom_section_mods
FOR SELECT USING (TRUE);

-- إنشاء فهارس
CREATE INDEX IF NOT EXISTS idx_custom_sections_active ON custom_sections(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_sections_order ON custom_sections(display_order);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_section ON custom_section_mods(section_id);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_mod ON custom_section_mods(mod_id);

-- إضافة بيانات تجريبية
INSERT INTO custom_sections (name_ar, name_en, description_ar, description_en, display_order, is_active) 
VALUES 
    ('الأكثر شعبية', 'Most Popular', 'أفضل المودات الأكثر شعبية', 'Best most popular mods', 1, true),
    ('الجديد والمميز', 'New & Featured', 'أحدث المودات المميزة', 'Latest featured mods', 2, true)
ON CONFLICT DO NOTHING;

-- ========================================
-- تم إنشاء جداول custom_sections بنجاح
-- custom_sections tables created successfully
-- ========================================
