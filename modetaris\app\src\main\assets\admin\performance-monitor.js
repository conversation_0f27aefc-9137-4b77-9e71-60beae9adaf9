// Real-time Performance Monitor
// مراقب الأداء المباشر

// Global variables
let performanceData = {
    cpu: [],
    memory: [],
    network: [],
    database: [],
    storage: []
};

let monitoringActive = false;
let monitoringInterval;
let alertThresholds = {
    cpu: 80,
    memory: 85,
    responseTime: 2000,
    errorRate: 5
};

// Initialize performance monitor
document.addEventListener('DOMContentLoaded', function() {
    console.log('Performance Monitor loaded');
    initializePerformanceMonitor();
});

// Initialize performance monitor
async function initializePerformanceMonitor() {
    try {
        // Load performance settings
        loadPerformanceSettings();
        
        // Setup monitoring UI
        setupMonitoringUI();
        
        // Start monitoring if enabled
        if (monitoringActive) {
            startPerformanceMonitoring();
        }
        
        // Setup real-time charts
        initializePerformanceCharts();
        
        console.log('✅ Performance monitor initialized');
    } catch (error) {
        console.error('Error initializing performance monitor:', error);
    }
}

// Load performance settings
function loadPerformanceSettings() {
    try {
        const savedSettings = localStorage.getItem('performanceSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            monitoringActive = settings.monitoringActive || false;
            alertThresholds = { ...alertThresholds, ...settings.alertThresholds };
        }
    } catch (error) {
        console.error('Error loading performance settings:', error);
    }
}

// Save performance settings
function savePerformanceSettings() {
    try {
        const settings = {
            monitoringActive,
            alertThresholds
        };
        localStorage.setItem('performanceSettings', JSON.stringify(settings));
    } catch (error) {
        console.error('Error saving performance settings:', error);
    }
}

// Start performance monitoring
function startPerformanceMonitoring() {
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
    }
    
    monitoringActive = true;
    updateMonitoringStatus();
    
    // Monitor every 5 seconds
    monitoringInterval = setInterval(async () => {
        await collectPerformanceMetrics();
    }, 5000);
    
    console.log('Performance monitoring started');
}

// Stop performance monitoring
function stopPerformanceMonitoring() {
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
        monitoringInterval = null;
    }
    
    monitoringActive = false;
    updateMonitoringStatus();
    
    console.log('Performance monitoring stopped');
}

// Collect performance metrics
async function collectPerformanceMetrics() {
    try {
        const timestamp = Date.now();
        
        // Collect CPU metrics
        const cpuMetric = await collectCPUMetrics();
        performanceData.cpu.push({ timestamp, value: cpuMetric });
        
        // Collect memory metrics
        const memoryMetric = await collectMemoryMetrics();
        performanceData.memory.push({ timestamp, value: memoryMetric });
        
        // Collect network metrics
        const networkMetric = await collectNetworkMetrics();
        performanceData.network.push({ timestamp, value: networkMetric });
        
        // Collect database metrics
        const databaseMetric = await collectDatabaseMetrics();
        performanceData.database.push({ timestamp, value: databaseMetric });
        
        // Collect storage metrics
        const storageMetric = await collectStorageMetrics();
        performanceData.storage.push({ timestamp, value: storageMetric });
        
        // Keep only last 100 data points
        Object.keys(performanceData).forEach(key => {
            if (performanceData[key].length > 100) {
                performanceData[key] = performanceData[key].slice(-100);
            }
        });
        
        // Update UI
        updatePerformanceUI();
        
        // Check for alerts
        checkPerformanceAlerts();
        
    } catch (error) {
        console.error('Error collecting performance metrics:', error);
    }
}

// Collect CPU metrics
async function collectCPUMetrics() {
    // Simulate CPU usage
    const baseUsage = 20;
    const variation = Math.sin(Date.now() / 10000) * 30;
    const randomNoise = (Math.random() - 0.5) * 20;
    
    return Math.max(0, Math.min(100, baseUsage + variation + randomNoise));
}

// Collect memory metrics
async function collectMemoryMetrics() {
    // Use actual memory info if available
    if (performance.memory) {
        const used = performance.memory.usedJSHeapSize;
        const total = performance.memory.totalJSHeapSize;
        return (used / total) * 100;
    }
    
    // Simulate memory usage
    const baseUsage = 45;
    const variation = Math.cos(Date.now() / 15000) * 25;
    const randomNoise = (Math.random() - 0.5) * 15;
    
    return Math.max(0, Math.min(100, baseUsage + variation + randomNoise));
}

// Collect network metrics
async function collectNetworkMetrics() {
    try {
        // Test network speed with a small request
        const startTime = performance.now();
        
        // Make a small request to test connectivity
        const response = await fetch('data:text/plain,test', { 
            method: 'HEAD',
            cache: 'no-cache'
        });
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        // Convert to a 0-100 scale (lower is better)
        return Math.min(100, responseTime / 10);
        
    } catch (error) {
        return 100; // Max value indicates network issues
    }
}

// Collect database metrics
async function collectDatabaseMetrics() {
    try {
        if (!supabaseClient) return 50; // Default value
        
        const startTime = performance.now();
        
        // Test database response time
        const { error } = await supabaseClient
            .from('mods')
            .select('count', { count: 'exact', head: true });
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        if (error) return 100; // Max value indicates database issues
        
        // Convert to a 0-100 scale (lower is better)
        return Math.min(100, responseTime / 20);
        
    } catch (error) {
        return 100; // Max value indicates database issues
    }
}

// Collect storage metrics
async function collectStorageMetrics() {
    try {
        // Estimate storage usage
        if (navigator.storage && navigator.storage.estimate) {
            const estimate = await navigator.storage.estimate();
            const usage = estimate.usage || 0;
            const quota = estimate.quota || 1;
            return (usage / quota) * 100;
        }
        
        // Simulate storage usage
        const baseUsage = 35;
        const variation = Math.sin(Date.now() / 20000) * 15;
        
        return Math.max(0, Math.min(100, baseUsage + variation));
        
    } catch (error) {
        return 50; // Default value
    }
}

// Update performance UI
function updatePerformanceUI() {
    const latest = getLatestMetrics();
    
    // Update metric displays
    updateMetricDisplay('cpu-usage-display', latest.cpu, '%');
    updateMetricDisplay('memory-usage-display', latest.memory, '%');
    updateMetricDisplay('network-latency-display', latest.network, 'ms');
    updateMetricDisplay('database-response-display', latest.database, 'ms');
    updateMetricDisplay('storage-usage-display', latest.storage, '%');
    
    // Update progress bars
    updateProgressBar('cpu-progress', latest.cpu);
    updateProgressBar('memory-progress', latest.memory);
    updateProgressBar('network-progress', latest.network);
    updateProgressBar('database-progress', latest.database);
    updateProgressBar('storage-progress', latest.storage);
    
    // Update charts if available
    updatePerformanceCharts();
}

// Get latest metrics
function getLatestMetrics() {
    return {
        cpu: performanceData.cpu.length > 0 ? performanceData.cpu[performanceData.cpu.length - 1].value : 0,
        memory: performanceData.memory.length > 0 ? performanceData.memory[performanceData.memory.length - 1].value : 0,
        network: performanceData.network.length > 0 ? performanceData.network[performanceData.network.length - 1].value : 0,
        database: performanceData.database.length > 0 ? performanceData.database[performanceData.database.length - 1].value : 0,
        storage: performanceData.storage.length > 0 ? performanceData.storage[performanceData.storage.length - 1].value : 0
    };
}

// Update metric display
function updateMetricDisplay(elementId, value, unit) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = Math.round(value) + unit;
        
        // Add color coding
        element.className = 'metric-display ' + getMetricClass(value);
    }
}

// Update progress bar
function updateProgressBar(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        const bar = element.querySelector('.progress-fill');
        if (bar) {
            bar.style.width = Math.round(value) + '%';
            bar.className = 'progress-fill ' + getMetricClass(value);
        }
    }
}

// Get metric class based on value
function getMetricClass(value) {
    if (value < 50) return 'good';
    if (value < 80) return 'warning';
    return 'critical';
}

// Check performance alerts
function checkPerformanceAlerts() {
    const latest = getLatestMetrics();
    
    // Check CPU alert
    if (latest.cpu > alertThresholds.cpu) {
        showPerformanceAlert('CPU', latest.cpu, alertThresholds.cpu);
    }
    
    // Check memory alert
    if (latest.memory > alertThresholds.memory) {
        showPerformanceAlert('Memory', latest.memory, alertThresholds.memory);
    }
    
    // Check database response time
    if (latest.database > alertThresholds.responseTime) {
        showPerformanceAlert('Database Response', latest.database, alertThresholds.responseTime);
    }
}

// Show performance alert
function showPerformanceAlert(metric, currentValue, threshold) {
    const alertId = `alert-${metric.toLowerCase().replace(' ', '-')}`;
    
    // Avoid duplicate alerts
    if (document.getElementById(alertId)) return;
    
    const alert = document.createElement('div');
    alert.id = alertId;
    alert.className = 'performance-alert critical';
    alert.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="alert-content">
            <h4>تحذير أداء: ${metric}</h4>
            <p>القيمة الحالية: ${Math.round(currentValue)} | الحد الأقصى: ${threshold}</p>
        </div>
        <button class="alert-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    const alertContainer = document.getElementById('performance-alerts');
    if (alertContainer) {
        alertContainer.appendChild(alert);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 10000);
    }
}

// Setup monitoring UI
function setupMonitoringUI() {
    // Setup toggle button
    const toggleButton = document.getElementById('monitoring-toggle');
    if (toggleButton) {
        toggleButton.addEventListener('click', () => {
            if (monitoringActive) {
                stopPerformanceMonitoring();
            } else {
                startPerformanceMonitoring();
            }
            savePerformanceSettings();
        });
    }
    
    // Setup threshold inputs
    Object.keys(alertThresholds).forEach(metric => {
        const input = document.getElementById(`threshold-${metric}`);
        if (input) {
            input.value = alertThresholds[metric];
            input.addEventListener('change', (e) => {
                alertThresholds[metric] = parseFloat(e.target.value);
                savePerformanceSettings();
            });
        }
    });
    
    updateMonitoringStatus();
}

// Update monitoring status
function updateMonitoringStatus() {
    const statusElement = document.getElementById('monitoring-status');
    const toggleButton = document.getElementById('monitoring-toggle');
    
    if (statusElement) {
        statusElement.textContent = monitoringActive ? 'نشط' : 'متوقف';
        statusElement.className = `monitoring-status ${monitoringActive ? 'active' : 'inactive'}`;
    }
    
    if (toggleButton) {
        toggleButton.textContent = monitoringActive ? 'إيقاف المراقبة' : 'بدء المراقبة';
        toggleButton.className = `monitoring-toggle ${monitoringActive ? 'stop' : 'start'}`;
    }
}

// Initialize performance charts
function initializePerformanceCharts() {
    // This would initialize Chart.js charts for real-time data
    console.log('Performance charts initialized');
}

// Update performance charts
function updatePerformanceCharts() {
    // This would update Chart.js charts with new data
    // For now, just log the data
    if (performanceData.cpu.length > 0) {
        console.log('Chart data updated:', {
            cpu: performanceData.cpu.slice(-10),
            memory: performanceData.memory.slice(-10)
        });
    }
}

// Export performance data
function exportPerformanceData() {
    const data = {
        timestamp: new Date().toISOString(),
        metrics: performanceData,
        settings: {
            alertThresholds,
            monitoringActive
        }
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
}

// Clear performance data
function clearPerformanceData() {
    if (confirm('هل أنت متأكد من مسح جميع بيانات الأداء؟')) {
        performanceData = {
            cpu: [],
            memory: [],
            network: [],
            database: [],
            storage: []
        };
        
        updatePerformanceUI();
        console.log('Performance data cleared');
    }
}

// Generate performance report
function generatePerformanceReport() {
    const latest = getLatestMetrics();
    const averages = calculateAverages();
    
    const report = `
تقرير أداء النظام
================

التاريخ: ${new Date().toLocaleDateString('ar-SA')}
الوقت: ${new Date().toLocaleTimeString('ar-SA')}

المقاييس الحالية:
- استخدام المعالج: ${Math.round(latest.cpu)}%
- استخدام الذاكرة: ${Math.round(latest.memory)}%
- زمن استجابة الشبكة: ${Math.round(latest.network)}ms
- زمن استجابة قاعدة البيانات: ${Math.round(latest.database)}ms
- استخدام التخزين: ${Math.round(latest.storage)}%

المتوسطات:
- متوسط استخدام المعالج: ${Math.round(averages.cpu)}%
- متوسط استخدام الذاكرة: ${Math.round(averages.memory)}%
- متوسط زمن استجابة الشبكة: ${Math.round(averages.network)}ms
- متوسط زمن استجابة قاعدة البيانات: ${Math.round(averages.database)}ms
- متوسط استخدام التخزين: ${Math.round(averages.storage)}%

حالة النظام: ${getSystemHealthStatus(latest)}
    `;
    
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
}

// Calculate averages
function calculateAverages() {
    const averages = {};
    
    Object.keys(performanceData).forEach(metric => {
        const data = performanceData[metric];
        if (data.length > 0) {
            const sum = data.reduce((acc, item) => acc + item.value, 0);
            averages[metric] = sum / data.length;
        } else {
            averages[metric] = 0;
        }
    });
    
    return averages;
}

// Get system health status
function getSystemHealthStatus(metrics) {
    const criticalCount = Object.values(metrics).filter(value => value > 80).length;
    
    if (criticalCount === 0) return 'ممتاز';
    if (criticalCount <= 2) return 'جيد';
    return 'يحتاج انتباه';
}

// Make functions globally available
window.startPerformanceMonitoring = startPerformanceMonitoring;
window.stopPerformanceMonitoring = stopPerformanceMonitoring;
window.exportPerformanceData = exportPerformanceData;
window.clearPerformanceData = clearPerformanceData;
window.generatePerformanceReport = generatePerformanceReport;
