// إعداد Firebase للنسخ الاحتياطي
// Firebase Backup Configuration

// إعدادات Firebase للنسخ الاحتياطي
const firebaseBackupConfig = {
    // إعدادات Firebase الأساسية
    apiKey: "AIzaSyBYX8Qw5K7N9mP3L2R4S6T8U0V2W4Y6Z8A",
    authDomain: "minecraft-mods-backup.firebaseapp.com",
    databaseURL: "https://minecraft-mods-backup-default-rtdb.firebaseio.com",
    projectId: "minecraft-mods-backup",
    storageBucket: "minecraft-mods-backup.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789",
    
    // إعدادات النسخ الاحتياطي
    backupSettings: {
        // حجم الدفعة للنسخ
        batchSize: 1000,
        
        // التأخير بين الدفعات (بالميلي ثانية)
        batchDelay: 100,
        
        // عدد محاولات إعادة المحاولة
        maxRetries: 3,
        
        // مهلة انتظار العمليات (بالثواني)
        operationTimeout: 30,
        
        // ضغط البيانات
        compressData: true,
        
        // تشفير البيانات الحساسة
        encryptSensitiveData: true,
        
        // الاحتفاظ بالنسخ الاحتياطية (بالأيام)
        retentionDays: 90,
        
        // النسخ الاحتياطي التلقائي
        autoBackup: {
            enabled: true,
            interval: 24, // كل 24 ساعة
            time: "02:00" // في الساعة 2:00 صباحاً
        }
    },
    
    // قواعد الأمان
    securityRules: {
        // السماح بالقراءة والكتابة للمشرفين فقط
        adminOnly: true,
        
        // تشفير البيانات الحساسة
        encryptionKey: "minecraft-mods-backup-key-2024",
        
        // قائمة المستخدمين المصرح لهم
        authorizedUsers: [
            "<EMAIL>",
            "<EMAIL>"
        ]
    }
};

// فئة إدارة إعدادات Firebase
class FirebaseBackupConfig {
    constructor() {
        this.config = firebaseBackupConfig;
        this.isInitialized = false;
    }
    
    // تهيئة Firebase
    async initialize() {
        try {
            if (this.isInitialized) {
                console.log('⚠️ Firebase مهيأ مسبقاً');
                return true;
            }
            
            // التحقق من توفر Firebase
            if (typeof firebase === 'undefined') {
                console.error('❌ Firebase SDK غير محمل');
                return false;
            }
            
            // تهيئة Firebase إذا لم يكن مهيأ
            if (!firebase.apps.length) {
                firebase.initializeApp(this.config);
                console.log('✅ تم تهيئة Firebase للنسخ الاحتياطي');
            } else {
                console.log('✅ Firebase مهيأ مسبقاً');
            }
            
            // اختبار الاتصال
            const testResult = await this.testConnection();
            if (testResult) {
                this.isInitialized = true;
                console.log('✅ تم التحقق من اتصال Firebase');
                return true;
            } else {
                console.error('❌ فشل في اختبار اتصال Firebase');
                return false;
            }
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة Firebase:', error);
            return false;
        }
    }
    
    // اختبار الاتصال
    async testConnection() {
        try {
            const firestore = firebase.firestore();
            
            // محاولة كتابة وقراءة وثيقة اختبار
            const testDoc = firestore.collection('system').doc('connection_test');
            
            await testDoc.set({
                timestamp: new Date().toISOString(),
                test: true
            });
            
            const doc = await testDoc.get();
            
            if (doc.exists) {
                // حذف وثيقة الاختبار
                await testDoc.delete();
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('❌ خطأ في اختبار اتصال Firebase:', error);
            return false;
        }
    }
    
    // الحصول على إعدادات النسخ الاحتياطي
    getBackupSettings() {
        return this.config.backupSettings;
    }
    
    // تحديث إعدادات النسخ الاحتياطي
    updateBackupSettings(newSettings) {
        this.config.backupSettings = {
            ...this.config.backupSettings,
            ...newSettings
        };
        
        // حفظ الإعدادات في localStorage
        localStorage.setItem('firebaseBackupSettings', JSON.stringify(this.config.backupSettings));
        
        console.log('✅ تم تحديث إعدادات النسخ الاحتياطي');
    }
    
    // تحميل الإعدادات من localStorage
    loadSettingsFromStorage() {
        try {
            const savedSettings = localStorage.getItem('firebaseBackupSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.config.backupSettings = {
                    ...this.config.backupSettings,
                    ...settings
                };
                console.log('✅ تم تحميل إعدادات النسخ الاحتياطي من التخزين المحلي');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات من التخزين المحلي:', error);
        }
    }
    
    // الحصول على مرجع Firestore
    getFirestore() {
        if (!this.isInitialized) {
            throw new Error('Firebase غير مهيأ');
        }
        return firebase.firestore();
    }
    
    // الحصول على مرجع Storage
    getStorage() {
        if (!this.isInitialized) {
            throw new Error('Firebase غير مهيأ');
        }
        return firebase.storage();
    }
    
    // تشفير البيانات الحساسة
    encryptData(data) {
        if (!this.config.backupSettings.encryptSensitiveData) {
            return data;
        }
        
        try {
            // تشفير بسيط (يجب استخدام مكتبة تشفير قوية في الإنتاج)
            const encrypted = btoa(JSON.stringify(data));
            return {
                _encrypted: true,
                data: encrypted
            };
        } catch (error) {
            console.error('❌ خطأ في تشفير البيانات:', error);
            return data;
        }
    }
    
    // فك تشفير البيانات
    decryptData(encryptedData) {
        if (!encryptedData._encrypted) {
            return encryptedData;
        }
        
        try {
            const decrypted = JSON.parse(atob(encryptedData.data));
            return decrypted;
        } catch (error) {
            console.error('❌ خطأ في فك تشفير البيانات:', error);
            return encryptedData;
        }
    }
    
    // ضغط البيانات
    compressData(data) {
        if (!this.config.backupSettings.compressData) {
            return data;
        }
        
        try {
            // ضغط بسيط عبر JSON.stringify مع إزالة المسافات
            const compressed = JSON.stringify(data);
            return {
                _compressed: true,
                data: compressed,
                originalSize: JSON.stringify(data, null, 2).length,
                compressedSize: compressed.length
            };
        } catch (error) {
            console.error('❌ خطأ في ضغط البيانات:', error);
            return data;
        }
    }
    
    // إلغاء ضغط البيانات
    decompressData(compressedData) {
        if (!compressedData._compressed) {
            return compressedData;
        }
        
        try {
            return JSON.parse(compressedData.data);
        } catch (error) {
            console.error('❌ خطأ في إلغاء ضغط البيانات:', error);
            return compressedData;
        }
    }
    
    // التحقق من صلاحيات المستخدم
    checkUserPermissions(userEmail) {
        if (!this.config.securityRules.adminOnly) {
            return true;
        }
        
        return this.config.securityRules.authorizedUsers.includes(userEmail);
    }
    
    // إنشاء معرف فريد للنسخة الاحتياطية
    generateBackupId() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const random = Math.random().toString(36).substring(2, 8);
        return `backup_${timestamp}_${random}`;
    }
    
    // تنظيف النسخ الاحتياطية القديمة تلقائياً
    async cleanupOldBackups() {
        try {
            const retentionDays = this.config.backupSettings.retentionDays;
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            
            const firestore = this.getFirestore();
            
            const oldBackups = await firestore
                .collection('backup_metadata')
                .where('timestamp', '<', cutoffDate.toISOString())
                .get();
            
            let deletedCount = 0;
            
            for (const doc of oldBackups.docs) {
                try {
                    // حذف بيانات النسخة الاحتياطية
                    await this.deleteBackupData(doc.id);
                    
                    // حذف معلومات النسخة
                    await doc.ref.delete();
                    
                    deletedCount++;
                } catch (error) {
                    console.error(`خطأ في حذف النسخة ${doc.id}:`, error);
                }
            }
            
            console.log(`✅ تم حذف ${deletedCount} نسخة احتياطية قديمة تلقائياً`);
            
            return deletedCount;
            
        } catch (error) {
            console.error('❌ خطأ في تنظيف النسخ الاحتياطية القديمة:', error);
            return 0;
        }
    }
    
    // حذف بيانات نسخة احتياطية محددة
    async deleteBackupData(backupId) {
        try {
            const firestore = this.getFirestore();
            const batch = firestore.batch();
            
            // حذف جميع المجموعات الفرعية للنسخة الاحتياطية
            const backupCollection = firestore.collection(`backup_${backupId}`);
            const snapshot = await backupCollection.get();
            
            snapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            
            await batch.commit();
            
            console.log(`✅ تم حذف بيانات النسخة الاحتياطية: ${backupId}`);
            
        } catch (error) {
            console.error(`❌ خطأ في حذف بيانات النسخة ${backupId}:`, error);
            throw error;
        }
    }
    
    // جدولة النسخ الاحتياطي التلقائي
    scheduleAutoBackup() {
        const settings = this.config.backupSettings.autoBackup;
        
        if (!settings.enabled) {
            console.log('⚠️ النسخ الاحتياطي التلقائي معطل');
            return;
        }
        
        const intervalMs = settings.interval * 60 * 60 * 1000; // تحويل الساعات إلى ميلي ثانية
        
        setInterval(async () => {
            try {
                console.log('🔄 بدء النسخ الاحتياطي التلقائي...');
                
                // التحقق من الوقت المحدد
                const now = new Date();
                const currentTime = now.toTimeString().substring(0, 5);
                
                if (currentTime === settings.time) {
                    // تنفيذ النسخ الاحتياطي
                    if (window.databaseBackupSystem) {
                        await window.databaseBackupSystem.createIncrementalBackup();
                        console.log('✅ تم إنجاز النسخ الاحتياطي التلقائي');
                    }
                    
                    // تنظيف النسخ القديمة
                    await this.cleanupOldBackups();
                }
                
            } catch (error) {
                console.error('❌ خطأ في النسخ الاحتياطي التلقائي:', error);
            }
        }, intervalMs);
        
        console.log(`✅ تم جدولة النسخ الاحتياطي التلقائي كل ${settings.interval} ساعة`);
    }
    
    // الحصول على إحصائيات الاستخدام
    async getUsageStatistics() {
        try {
            const firestore = this.getFirestore();
            
            // إحصائيات النسخ الاحتياطية
            const backupsSnapshot = await firestore.collection('backup_metadata').get();
            const totalBackups = backupsSnapshot.size;
            
            let totalSize = 0;
            let successfulBackups = 0;
            
            backupsSnapshot.docs.forEach(doc => {
                const data = doc.data();
                if (data.status === 'completed') {
                    successfulBackups++;
                }
                totalSize += data.totalRecords || 0;
            });
            
            return {
                totalBackups,
                successfulBackups,
                failedBackups: totalBackups - successfulBackups,
                totalRecords: totalSize,
                successRate: totalBackups > 0 ? (successfulBackups / totalBackups * 100).toFixed(2) : 0
            };
            
        } catch (error) {
            console.error('❌ خطأ في جلب إحصائيات الاستخدام:', error);
            return null;
        }
    }
}

// إنشاء مثيل عام للإعدادات
window.firebaseBackupConfig = new FirebaseBackupConfig();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // تحميل الإعدادات من التخزين المحلي
        window.firebaseBackupConfig.loadSettingsFromStorage();
        
        // تهيئة Firebase
        await window.firebaseBackupConfig.initialize();
        
        // جدولة النسخ الاحتياطي التلقائي
        window.firebaseBackupConfig.scheduleAutoBackup();
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة إعدادات Firebase:', error);
    }
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { firebaseBackupConfig, FirebaseBackupConfig };
}
