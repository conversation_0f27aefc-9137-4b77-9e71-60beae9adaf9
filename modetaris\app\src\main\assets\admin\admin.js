// Admin.js - Banner management functionality

// DOM Elements
const bannerTitleInput = document.getElementById('banner-title');
const bannerDescriptionInput = document.getElementById('banner-description');
const bannerImageUrlInput = document.getElementById('banner-image-url');
const bannerImageFileInput = document.getElementById('banner-image-file');
const bannerTargetUrlInput = document.getElementById('banner-target-url');
const bannerDisplayOrderInput = document.getElementById('banner-display-order');
const bannerIsActiveInput = document.getElementById('banner-is-active');
const bannerCampaignIdInput = document.getElementById('banner-campaign-id'); // New
const bannerDisplayTypeInput = document.getElementById('banner-display-type'); // New
const bannerBannerTypeInput = document.getElementById('banner-banner-type'); // New
const bannerIdInput = document.getElementById('banner-id');
const saveBannerButton = document.getElementById('save-banner');
const previewBannerButton = document.getElementById('preview-banner');
const resetFormButton = document.getElementById('reset-form');
const bannerListContainer = document.getElementById('banner-list');
const previewImage = document.getElementById('preview-image');
const previewPlaceholder = document.getElementById('preview-placeholder');

// Event Listeners
document.addEventListener('DOMContentLoaded', async () => { // Changed to async
    await loadCampaigns(); // New: Load campaigns first
    loadBanners();

    saveBannerButton.addEventListener('click', saveBanner);
    previewBannerButton.addEventListener('click', showBannerPreview);
    resetFormButton.addEventListener('click', resetForm);

    // Preview image when URL changes
    bannerImageUrlInput.addEventListener('input', updateImagePreview);

    // Handle file upload
    bannerImageFileInput.addEventListener('change', handleImageUpload);
});

// Functions
async function loadBanners() {
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching banners:', error);
            bannerListContainer.innerHTML = '<p style="color: red;">حدث خطأ أثناء تحميل البانرات</p>';
            return;
        }

        if (!data || data.length === 0) {
            bannerListContainer.innerHTML = '<p>لا توجد بانرات إعلانية حاليًا</p>';
            return;
        }

        renderBannerList(data);
    } catch (error) {
        console.error('Unexpected error loading banners:', error);
        bannerListContainer.innerHTML = '<p style="color: red;">حدث خطأ غير متوقع</p>';
    }
}

// New function to load free subscription campaigns
async function loadCampaigns() {
    try {
        const { data, error } = await supabaseClient
            .from('free_subscription_campaigns') // Assuming table name
            .select('id, title_ar, title_en')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching campaigns:', error);
            return;
        }

        bannerCampaignIdInput.innerHTML = '<option value="">اختر حملة</option>'; // Reset options
        data.forEach(campaign => {
            const option = document.createElement('option');
            option.value = campaign.id;
            option.textContent = campaign.title_ar || campaign.title_en || campaign.id;
            bannerCampaignIdInput.appendChild(option);
        });
    } catch (error) {
        console.error('Unexpected error loading campaigns:', error);
    }
}

function renderBannerList(banners) {
    bannerListContainer.innerHTML = '<h3>البانرات الحالية</h3>';

    banners.forEach(banner => {
        const bannerItem = document.createElement('div');
        bannerItem.className = 'banner-item';

        bannerItem.innerHTML = `
            <img src="${banner.image_url}" alt="${banner.title}" class="banner-image">
            <div class="banner-info">
                <div class="banner-title">
                    ${banner.title || 'بدون عنوان'}
                    <span class="status-badge ${banner.is_active ? 'status-active' : 'status-inactive'}">
                        ${banner.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
                <div>الترتيب: ${banner.display_order}</div>
            </div>
            <div class="banner-actions">
                <button class="edit-button" title="تعديل" onclick="editBanner(${banner.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-button" title="حذف" onclick="deleteBanner(${banner.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        bannerListContainer.appendChild(bannerItem);
    });
}

async function saveBanner() {
    const title = bannerTitleInput.value.trim();
    const description = bannerDescriptionInput.value.trim();
    const imageUrl = bannerImageUrlInput.value.trim();
    const targetUrl = bannerTargetUrlInput.value.trim();
    const displayOrder = parseInt(bannerDisplayOrderInput.value) || 1;
    const isActive = bannerIsActiveInput.checked;
    const campaignId = bannerCampaignIdInput.value || null; // New
    const displayType = bannerDisplayTypeInput.value; // New
    const bannerType = bannerBannerTypeInput.value; // New
    const bannerId = bannerIdInput.value;

    if (!imageUrl) {
        alert('يرجى إدخال رابط صورة البانر');
        return;
    }

    if (bannerType === 'subscription' && !campaignId) {
        alert('يرجى اختيار حملة اشتراك مجاني للبانرات من نوع "subscription"');
        return;
    }

    try {
        let result;

        if (bannerId) {
            // Update existing banner
            result = await supabaseClient
                .from(BANNER_ADS_TABLE)
                .update({
                    title,
                    description,
                    image_url: imageUrl,
                    target_url: targetUrl,
                    display_order: displayOrder,
                    is_active: isActive,
                    campaign_id: campaignId, // New
                    display_type: displayType, // New
                    banner_type: bannerType, // New
                    updated_at: new Date()
                })
                .eq('id', bannerId);
        } else {
            // Insert new banner
            result = await supabaseClient
                .from(BANNER_ADS_TABLE)
                .insert({
                    title,
                    description,
                    image_url: imageUrl,
                    target_url: targetUrl,
                    display_order: displayOrder,
                    is_active: isActive,
                    campaign_id: campaignId, // New
                    display_type: displayType, // New
                    banner_type: bannerType, // New
                    created_at: new Date()
                });
        }

        if (result.error) {
            console.error('Error saving banner:', result.error);
            alert('حدث خطأ أثناء حفظ البانر');
            return;
        }

        alert(bannerId ? 'تم تحديث البانر بنجاح' : 'تم إضافة البانر بنجاح');
        resetForm();
        loadBanners();
    } catch (error) {
        console.error('Unexpected error saving banner:', error);
        alert('حدث خطأ غير متوقع');
    }
}

function resetForm() {
    bannerTitleInput.value = '';
    bannerDescriptionInput.value = '';
    bannerImageUrlInput.value = '';
    bannerImageFileInput.value = ''; // Reset file input
    bannerTargetUrlInput.value = '';
    bannerDisplayOrderInput.value = '1';
    bannerIsActiveInput.checked = true;
    bannerCampaignIdInput.value = ''; // New
    bannerIdInput.value = '';
    saveBannerButton.textContent = 'حفظ البانر';

    // Reset preview
    previewImage.style.display = 'none';
    previewPlaceholder.style.display = 'block';
    previewPlaceholder.textContent = 'سيتم عرض معاينة الصورة هنا';
}

async function editBanner(id) {
    try {
        const { data, error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            console.error('Error fetching banner for edit:', error);
            alert('حدث خطأ أثناء تحميل بيانات البانر');
            return;
        }

        if (!data) {
            alert('لم يتم العثور على البانر');
            return;
        }

        // Fill form with banner data
        bannerTitleInput.value = data.title || '';
        bannerDescriptionInput.value = data.description || '';
        bannerImageUrlInput.value = data.image_url || '';
        bannerTargetUrlInput.value = data.target_url || '';
        bannerDisplayOrderInput.value = data.display_order || 1;
        bannerIsActiveInput.checked = data.is_active;
        bannerCampaignIdInput.value = data.campaign_id || ''; // New
        bannerIdInput.value = data.id;

        saveBannerButton.textContent = 'تحديث البانر';

        // Update preview
        updateImagePreview();

        // Scroll to form
        document.querySelector('.admin-form').scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
        console.error('Unexpected error editing banner:', error);
        alert('حدث خطأ غير متوقع');
    }
}

async function deleteBanner(id) {
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا البانر؟')) {
        return;
    }

    try {
        const { error } = await supabaseClient
            .from(BANNER_ADS_TABLE)
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error deleting banner:', error);
            alert('حدث خطأ أثناء حذف البانر');
            return;
        }

        alert('تم حذف البانر بنجاح');
        loadBanners();
    } catch (error) {
        console.error('Unexpected error deleting banner:', error);
        alert('حدث خطأ غير متوقع');
    }
}

function updateImagePreview() {
    const imageUrl = bannerImageUrlInput.value.trim();

    if (imageUrl) {
        previewImage.src = imageUrl;
        previewImage.style.display = 'block';
        previewPlaceholder.style.display = 'none';

        // Handle image load error
        previewImage.onerror = () => {
            previewImage.style.display = 'none';
            previewPlaceholder.style.display = 'block';
            previewPlaceholder.textContent = 'تعذر تحميل الصورة';
        };
    } else {
        previewImage.style.display = 'none';
        previewPlaceholder.style.display = 'block';
        previewPlaceholder.textContent = 'سيتم عرض معاينة الصورة هنا';
    }
}

// Función para manejar la carga de imágenes
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Verificar que sea una imagen
    if (!file.type.startsWith('image/')) {
        alert('الرجاء تحميل ملف صورة فقط');
        return;
    }

    // Mostrar mensaje de carga
    previewPlaceholder.style.display = 'block';
    previewImage.style.display = 'none';
    previewPlaceholder.textContent = 'جاري تحميل الصورة...';

    // Convertir la imagen a una URL de datos (base64)
    const reader = new FileReader();
    reader.onload = function(e) {
        // Actualizar el campo de URL con la URL de datos
        bannerImageUrlInput.value = e.target.result;

        // Actualizar la vista previa
        previewImage.src = e.target.result;
        previewImage.style.display = 'block';
        previewPlaceholder.style.display = 'none';
    };

    reader.onerror = function() {
        previewPlaceholder.textContent = 'فشل في تحميل الصورة';
        alert('حدث خطأ أثناء تحميل الصورة');
    };

    reader.readAsDataURL(file);
}

// Función para mostrar la previsualización del banner
function showBannerPreview() {
    const imageUrl = bannerImageUrlInput.value.trim();
    const title = bannerTitleInput.value.trim();
    const description = bannerDescriptionInput.value.trim();
    const targetUrl = bannerTargetUrlInput.value.trim();

    if (!imageUrl) {
        alert('يرجى إدخال رابط صورة البانر أولاً');
        return;
    }

    // Crear el modal de previsualización
    const modal = document.createElement('div');
    modal.className = 'preview-modal';

    modal.innerHTML = `
        <div class="preview-modal-content">
            <div class="preview-modal-header">
                <h3 class="preview-modal-title">معاينة البانر</h3>
                <button class="preview-modal-close">&times;</button>
            </div>
            <div class="preview-modal-body">
                <div class="preview-device-frame">
                    <div class="preview-device-header">معاينة البانر كما سيظهر في التطبيق</div>
                    <div class="preview-device-content">
                        <div class="preview-banner-container">
                            <img src="${imageUrl}" alt="${title || 'Banner Preview'}" class="preview-banner-image">
                        </div>
                    </div>
                </div>

                <div class="preview-device-frame">
                    <div class="preview-device-header">معاينة نافذة البانر عند النقر عليه</div>
                    <div style="padding: 15px; text-align: center;">
                        <h4 style="color: white; margin-top: 0;">${title || 'عنوان البانر'}</h4>
                        <p style="color: #ccc; font-size: 0.9rem;">${description || 'وصف البانر (اختياري)'}</p>
                        <button style="background: linear-gradient(45deg, #ffd700, #ffcc00); color: black; border: none; padding: 8px 15px; border-radius: 5px; font-weight: bold;">متابعة</button>
                        <p style="color: #999; font-size: 0.8rem; margin-top: 10px;">سيتم توجيه المستخدم إلى: ${targetUrl || 'لم يتم تحديد رابط'}</p>
                    </div>
                </div>
            </div>
            <div class="preview-modal-footer">
                <button class="preview-modal-button">إغلاق</button>
            </div>
        </div>
    `;

    // Añadir el modal al DOM
    document.body.appendChild(modal);

    // Manejar el cierre del modal
    const closeButton = modal.querySelector('.preview-modal-close');
    const closeButtonFooter = modal.querySelector('.preview-modal-button');

    closeButton.addEventListener('click', () => {
        modal.remove();
    });

    closeButtonFooter.addEventListener('click', () => {
        modal.remove();
    });

    // Cerrar el modal al hacer clic fuera del contenido
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// Make functions available globally
window.editBanner = editBanner;
window.deleteBanner = deleteBanner;
