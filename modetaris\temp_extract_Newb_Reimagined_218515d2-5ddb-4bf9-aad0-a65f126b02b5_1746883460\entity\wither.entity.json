{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:wither", "min_engine_version": "1.8.0", "materials": {"default": "enderman", "armor": "wither_boss_armor"}, "textures": {"default": "textures/entity/wither/wither", "armor_white": "textures/entity/wither/wither_armor_white", "armor_blue": "textures/entity/wither/wither_armor_blue", "invulnerable": "textures/entity/wither/wither_invulnerable"}, "geometry": {"default": "geometry.wither", "armor": "geometry.wither.armor.v1.8"}, "scripts": {"pre_animation": ["variable.base_scale = 2;", "variable.swell_clamped = Math.clamp(query.swell_amount, 0.0, 1.0);", "variable.wobble = 1.0 + Math.sin(query.swell_amount * 5730) * query.swell_amount * 0.01;", "variable.swell_adjustment = Math.pow(variable.swell_clamped, 4);", "variable.scale_xz = (1.0 + variable.swell_adjustment * 0.4) * variable.wobble;", "variable.scale_y = (1.0 + variable.swell_adjustment * 0.1) / variable.wobble;", "variable.body_base_rotation = Math.cos(query.life_time * 114.6);", "variable.upper_body_rotation = (0.065 + 0.05 * variable.body_base_rotation) * 180 + query.target_x_rotation;", "variable.is_invulnerable = query.invulnerable_ticks > 0.0;", "variable.display_normal_skin = (query.invulnerable_ticks <= 0) || ((query.invulnerable_ticks <= 80) && (Math.mod(query.invulnerable_ticks / 5, 2) == 1));"], "scalex": "variable.scale_xz * variable.base_scale", "scaley": "variable.scale_y * variable.base_scale", "scalez": "variable.scale_xz * variable.base_scale"}, "animations": {"scale": "animation.wither.scale", "move": "animation.wither.move", "look_at_target": "animation.wither.look_at_target"}, "animation_controllers": [{"move": "controller.animation.wither.move"}], "render_controllers": ["controller.render.wither", "controller.render.wither_armor_white", "controller.render.wither_armor_blue"]}}}