// New Mods Settings Management
let supabaseClient;

// Initialize Supabase
function initializeSupabase() {
    try {
        const supabaseUrl = 'https://ytqxxodyecdeosnqoure.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
        
        supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
        console.log('✅ Supabase initialized successfully');
        
        // Check database connection
        checkDatabaseConnection();
        
    } catch (error) {
        console.error('❌ Error initializing Supabase:', error);
        showError('فشل في الاتصال بقاعدة البيانات');
    }
}

// Check database connection
async function checkDatabaseConnection() {
    try {
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id')
            .limit(1);
            
        if (error) {
            throw error;
        }
        
        document.getElementById('dbStatus').innerHTML = `
            متصل
            <span class="status-indicator status-active"></span>
        `;
        
        // Load current new mods count
        loadCurrentNewModsCount();
        
    } catch (error) {
        console.error('❌ Database connection failed:', error);
        document.getElementById('dbStatus').innerHTML = `
            غير متصل
            <span class="status-indicator status-inactive"></span>
        `;
    }
}

// Load current new mods count
async function loadCurrentNewModsCount() {
    try {
        const currentDuration = getCurrentDuration();
        const daysAgo = new Date();
        daysAgo.setDate(daysAgo.getDate() - currentDuration);
        
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id', { count: 'exact' })
            .gte('created_at', daysAgo.toISOString());
            
        if (error) {
            throw error;
        }
        
        document.getElementById('currentNewMods').textContent = `${data.length} مود`;
        
    } catch (error) {
        console.error('❌ Error loading new mods count:', error);
        document.getElementById('currentNewMods').textContent = 'خطأ في التحميل';
    }
}

// Get current duration from settings
function getCurrentDuration() {
    return parseInt(localStorage.getItem('newModsDuration') || '7');
}

// Update duration display
function updateDurationDisplay(value) {
    document.getElementById('durationValue').textContent = value;
    document.getElementById('previewDuration').textContent = value;
    document.getElementById('customDuration').value = value;
}

// Update slider from input
function updateSliderFromInput(value) {
    if (value >= 1 && value <= 365) {
        document.getElementById('durationSlider').value = Math.min(value, 30);
        updateDurationDisplay(value);
    }
}

// Update preview values
function updatePreview() {
    const duration = document.getElementById('durationSlider').value;
    const cache = document.getElementById('cacheMinutes').value;
    const limit = document.getElementById('homePageLimit').value;
    
    document.getElementById('previewDuration').textContent = duration;
    document.getElementById('previewCache').textContent = cache;
    document.getElementById('previewLimit').textContent = limit;
}

// Save settings
async function saveSettings() {
    try {
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري الحفظ...';
        
        const duration = document.getElementById('customDuration').value || document.getElementById('durationSlider').value;
        const cacheMinutes = document.getElementById('cacheMinutes').value;
        const homePageLimit = document.getElementById('homePageLimit').value;
        
        // Validate inputs
        if (duration < 1 || duration > 365) {
            throw new Error('مدة العرض يجب أن تكون بين 1 و 365 يوم');
        }
        
        // Save to localStorage
        localStorage.setItem('newModsDuration', duration);
        localStorage.setItem('newModsCacheMinutes', cacheMinutes);
        localStorage.setItem('newModsHomePageLimit', homePageLimit);
        localStorage.setItem('newModsLastUpdate', new Date().toISOString());
        
        // Save to database (create settings table if needed)
        await saveToDatabase({
            duration: parseInt(duration),
            cache_minutes: parseInt(cacheMinutes),
            home_page_limit: parseInt(homePageLimit),
            updated_at: new Date().toISOString()
        });
        
        // Update last update display
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');
        
        showSuccess(`تم حفظ الإعدادات بنجاح! مدة العرض: ${duration} أيام`);
        
        // Clear cache to apply new settings
        clearNewModsCache();
        
        // Reload new mods count with new duration
        setTimeout(loadCurrentNewModsCount, 1000);
        
    } catch (error) {
        console.error('❌ Error saving settings:', error);
        showError('حدث خطأ أثناء حفظ الإعدادات: ' + error.message);
    } finally {
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save mr-2"></i>حفظ الإعدادات';
    }
}

// Save to database
async function saveToDatabase(settings) {
    try {
        // First, try to create the table if it doesn't exist
        const { error: createError } = await supabaseClient.rpc('create_new_mods_settings_table');
        
        // Insert or update settings
        const { error } = await supabaseClient
            .from('new_mods_settings')
            .upsert({
                id: 1, // Single row for global settings
                ...settings
            });
            
        if (error) {
            console.warn('Could not save to database:', error.message);
            // Continue anyway, localStorage is sufficient
        }
        
    } catch (error) {
        console.warn('Database save failed, using localStorage only:', error);
    }
}

// Test settings
async function testSettings() {
    try {
        showSuccess('جاري اختبار الإعدادات...');
        
        const duration = document.getElementById('customDuration').value || document.getElementById('durationSlider').value;
        
        // Test fetch with new duration
        const daysAgo = new Date();
        daysAgo.setDate(daysAgo.getDate() - parseInt(duration));
        
        const { data, error } = await supabaseClient
            .from('mods')
            .select('id, title, created_at')
            .gte('created_at', daysAgo.toISOString())
            .order('created_at', { ascending: false })
            .limit(5);
            
        if (error) {
            throw error;
        }
        
        showSuccess(`✅ الاختبار نجح! تم العثور على ${data.length} مود جديد خلال آخر ${duration} أيام`);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        showError('فشل الاختبار: ' + error.message);
    }
}

// Reset to default
function resetToDefault() {
    if (confirm('هل أنت متأكد من إعادة تعيين الإعدادات إلى القيم الافتراضية؟')) {
        document.getElementById('durationSlider').value = 7;
        document.getElementById('customDuration').value = '';
        document.getElementById('cacheMinutes').value = 3;
        document.getElementById('homePageLimit').value = 10;
        
        updateDurationDisplay(7);
        updatePreview();
        
        showSuccess('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
    }
}

// Clear cache
function clearCache() {
    try {
        clearNewModsCache();
        showSuccess('تم مسح التخزين المؤقت بنجاح');
    } catch (error) {
        showError('حدث خطأ أثناء مسح التخزين المؤقت');
    }
}

// Clear new mods cache
function clearNewModsCache() {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('new_mods_')) {
            localStorage.removeItem(key);
        }
    });
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    errorDiv.style.display = 'none';
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    
    setTimeout(() => {
        successDiv.style.display = 'none';
    }, 5000);
}

// Show error message
function showError(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    successDiv.style.display = 'none';
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 7000);
}

// Go back
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '../admin.html';
    }
}

// Load saved settings
function loadSavedSettings() {
    const savedDuration = localStorage.getItem('newModsDuration') || '7';
    const savedCache = localStorage.getItem('newModsCacheMinutes') || '3';
    const savedLimit = localStorage.getItem('newModsHomePageLimit') || '10';
    const lastUpdate = localStorage.getItem('newModsLastUpdate');
    
    document.getElementById('durationSlider').value = Math.min(savedDuration, 30);
    document.getElementById('customDuration').value = savedDuration;
    document.getElementById('cacheMinutes').value = savedCache;
    document.getElementById('homePageLimit').value = savedLimit;
    
    updateDurationDisplay(savedDuration);
    updatePreview();
    
    if (lastUpdate) {
        document.getElementById('lastUpdate').textContent = new Date(lastUpdate).toLocaleString('ar-SA');
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeSupabase();
    loadSavedSettings();
    
    // Add change listeners for preview updates
    document.getElementById('cacheMinutes').addEventListener('change', updatePreview);
    document.getElementById('homePageLimit').addEventListener('change', updatePreview);
    
    console.log('✅ New Mods Settings page loaded');
});
