-- ========================================
-- حل سريع لمشاكل Supabase - Quick Fix for Supabase Issues
-- نفذ هذا الكود في Supabase SQL Editor
-- Execute this code in Supabase SQL Editor
-- ========================================

-- 1. إنشاء جدول custom_mod_dialogs
CREATE TABLE IF NOT EXISTS custom_mod_dialogs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    button_text VARCHAR(100) DEFAULT 'تم',
    show_dont_show_again BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON><PERSON>ء جدول custom_dialog_mods
CREATE TABLE IF NOT EXISTS custom_dialog_mods (
    id SERIAL PRIMARY KEY,
    dialog_id INTEGER NOT NULL,
    mod_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_dialog_mods_dialog_id FOREIGN KEY (dialog_id) REFERENCES custom_mod_dialogs(id) ON DELETE CASCADE,
    CONSTRAINT fk_custom_dialog_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_dialog_mod UNIQUE (dialog_id, mod_id)
);

-- 3. إنشاء جدول custom_copyright_mods
CREATE TABLE IF NOT EXISTS custom_copyright_mods (
    id SERIAL PRIMARY KEY,
    mod_id UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_custom_copyright_mods_mod_id FOREIGN KEY (mod_id) REFERENCES mods(id) ON DELETE CASCADE,
    CONSTRAINT unique_copyright_mod UNIQUE (mod_id)
);

-- 4. إنشاء دالة execute_sql (اختيارية - لتجنب الأخطاء المستقبلية)
CREATE OR REPLACE FUNCTION execute_sql(sql_query TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    query_lower TEXT;
BEGIN
    -- تحويل إلى أحرف صغيرة للفحص
    query_lower := LOWER(TRIM(sql_query));
    
    -- فحص الأمان: منع العمليات الخطيرة
    IF query_lower LIKE '%drop %' OR 
       query_lower LIKE '%delete %' OR 
       query_lower LIKE '%truncate %' OR
       query_lower LIKE '%alter %' OR
       query_lower LIKE '%grant %' OR
       query_lower LIKE '%revoke %' OR
       query_lower LIKE '%insert %' OR
       query_lower LIKE '%update %' THEN
        
        -- السماح فقط بـ CREATE TABLE IF NOT EXISTS
        IF NOT (query_lower LIKE '%create table if not exists%') THEN
            RAISE EXCEPTION 'Only CREATE TABLE IF NOT EXISTS operations are allowed for security reasons';
        END IF;
    END IF;
    
    -- التحقق من أن الاستعلام يبدأ بـ CREATE
    IF NOT query_lower LIKE 'create%' THEN
        RAISE EXCEPTION 'Only CREATE statements are allowed';
    END IF;
    
    -- تنفيذ الاستعلام
    EXECUTE sql_query;
    
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error executing SQL: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. إضافة تعليقات توضيحية
COMMENT ON TABLE custom_mod_dialogs IS 'Stores custom dialog configurations for mods';
COMMENT ON TABLE custom_dialog_mods IS 'Junction table linking dialogs to specific mods';
COMMENT ON TABLE custom_copyright_mods IS 'Tracks mods that have custom copyright descriptions';
COMMENT ON FUNCTION execute_sql(TEXT) IS 'Safely executes CREATE TABLE statements from the application';

-- 6. إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_dialog_id ON custom_dialog_mods(dialog_id);
CREATE INDEX IF NOT EXISTS idx_custom_dialog_mods_mod_id ON custom_dialog_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_copyright_mods_mod_id ON custom_copyright_mods(mod_id);
CREATE INDEX IF NOT EXISTS idx_custom_mod_dialogs_active ON custom_mod_dialogs(is_active);

-- 7. منح الصلاحيات للدالة (قم بإلغاء التعليق إذا كنت تريد السماح للتطبيق باستخدام الدالة)
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO anon;
-- GRANT EXECUTE ON FUNCTION execute_sql(TEXT) TO authenticated;

-- ========================================
-- انتهى الحل السريع
-- Quick Fix Complete
-- ========================================

-- للتحقق من نجاح العملية، نفذ الاستعلام التالي:
-- To verify success, run the following query:

-- SELECT 
--     schemaname, 
--     tablename 
-- FROM pg_tables 
-- WHERE tablename IN ('custom_mod_dialogs', 'custom_dialog_mods', 'custom_copyright_mods')
-- ORDER BY tablename;
