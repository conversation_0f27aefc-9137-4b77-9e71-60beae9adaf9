# -*- coding: utf-8 -*-
"""
اختبار إصلاحات Supabase SSL timeout
"""

import sys
import os
import json
import time

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🔍 اختبار الاتصال الأساسي بـ Supabase...")
    
    try:
        from supabase_config import supabase_config
        
        # محاولة الاتصال
        if supabase_config.connect():
            print("✅ نجح الاتصال الأساسي")
            return True
        else:
            print("❌ فشل الاتصال الأساسي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار الأساسي: {e}")
        return False

def test_database_query():
    """اختبار استعلام قاعدة البيانات"""
    print("🔍 اختبار استعلام قاعدة البيانات...")
    
    try:
        from supabase_config import get_supabase_client
        
        client = get_supabase_client()
        if not client:
            print("❌ لا يمكن الحصول على عميل Supabase")
            return False
        
        # اختبار استعلام بسيط
        response = client.table('mods').select('id').limit(1).execute()
        
        if hasattr(response, 'data'):
            print("✅ نجح استعلام قاعدة البيانات")
            print(f"📊 عدد النتائج: {len(response.data) if response.data else 0}")
            return True
        else:
            print("❌ استجابة غير متوقعة من قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في استعلام قاعدة البيانات: {e}")
        return False

def test_publish_operation():
    """اختبار عملية النشر"""
    print("🔍 اختبار عملية النشر...")
    
    try:
        from supabase_config import publish_to_supabase
        
        # بيانات اختبار
        test_data = {
            'title': 'Test Mod - اختبار الإصلاحات',
            'description': 'هذا مود اختبار لفحص إصلاحات SSL timeout',
            'category': 'Test',
            'download_url': 'https://example.com/test.mcpack',
            'image_url': 'https://example.com/test.jpg',
            'file_size': '1.5 MB',
            'version': '1.0.0',
            'creator_name': 'Test Creator',
            'is_test': True  # علامة للتمييز أن هذا اختبار
        }
        
        # محاولة النشر
        if publish_to_supabase(test_data, 'mods'):
            print("✅ نجحت عملية النشر الاختبارية")
            
            # محاولة حذف البيانات الاختبارية
            try:
                from supabase_config import get_supabase_client
                client = get_supabase_client()
                if client:
                    # حذف البيانات الاختبارية
                    client.table('mods').delete().eq('is_test', True).execute()
                    print("🗑️ تم حذف البيانات الاختبارية")
            except:
                print("⚠️ لم يتم حذف البيانات الاختبارية (قد تحتاج لحذفها يدوياً)")
            
            return True
        else:
            print("❌ فشلت عملية النشر الاختبارية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النشر: {e}")
        return False

def test_network_connectivity():
    """اختبار الاتصال بالشبكة"""
    print("🔍 اختبار الاتصال بالشبكة...")
    
    try:
        from supabase_config import supabase_config
        
        if supabase_config.check_network_connectivity():
            print("✅ الاتصال بالإنترنت متاح")
            return True
        else:
            print("❌ لا يوجد اتصال بالإنترنت")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الشبكة: {e}")
        return False

def test_api_keys():
    """اختبار مفاتيح API"""
    print("🔍 اختبار مفاتيح API...")
    
    try:
        config_file = "api_keys.json"
        if not os.path.exists(config_file):
            print("❌ ملف api_keys.json غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        supabase_key = config.get("SUPABASE_KEY", "")
        if not supabase_key:
            print("❌ مفتاح SUPABASE_KEY غير موجود في ملف الإعدادات")
            return False
        
        if len(supabase_key) < 100:  # مفاتيح Supabase عادة طويلة
            print("⚠️ مفتاح SUPABASE_KEY قد يكون غير صحيح (قصير جداً)")
            return False
        
        print("✅ مفتاح Supabase موجود ويبدو صحيحاً")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مفاتيح API: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات إصلاحات Supabase SSL timeout...")
    print("=" * 60)
    
    tests = [
        ("اختبار مفاتيح API", test_api_keys),
        ("اختبار الاتصال بالشبكة", test_network_connectivity),
        ("اختبار الاتصال الأساسي", test_basic_connection),
        ("اختبار استعلام قاعدة البيانات", test_database_query),
        ("اختبار عملية النشر", test_publish_operation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        print("-" * 40)
        
        start_time = time.time()
        try:
            result = test_func()
            end_time = time.time()
            duration = end_time - start_time
            
            results.append((test_name, result, duration))
            
            if result:
                print(f"✅ {test_name} نجح في {duration:.2f} ثانية")
            else:
                print(f"❌ {test_name} فشل في {duration:.2f} ثانية")
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            results.append((test_name, False, duration))
            print(f"💥 {test_name} تعطل: {e}")
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج اختبارات إصلاحات Supabase:")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result, duration in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} | {test_name} ({duration:.2f}s)")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed} نجح، {failed} فشل")
    
    if failed == 0:
        print("🎉 جميع الاختبارات نجحت! إصلاحات SSL timeout تعمل بشكل صحيح.")
    elif passed > failed:
        print("⚠️ معظم الاختبارات نجحت، لكن هناك بعض المشاكل المتبقية.")
    else:
        print("🚨 معظم الاختبارات فشلت، يحتاج النظام لإصلاحات إضافية.")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = run_all_tests()
        
        print("\n" + "=" * 60)
        print("💡 نصائح لحل مشاكل SSL timeout:")
        print("=" * 60)
        print("1. تأكد من استقرار اتصال الإنترنت")
        print("2. تحقق من إعدادات جدار الحماية")
        print("3. جرب استخدام VPN إذا كان هناك حجب للخدمة")
        print("4. تأكد من صحة مفاتيح Supabase في api_keys.json")
        print("5. أعد تشغيل التطبيق إذا استمرت المشاكل")
        
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبارات بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ عام في الاختبارات: {e}")
        sys.exit(1)
