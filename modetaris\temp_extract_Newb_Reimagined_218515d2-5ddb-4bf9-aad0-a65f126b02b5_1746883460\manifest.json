{"format_version": 2, "header": {"name": "Newb Reimagined", "description": "§cOnly works with Minecraft Patched & BetterRenderDragon\n§7A shader that have a vanilla minecraft feel but improved\nr15.65 Merged\n§3https://github.com/alyow/newb-reimagined/§r\n", "uuid": "1acad283-5790-480f-bf83-099b34185b47", "version": [0, 15, 65], "min_engine_version": [1, 21, 20]}, "modules": [{"type": "resources", "uuid": "8ac5b886-c545-4de2-baf4-9d0b6e510c77", "version": [0, 15, 65]}], "settings": [{"type": "label", "text": "§7  Restart your minecraft after changing these settings!§r\n\n  Recommended:\n  §7Set brightness to 10 - 30\n  Don't use texture packs to avoid some bugs§r"}], "subpacks": [{"folder_name": "comp_vanilla", "name": "§gComplementary§r\n\n Vanilla Clouds\n No Aurora\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "comp_soft", "name": "§gComplementary§r\n\n Soft Clouds\n Aurora §bLight Blue §uPurple§r\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "comp_dc", "name": "§eComplementary Reimagined§r\n\n Double Cloud Layers§r\n Aurora §bLight Blue §uPurple§r\n Reimagined Water\n\n", "memory_tier": 1}, {"folder_name": "comp_gr", "name": "§eComplementary Reimagined§r\n\n Rounded Clouds, Aurora §bLight Blue §uPurple§r\n Ground Reflection, Rainbow\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "comp_aurora", "name": "§eComplementary Reimagined§r\n\n Rounded Clouds, Aurora §bLight Blue §uPurple§r\n Blinking Torch\n Reimagined Water\n\n", "memory_tier": 1}, {"folder_name": "comp_def", "name": "§eComplementary Reimagined§r\n\n Rounded Clouds\n No Aurora\n Reimagined Water\n\n", "memory_tier": 1}, {"folder_name": "def_vanilla", "name": "§bNewb Reimagined§r\n\n Vanilla Clouds\n No Aurora\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "def_soft", "name": "§bNewb Reimagined§r\n\n Soft Clouds\n Aurora §bLight Blue §aGreen§r\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "def_dc", "name": "§bNewb Reimagined§r\n\n Double Cloud Layers\n Aurora §bLight Blue §aGreen§r\n Reimagined Water\n\n", "memory_tier": 1}, {"folder_name": "def_gr", "name": "§bNewb Reimagined§r\n\n Rounded Clouds, Aurora §bLight Blue §aGreen§r\n Ground Reflection, Rainbow\n Wavy Water\n\n", "memory_tier": 1}, {"folder_name": "def_aurora", "name": "§bNewb Reimagined§r\n\n Rounded Clouds, Aurora §bLight Blue §aGreen§r\n Blinking Torch\n Reimagined Water\n\n", "memory_tier": 1}, {"folder_name": "default", "name": "§bNewb Reimagined Default§r\n\n Rounded Clouds\n No Aurora\n Reimagined Water\n\n", "memory_tier": 1}], "metadata": {"authors": ["al_yoww", "deven<PERSON>n"], "url": "https://github.com/alyow/newb-reimagined/"}}