/* Advanced Admin Features Styles */
/* تصميم ميزات الأدمن المتقدمة */

/* ========================================
   Users Management Styles
   ======================================== */

.users-dashboard {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #ffcc00;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 204, 0, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #000;
}

.stat-info h3 {
    font-size: 2rem;
    color: #ffcc00;
    margin: 0;
    font-family: 'VT323', monospace;
}

.stat-info p {
    color: #ffffff;
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.management-section {
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.section-header h3 {
    color: #ffcc00;
    font-size: 1.5rem;
    margin: 0;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tool-btn {
    background: linear-gradient(135deg, #3a3a4e, #2a2a3e);
    border: 2px solid #666;
    border-radius: 10px;
    padding: 15px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
}

.tool-btn:hover {
    border-color: #ffcc00;
    background: linear-gradient(135deg, #4a4a5e, #3a3a4e);
    transform: translateY(-2px);
}

.tool-btn i {
    font-size: 1.2rem;
    color: #ffcc00;
}

.activity-section {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #333;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: rgba(255, 204, 0, 0.1);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
}

.activity-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.activity-user {
    font-weight: bold;
    color: #ffcc00;
}

.activity-action {
    color: #ffffff;
    opacity: 0.9;
}

.activity-time {
    color: #888;
    font-size: 0.9rem;
}

/* ========================================
   Analytics Styles
   ======================================== */

.analytics-dashboard {
    padding: 20px;
}

.analytics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #ffcc00;
    border-radius: 15px;
    padding: 20px;
}

.overview-card h3 {
    color: #ffcc00;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container {
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #888;
}

.chart-stats {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.chart-stats span {
    color: #ffffff;
    font-size: 0.9rem;
}

.chart-stats strong {
    color: #ffcc00;
}

.reports-section {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.report-tools {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.report-options {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.report-options label {
    color: #ffcc00;
    font-weight: bold;
}

.report-options select {
    background: #1e1e2e;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
    min-width: 150px;
}

.report-options select:focus {
    border-color: #ffcc00;
    outline: none;
}

/* ========================================
   Content Management Styles
   ======================================== */

.content-dashboard {
    padding: 20px;
}

.content-stats {
    display: flex;
    justify-content: space-around;
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
}

.stat-item i {
    color: #ffcc00;
    font-size: 1.2rem;
}

.stat-item strong {
    color: #ffcc00;
}

.content-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.tool-section {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.tool-section h3 {
    color: #ffcc00;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-area {
    border: 2px dashed #666;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #ffcc00;
    background: rgba(255, 204, 0, 0.1);
}

.upload-area i {
    font-size: 3rem;
    color: #ffcc00;
    margin-bottom: 10px;
}

.upload-area p {
    color: #ffffff;
    margin: 0;
}

.upload-area input[type="file"] {
    display: none;
}

/* ========================================
   Notifications Styles
   ======================================== */

.notifications-dashboard {
    padding: 20px;
}

.notification-composer {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.notification-composer h3 {
    color: #ffcc00;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.composer-form {
    display: grid;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    color: #ffcc00;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    background: #1e1e2e;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 10px;
    color: #ffffff;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #ffcc00;
    outline: none;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.notification-history {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.notification-history h3 {
    color: #ffcc00;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #ffcc00;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.notification-header h4 {
    color: #ffcc00;
    margin: 0;
}

.notification-time {
    color: #888;
    font-size: 0.9rem;
}

.notification-item p {
    color: #ffffff;
    margin: 10px 0;
    opacity: 0.9;
}

.notification-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
}

.notification-type,
.notification-recipients {
    background: rgba(255, 204, 0, 0.2);
    color: #ffcc00;
    padding: 4px 8px;
    border-radius: 4px;
}

/* ========================================
   Maintenance Styles
   ======================================== */

.maintenance-dashboard {
    padding: 20px;
}

.system-health {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid #444;
}

.system-health h3 {
    color: #ffcc00;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.health-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.health-label {
    color: #ffffff;
}

.health-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
}

.health-status.healthy {
    background: #22c55e;
    color: white;
}

.health-status.unhealthy {
    background: #ef4444;
    color: white;
}

.maintenance-tools {
    margin-bottom: 30px;
}

.maintenance-tools h3 {
    color: #ffcc00;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tool-card {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #444;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #ffcc00;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 204, 0, 0.2);
}

.tool-card h4 {
    color: #ffcc00;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.tool-card p {
    color: #ffffff;
    opacity: 0.9;
    margin-bottom: 15px;
}

.tool-card .tool-btn {
    width: 100%;
    justify-content: center;
}

.performance-monitor {
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #444;
}

.performance-monitor h3 {
    color: #ffcc00;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.metric-label {
    color: #ffffff;
    min-width: 120px;
}

.metric-bar {
    flex: 1;
    height: 20px;
    background: #1e1e2e;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #444;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    transition: width 0.5s ease;
}

.metric-value {
    color: #ffcc00;
    font-weight: bold;
    min-width: 50px;
    text-align: right;
}

/* ========================================
   Modal Styles
   ======================================== */

.advanced-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: relative;
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border: 2px solid #ffcc00;
    border-radius: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    color: #000;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: bold;
}

.modal-header .close-btn {
    background: transparent;
    border: none;
    color: #000;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-header .close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

/* ========================================
   Table Styles
   ======================================== */

.users-table-container {
    width: 100%;
    min-width: 800px;
}

.table-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.table-controls input,
.table-controls select {
    background: #1e1e2e;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 8px 12px;
    color: #ffffff;
}

.table-controls input:focus,
.table-controls select:focus {
    border-color: #ffcc00;
    outline: none;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    background: linear-gradient(135deg, #2a2a3e, #1e1e2e);
    border-radius: 10px;
    overflow: hidden;
}

.users-table th,
.users-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #444;
}

.users-table th {
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    color: #000;
    font-weight: bold;
}

.users-table td {
    color: #ffffff;
}

.users-table tr:hover {
    background: rgba(255, 204, 0, 0.1);
}

.action-btn {
    background: transparent;
    border: 2px solid #666;
    border-radius: 6px;
    padding: 6px 10px;
    color: #ffffff;
    cursor: pointer;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    border-color: #ffcc00;
    color: #ffcc00;
}

.action-btn.danger:hover {
    border-color: #ef4444;
    color: #ef4444;
}

/* ========================================
   Button Styles
   ======================================== */

.primary-btn {
    background: linear-gradient(45deg, #ffcc00, #ff9800);
    color: #000;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.primary-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ffb300);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 204, 0, 0.4);
}

.secondary-btn {
    background: transparent;
    color: #ffcc00;
    border: 2px solid #ffcc00;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.secondary-btn:hover {
    background: #ffcc00;
    color: #000;
    transform: translateY(-2px);
}

.refresh-btn {
    background: transparent;
    border: 2px solid #666;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    border-color: #ffcc00;
    color: #ffcc00;
    transform: rotate(180deg);
}

/* ========================================
   Responsive Design
   ======================================== */

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }

    .analytics-overview {
        grid-template-columns: 1fr;
    }

    .content-tools {
        grid-template-columns: 1fr;
    }

    .report-tools {
        flex-direction: column;
        align-items: stretch;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .users-table-container {
        min-width: auto;
        overflow-x: auto;
    }

    .modal-container {
        max-width: 95vw;
        margin: 10px;
    }

    .health-indicators {
        grid-template-columns: 1fr;
    }

    .content-stats {
        flex-direction: column;
        gap: 15px;
    }
}

/* ========================================
   Animation Classes
   ======================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.6s ease;
}

.pulse {
    animation: pulse 2s infinite;
}

/* ========================================
   Loading Spinner
   ======================================== */

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #444;
    border-top: 4px solid #ffcc00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
