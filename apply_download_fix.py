#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إصلاح روابط التحميل على الأداة الرئيسية
Apply download links fix to the main tool
"""

import os
import sys
import shutil
from datetime import datetime

def backup_original_files():
    """إنشاء نسخة احتياطية من الملفات الأصلية"""
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'firebase_config.py',
        'mod_processor_broken_final.py'
    ]
    
    print(f"📁 إنشاء نسخة احتياطية في: {backup_dir}")
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(backup_dir, file))
            print(f"✅ تم نسخ: {file}")
    
    return backup_dir

def test_enhanced_firebase_config():
    """اختبار تحسينات firebase_config.py"""
    try:
        from firebase_config import firebase_manager
        
        # اختبار الدوال الجديدة
        if hasattr(firebase_manager, 'create_enhanced_download_url'):
            print("✅ دالة create_enhanced_download_url متوفرة")
        else:
            print("❌ دالة create_enhanced_download_url غير متوفرة")
            
        if hasattr(firebase_manager, 'test_download_url'):
            print("✅ دالة test_download_url متوفرة")
        else:
            print("❌ دالة test_download_url غير متوفرة")
            
        if hasattr(firebase_manager, 'create_multiple_download_formats'):
            print("✅ دالة create_multiple_download_formats متوفرة")
        else:
            print("❌ دالة create_multiple_download_formats غير متوفرة")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار firebase_config: {e}")
        return False

def test_download_enhancer():
    """اختبار محسن الروابط"""
    try:
        from download_links_enhancer import enhance_url_for_app, test_url_for_app
        
        # اختبار سريع
        test_url = "https://firebasestorage.googleapis.com/v0/b/test/o/file.mcpack?alt=media"
        enhanced = enhance_url_for_app(test_url, "test.mcpack")
        
        print(f"✅ محسن الروابط يعمل بشكل صحيح")
        print(f"   الرابط الأصلي: {test_url}")
        print(f"   الرابط المحسن: {enhanced}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محسن الروابط: {e}")
        return False

def verify_main_tool_integration():
    """التحقق من دمج التحسينات في الأداة الرئيسية"""
    try:
        # قراءة الملف الرئيسي
        with open('mod_processor_broken_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص وجود الاستيرادات الجديدة
        checks = {
            'download_links_enhancer import': 'from download_links_enhancer import' in content,
            'DOWNLOAD_ENHANCER_AVAILABLE': 'DOWNLOAD_ENHANCER_AVAILABLE' in content,
            'enhance_url_for_app usage': 'enhance_url_for_app' in content,
            'test_url_for_app usage': 'test_url_for_app' in content
        }
        
        print("🔍 فحص دمج التحسينات في الأداة الرئيسية:")
        all_good = True
        
        for check, result in checks.items():
            status = "✅" if result else "❌"
            print(f"   {status} {check}: {result}")
            if not result:
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأداة الرئيسية: {e}")
        return False

def create_usage_instructions():
    """إنشاء تعليمات الاستخدام"""
    instructions = """
# تعليمات استخدام إصلاح روابط التحميل

## المشكلة التي تم حلها:
- روابط Firebase التي تحتوي على `?alt=media` لا تعمل مع Android DownloadManager
- الروابط المُرمزة (`%2F`) تسبب مشاكل في التطبيقات
- عدم وجود امتداد ملف واضح في نهاية الرابط

## الحل المطبق:
1. **تحسين firebase_config.py**: إضافة دوال لإنشاء روابط محسنة
2. **إنشاء download_links_enhancer.py**: نظام ذكي لتحسين الروابط
3. **تحديث الأداة الرئيسية**: دمج النظام الجديد في عملية الرفع

## أفضل تنسيق للروابط (حسب الاختبار):
```
https://storage.googleapis.com/bucket-name/path/file.mcpack?filename=original_name.mcpack
```

## نسبة التوافق مع Android: 80%

## كيفية الاستخدام:
1. شغل الأداة كالمعتاد
2. عند رفع مود، سيتم تحسين الرابط تلقائياً
3. ستظهر رسائل تأكيد تحسين الرابط في السجل
4. الرابط المحسن سيُملأ تلقائياً في حقل التحميل

## اختبار الروابط:
```bash
python test_download_links_fix.py
```

## ملاحظات مهمة:
- النظام يحتفظ بالرابط الأصلي كبديل
- يتم اختبار الروابط تلقائياً قبل الاستخدام
- التحسينات متوافقة مع المتصفحات والتطبيقات
"""
    
    with open('DOWNLOAD_FIX_INSTRUCTIONS.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("📝 تم إنشاء ملف التعليمات: DOWNLOAD_FIX_INSTRUCTIONS.md")

def main():
    """الدالة الرئيسية لتطبيق الإصلاح"""
    print("🔧 تطبيق إصلاح روابط التحميل لتطبيق Modetaris")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    backup_dir = backup_original_files()
    
    # اختبار التحسينات
    print(f"\n🧪 اختبار التحسينات:")
    
    firebase_ok = test_enhanced_firebase_config()
    enhancer_ok = test_download_enhancer()
    integration_ok = verify_main_tool_integration()
    
    # النتائج
    print(f"\n📊 نتائج الاختبار:")
    print(f"   Firebase Config: {'✅' if firebase_ok else '❌'}")
    print(f"   Download Enhancer: {'✅' if enhancer_ok else '❌'}")
    print(f"   Main Tool Integration: {'✅' if integration_ok else '❌'}")
    
    if firebase_ok and enhancer_ok and integration_ok:
        print(f"\n🎉 تم تطبيق الإصلاح بنجاح!")
        print(f"✅ جميع التحسينات تعمل بشكل صحيح")
        print(f"📁 النسخة الاحتياطية في: {backup_dir}")
        
        # إنشاء تعليمات الاستخدام
        create_usage_instructions()
        
        print(f"\n💡 الخطوات التالية:")
        print(f"   1. شغل الأداة الرئيسية: python mod_processor_broken_final.py")
        print(f"   2. ارفع مود واختبر الرابط الجديد")
        print(f"   3. تأكد من عمل التحميل في تطبيق Modetaris")
        
    else:
        print(f"\n❌ فشل في تطبيق بعض التحسينات")
        print(f"⚠️ راجع الأخطاء أعلاه وأعد المحاولة")
        print(f"📁 يمكنك استعادة الملفات الأصلية من: {backup_dir}")

if __name__ == "__main__":
    main()
