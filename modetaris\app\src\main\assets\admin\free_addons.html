<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة Free Addons - Mod Etaris Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #94a3b8;
            font-size: 1.1rem;
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }

        .search-box input::placeholder {
            color: #94a3b8;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(74, 222, 128, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section h2 {
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }

        .mod-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .mod-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .mod-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .mod-item.selected {
            background: rgba(74, 222, 128, 0.2);
            border: 1px solid #4ade80;
        }

        .mod-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            margin-left: 15px;
        }

        .mod-info {
            flex: 1;
        }

        .mod-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: white;
        }

        .mod-stats {
            font-size: 0.9rem;
            color: #94a3b8;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #94a3b8;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #4ade80;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-bar {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateY(100px);
            transition: transform 0.3s ease;
        }

        .status-bar.show {
            transform: translateY(0);
        }

        .status-bar.success {
            background: linear-gradient(45deg, #22c55e, #16a34a);
        }

        .status-bar.error {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }

        @media (max-width: 768px) {
            .sections {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 إدارة Free Addons</h1>
            <p>قم بتحديد المودات التي تريد عرضها في قسم "Free Addons" المميز</p>
        </div>

        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="🔍 ابحث عن المودات...">
            </div>
            <button class="btn btn-primary" onclick="saveChanges()">💾 حفظ التغييرات</button>
            <button class="btn btn-secondary" onclick="loadData()">🔄 تحديث البيانات</button>
        </div>

        <div class="sections">
            <div class="section">
                <h2>📦 جميع مودات Addons</h2>
                <div id="allModsList" class="mod-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل المودات...
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>⭐ Free Addons المحددة</h2>
                <div id="selectedModsList" class="mod-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل المودات المحددة...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="statusBar" class="status-bar">
        <span id="statusMessage"></span>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="free_addons.js"></script>
</body>
</html>
