#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة البدء السريع لنظام النسخ الاحتياطي
Quick Start Tool for Backup System

هذه الأداة تساعد في:
- تثبيت المتطلبات
- إعداد النظام
- اختبار الاتصالات
- بدء النسخ الاحتياطي الأول
"""

import os
import sys
import json
import subprocess
import asyncio
import logging
from pathlib import Path

# تكوين نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QuickStartManager:
    """مدير البدء السريع"""
    
    def __init__(self):
        self.backup_dir = Path(__file__).parent
        self.project_root = self.backup_dir.parent
        self.config_file = self.backup_dir / "backup_config.json"
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("=" * 80)
        print("🚀 نظام النسخ الاحتياطي الاحترافي - أداة البدء السريع")
        print("   Professional Backup System - Quick Start Tool")
        print("=" * 80)
        print()
    
    def check_python_version(self):
        """التحقق من إصدار Python"""
        logger.info("🔍 التحقق من إصدار Python...")
        
        if sys.version_info < (3, 8):
            logger.error("❌ يتطلب Python 3.8 أو أحدث")
            logger.error(f"الإصدار الحالي: {sys.version}")
            return False
        
        logger.info(f"✅ إصدار Python مناسب: {sys.version}")
        return True
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        logger.info("📦 تثبيت المتطلبات...")
        
        requirements_file = self.backup_dir / "requirements.txt"
        if not requirements_file.exists():
            logger.error("❌ ملف requirements.txt غير موجود")
            return False
        
        try:
            # تحديث pip أولاً
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # تثبيت المتطلبات
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, capture_output=True, text=True)
            
            logger.info("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ فشل في تثبيت المتطلبات: {e}")
            logger.error(f"خطأ: {e.stderr}")
            return False
    
    def check_firebase_credentials(self):
        """التحقق من بيانات اعتماد Firebase"""
        logger.info("🔑 التحقق من بيانات اعتماد Firebase...")
        
        # قراءة الإعدادات
        if not self.config_file.exists():
            logger.warning("⚠️ ملف الإعدادات غير موجود، سيتم إنشاؤه")
            return True
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        firebase_creds_path = config.get("firebase", {}).get("credentials_path", "")
        
        if not firebase_creds_path:
            logger.warning("⚠️ مسار بيانات اعتماد Firebase غير محدد")
            return True
        
        # التحقق من وجود الملف
        full_path = self.project_root / firebase_creds_path
        if not full_path.exists():
            logger.error(f"❌ ملف بيانات اعتماد Firebase غير موجود: {full_path}")
            logger.info("💡 تأكد من وضع ملف Firebase Admin SDK في المكان الصحيح")
            return False
        
        logger.info("✅ ملف بيانات اعتماد Firebase موجود")
        return True
    
    def setup_config(self):
        """إعداد ملف التكوين"""
        logger.info("⚙️ إعداد ملف التكوين...")
        
        if self.config_file.exists():
            logger.info("✅ ملف الإعدادات موجود بالفعل")
            return True
        
        # إنشاء إعدادات افتراضية
        try:
            from database_backup_manager import DatabaseBackupManager
            manager = DatabaseBackupManager()
            logger.info("✅ تم إنشاء ملف الإعدادات الافتراضي")
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
            return False
    
    async def test_connections(self):
        """اختبار الاتصالات"""
        logger.info("🔗 اختبار الاتصالات...")
        
        try:
            from database_backup_manager import DatabaseBackupManager
            manager = DatabaseBackupManager()
            
            # اختبار Firebase
            if manager.storage_bucket:
                logger.info("✅ الاتصال بـ Firebase Storage نجح")
            else:
                logger.warning("⚠️ لم يتم تكوين Firebase Storage")
            
            # اختبار Supabase
            success_count = 0
            for db_name, client in manager.supabase_clients.items():
                try:
                    is_connected = await manager.test_database_connection(db_name)
                    if is_connected:
                        logger.info(f"✅ الاتصال بقاعدة البيانات {db_name} نجح")
                        success_count += 1
                    else:
                        logger.warning(f"⚠️ فشل الاتصال بقاعدة البيانات {db_name}")
                except Exception as e:
                    logger.warning(f"⚠️ خطأ في اختبار {db_name}: {e}")
            
            if success_count > 0:
                logger.info(f"✅ تم الاتصال بـ {success_count} قاعدة بيانات بنجاح")
                return True
            else:
                logger.error("❌ فشل في الاتصال بجميع قواعد البيانات")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الاتصالات: {e}")
            return False
    
    async def create_first_backup(self):
        """إنشاء أول نسخة احتياطية"""
        logger.info("💾 إنشاء أول نسخة احتياطية...")
        
        try:
            from database_backup_manager import DatabaseBackupManager
            manager = DatabaseBackupManager()
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الرئيسية
            backup_result = await manager.create_full_backup("main")
            
            logger.info(f"✅ تم إنشاء النسخة الاحتياطية: {backup_result['backup_id']}")
            logger.info(f"📊 عدد السجلات: {backup_result['metadata']['total_records']}")
            logger.info(f"⏱️ المدة: {backup_result['metadata']['backup_duration']:.2f} ثانية")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def setup_admin_panel_link(self):
        """إعداد رابط لوحة الإدارة"""
        logger.info("🔗 إعداد رابط لوحة إدارة النسخ الاحتياطية...")
        
        try:
            # إضافة رابط في لوحة الإدارة الرئيسية
            admin_index = self.project_root / "app/src/main/assets/admin/index.html"
            
            if admin_index.exists():
                with open(admin_index, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن مكان إضافة الرابط
                backup_link = '''
                <div class="admin-card">
                    <div class="card-icon">🗄️</div>
                    <h3>إدارة النسخ الاحتياطية</h3>
                    <p>نظام احترافي للنسخ الاحتياطي والمراقبة</p>
                    <a href="backup-management.html" class="card-link">
                        <i class="fas fa-database"></i> فتح لوحة النسخ الاحتياطية
                    </a>
                </div>
                '''
                
                # إضافة الرابط إذا لم يكن موجوداً
                if "backup-management.html" not in content:
                    # البحث عن مكان مناسب للإدراج
                    if '<div class="admin-grid">' in content:
                        content = content.replace(
                            '<div class="admin-grid">',
                            f'<div class="admin-grid">{backup_link}'
                        )
                        
                        with open(admin_index, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        logger.info("✅ تم إضافة رابط لوحة النسخ الاحتياطية")
                    else:
                        logger.warning("⚠️ لم يتم العثور على مكان مناسب لإضافة الرابط")
                else:
                    logger.info("✅ رابط لوحة النسخ الاحتياطية موجود بالفعل")
            else:
                logger.warning("⚠️ ملف لوحة الإدارة الرئيسية غير موجود")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إعداد رابط لوحة الإدارة: {e}")
    
    def create_startup_scripts(self):
        """إنشاء ملفات تشغيل سريعة"""
        logger.info("📝 إنشاء ملفات التشغيل السريع...")
        
        # ملف تشغيل Windows
        windows_script = self.backup_dir / "start_backup_system.bat"
        with open(windows_script, 'w', encoding='utf-8') as f:
            f.write(f"""@echo off
echo 🚀 بدء نظام النسخ الاحتياطي الاحترافي
echo Starting Professional Backup System
echo.

cd /d "{self.backup_dir}"

echo 📦 تثبيت المتطلبات...
python -m pip install -r requirements.txt

echo 🔄 بدء النسخ الاحتياطي التلقائي...
python database_backup_manager.py auto

pause
""")
        
        # ملف تشغيل Linux/Mac
        unix_script = self.backup_dir / "start_backup_system.sh"
        with open(unix_script, 'w', encoding='utf-8') as f:
            f.write(f"""#!/bin/bash
echo "🚀 بدء نظام النسخ الاحتياطي الاحترافي"
echo "Starting Professional Backup System"
echo

cd "{self.backup_dir}"

echo "📦 تثبيت المتطلبات..."
python3 -m pip install -r requirements.txt

echo "🔄 بدء النسخ الاحتياطي التلقائي..."
python3 database_backup_manager.py auto
""")
        
        # جعل الملف قابل للتنفيذ على Unix
        try:
            os.chmod(unix_script, 0o755)
        except:
            pass
        
        logger.info("✅ تم إنشاء ملفات التشغيل السريع")
    
    def print_next_steps(self):
        """طباعة الخطوات التالية"""
        print()
        print("=" * 80)
        print("🎉 تم إعداد نظام النسخ الاحتياطي بنجاح!")
        print("=" * 80)
        print()
        print("📋 الخطوات التالية:")
        print()
        print("1️⃣ إعداد قواعد البيانات الاحتياطية:")
        print("   • افتح ملف backup_config.json")
        print("   • أضف روابط ومفاتيح قواعد البيانات الاحتياطية")
        print("   • مثال:")
        print('     "backup1": {')
        print('       "url": "https://your-backup-url.supabase.co",')
        print('       "key": "your-backup-key"')
        print('     }')
        print()
        print("2️⃣ بدء النظام:")
        print("   • Windows: انقر مرتين على start_backup_system.bat")
        print("   • Linux/Mac: ./start_backup_system.sh")
        print("   • يدوياً: python database_backup_manager.py auto")
        print()
        print("3️⃣ فتح لوحة الإدارة:")
        print("   • افتح app/src/main/assets/admin/backup-management.html")
        print("   • أو من لوحة الإدارة الرئيسية")
        print()
        print("4️⃣ بدء المراقبة:")
        print("   • python database_monitor.py start")
        print()
        print("🔧 أوامر مفيدة:")
        print("   • نسخة احتياطية سريعة:")
        print("     python database_backup_manager.py backup")
        print("   • نقل البيانات مع التفعيل:")
        print("     python database_backup_manager.py transfer main backup1 --activate")
        print("   • تبديل قاعدة البيانات:")
        print("     python database_backup_manager.py switch backup1")
        print("   • مقارنة قواعد البيانات:")
        print("     python database_backup_manager.py compare main backup1")
        print("   • حالة النظام:")
        print("     python database_backup_manager.py status")
        print()
        print("🚨 ميزات الحماية:")
        print("   ✅ نسخ احتياطي تلقائي كل 6 ساعات")
        print("   ✅ مراقبة مستمرة لحالة قواعد البيانات")
        print("   ✅ تبديل تلقائي عند الأعطال")
        print("   ✅ نقل البيانات مع التحقق من السلامة")
        print("   ✅ تفعيل قاعدة البيانات الجديدة تلقائياً")
        print()
        print("📖 للمزيد من التفاصيل:")
        print("   • اقرأ ملف README.md")
        print("   • راجع ملف backup_config.json")
        print()
        print("📧 للدعم: <EMAIL>")
        print("=" * 80)
    
    async def run_setup(self):
        """تشغيل عملية الإعداد الكاملة"""
        self.print_header()
        
        steps = [
            ("التحقق من إصدار Python", self.check_python_version),
            ("تثبيت المتطلبات", self.install_requirements),
            ("إعداد ملف التكوين", self.setup_config),
            ("التحقق من بيانات اعتماد Firebase", self.check_firebase_credentials),
            ("اختبار الاتصالات", self.test_connections),
            ("إنشاء أول نسخة احتياطية", self.create_first_backup),
            ("إعداد رابط لوحة الإدارة", self.setup_admin_panel_link),
            ("إنشاء ملفات التشغيل السريع", self.create_startup_scripts)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            print(f"🔄 {step_name}...")
            try:
                if asyncio.iscoroutinefunction(step_func):
                    result = await step_func()
                else:
                    result = step_func()
                
                if result:
                    success_count += 1
                    print(f"✅ {step_name} - مكتمل")
                else:
                    print(f"⚠️ {step_name} - تحذير")
                    
            except Exception as e:
                print(f"❌ {step_name} - خطأ: {e}")
            
            print()
        
        print(f"📊 تم إكمال {success_count}/{len(steps)} خطوة بنجاح")
        
        if success_count >= len(steps) - 2:  # السماح بخطأين
            self.print_next_steps()
        else:
            print("❌ فشل في إعداد النظام. يرجى مراجعة الأخطاء أعلاه.")


async def main():
    """الدالة الرئيسية"""
    manager = QuickStartManager()
    await manager.run_setup()


if __name__ == "__main__":
    asyncio.run(main())
