# -*- coding: utf-8 -*-
"""
إعدادات محسنة لـ Supabase مع معالجة أخطاء SSL timeout
"""

import os
import json
import time
import socket
from typing import Optional, Dict, Any
from supabase import create_client, Client

class SupabaseConfig:
    """فئة لإدارة إعدادات Supabase المحسنة"""
    
    def __init__(self):
        self.url = "https://ytqxxodyecdeosnqoure.supabase.co"
        self.key = ""
        self.client = None
        self.is_connected = False
        self.connection_timeout = 30
        self.retry_attempts = 3
        self.retry_delay = 2
        
        # تحميل المفاتيح من ملف الإعدادات
        self.load_api_keys()
    
    def load_api_keys(self):
        """تحميل مفاتيح API من ملف الإعدادات"""
        try:
            config_file = "api_keys.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.key = config.get("SUPABASE_KEY", "")
                    print("✅ تم تحميل مفتاح Supabase من ملف الإعدادات")
            else:
                print("⚠️ ملف api_keys.json غير موجود")
        except Exception as e:
            print(f"❌ خطأ في تحميل مفاتيح API: {e}")
    
    def check_network_connectivity(self) -> bool:
        """فحص الاتصال بالإنترنت"""
        try:
            # محاولة الاتصال بـ Google DNS
            socket.create_connection(("8.8.8.8", 53), timeout=5)
            return True
        except OSError:
            try:
                # محاولة بديلة مع Cloudflare DNS
                socket.create_connection(("1.1.1.1", 53), timeout=5)
                return True
            except OSError:
                return False
    
    def create_client_with_timeout(self) -> Optional[Client]:
        """إنشاء عميل Supabase مع إعدادات timeout محسنة"""
        try:
            if not self.url or not self.key:
                print("❌ معلومات Supabase غير مكتملة")
                return None

            # إعدادات بسيطة ومتوافقة
            client_options = {
                'schema': 'public',
                'auto_refresh_token': False,  # تعطيل لتجنب مشاكل SSL
                'persist_session': False,    # تعطيل لتجنب مشاكل SSL
                'detect_session_in_url': False
            }

            client = create_client(self.url, self.key, options=client_options)
            return client

        except Exception as e:
            print(f"❌ خطأ في إنشاء عميل Supabase: {e}")
            # fallback إلى الطريقة العادية
            try:
                return create_client(self.url, self.key)
            except Exception as fallback_e:
                print(f"❌ فشل في الطريقة البديلة أيضاً: {fallback_e}")
                return None
    
    def test_connection(self, client: Client, table_name: str = "mods") -> bool:
        """اختبار الاتصال مع timeout (متوافق مع Windows)"""
        try:
            import threading

            def test_query():
                try:
                    # اختبار بسيط للاتصال
                    result = client.table(table_name).select("id").limit(1).execute()
                    return True
                except Exception as e:
                    print(f"❌ فشل اختبار الاتصال: {e}")
                    return False

            # تشغيل الاختبار في خيط منفصل مع timeout
            result = [False]

            def run_test():
                result[0] = test_query()

            test_thread = threading.Thread(target=run_test)
            test_thread.daemon = True
            test_thread.start()
            test_thread.join(timeout=15)  # 15 ثانية للاختبار

            if test_thread.is_alive():
                print("⏰ انتهت مهلة اختبار الاتصال")
                return False
            else:
                return result[0]

        except Exception as e:
            print(f"❌ خطأ في اختبار الاتصال: {e}")
            return False
    
    def connect(self) -> bool:
        """الاتصال بـ Supabase مع إعادة المحاولة"""
        if not self.check_network_connectivity():
            print("📡 لا يوجد اتصال بالإنترنت")
            return False
        
        for attempt in range(self.retry_attempts):
            try:
                print(f"🔄 محاولة الاتصال بـ Supabase (المحاولة {attempt + 1}/{self.retry_attempts})...")
                
                # إنشاء العميل
                self.client = self.create_client_with_timeout()
                if not self.client:
                    continue
                
                # اختبار الاتصال
                if self.test_connection(self.client):
                    self.is_connected = True
                    print("✅ تم الاتصال بـ Supabase بنجاح")
                    return True
                else:
                    print(f"❌ فشل اختبار الاتصال في المحاولة {attempt + 1}")
                    
            except Exception as e:
                print(f"❌ خطأ في المحاولة {attempt + 1}: {e}")
            
            # انتظار قبل إعادة المحاولة
            if attempt < self.retry_attempts - 1:
                delay = self.retry_delay * (attempt + 1)
                print(f"⏳ انتظار {delay} ثانية قبل إعادة المحاولة...")
                time.sleep(delay)
        
        print("❌ فشل في جميع محاولات الاتصال")
        self.is_connected = False
        return False
    
    def reconnect(self) -> bool:
        """إعادة الاتصال"""
        self.is_connected = False
        self.client = None
        return self.connect()
    
    def get_client(self) -> Optional[Client]:
        """الحصول على العميل مع التحقق من الاتصال"""
        if not self.is_connected or not self.client:
            if not self.connect():
                return None
        return self.client
    
    def execute_with_retry(self, operation_func, operation_name: str = "عملية قاعدة البيانات"):
        """تنفيذ عملية مع إعادة المحاولة عند فشل الاتصال"""
        for attempt in range(self.retry_attempts):
            try:
                client = self.get_client()
                if not client:
                    raise Exception("لا يمكن الحصول على عميل Supabase")
                
                # تنفيذ العملية
                return operation_func(client)
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # معالجة أخطاء SSL timeout
                if 'ssl' in error_msg and ('timeout' in error_msg or 'handshake' in error_msg):
                    print(f"🔒 خطأ SSL timeout في {operation_name} (المحاولة {attempt + 1})")
                    
                    if attempt < self.retry_attempts - 1:
                        print("🔄 محاولة إعادة الاتصال...")
                        self.reconnect()
                        time.sleep(self.retry_delay * (attempt + 1))
                        continue
                
                # معالجة أخطاء الشبكة الأخرى
                elif any(keyword in error_msg for keyword in ['connection', 'network', 'timeout']):
                    print(f"🌐 خطأ شبكة في {operation_name} (المحاولة {attempt + 1})")
                    
                    if attempt < self.retry_attempts - 1:
                        time.sleep(self.retry_delay * (attempt + 1))
                        continue
                
                # خطأ غير قابل للإصلاح
                print(f"❌ خطأ في {operation_name}: {e}")
                raise e
        
        raise Exception(f"فشل في {operation_name} بعد {self.retry_attempts} محاولات")

# إنشاء مثيل عام
supabase_config = SupabaseConfig()

def get_supabase_client() -> Optional[Client]:
    """دالة مساعدة للحصول على عميل Supabase"""
    return supabase_config.get_client()

def publish_to_supabase(data: Dict[str, Any], table_name: str = "mods") -> bool:
    """نشر البيانات إلى Supabase مع معالجة الأخطاء"""
    def insert_operation(client):
        response = client.table(table_name).insert(data).execute()
        if hasattr(response, 'data') and response.data:
            return response.data
        else:
            raise Exception(f"استجابة غير متوقعة: {response}")
    
    try:
        result = supabase_config.execute_with_retry(insert_operation, f"نشر البيانات إلى جدول {table_name}")
        print("✅ تم نشر البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في نشر البيانات: {e}")
        return False
