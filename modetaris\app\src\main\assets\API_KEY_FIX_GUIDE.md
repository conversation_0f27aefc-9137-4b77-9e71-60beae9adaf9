# 🔑 دليل إصلاح مفتاح API - عاجل

## ❌ المشكلة الحالية:
```
401 Unauthorized - Invalid API key
Double check your Supabase `anon` or `service_role` API key
```

## 🚨 الحل الفوري المطلوب:

### **1. الحصول على مفتاح API جديد:**

#### **خطوات الحصول على المفتاح:**
1. **اذهب إلى Supabase Dashboard:**
   ```
   https://supabase.com/dashboard/project/ytqxxodyecdeosnqoure
   ```

2. **انتقل إلى Settings > API:**
   - انقر على "Settings" في الشريط الجانبي
   - اختر "API" من القائمة

3. **انسخ المفتاح الصحيح:**
   - انسخ "anon public" key
   - أو "service_role" key إذا كنت تحتاج صلاحيات أكبر

### **2. تحديث المفتاح في الكود:**

#### **في ملف `script.js`:**
```javascript
// ابحث عن هذا السطر وحدث المفتاح
const supabaseKey = 'المفتاح_الجديد_هنا';

// أو
const supabase = createClient(
    'https://ytqxxodyecdeosnqoure.supabase.co',
    'المفتاح_الجديد_هنا'
);
```

#### **في ملف `api-key-emergency-fix.js`:**
```javascript
// حدث المفتاح في POTENTIAL_API_KEYS
const POTENTIAL_API_KEYS = [
    'المفتاح_الجديد_هنا',  // ضع المفتاح الصحيح هنا
    // باقي المفاتيح...
];
```

---

## 🛠️ حل مؤقت - تعطيل RLS:

### **إذا كان المفتاح صحيح لكن المشكلة مستمرة:**

#### **تنفيذ SQL في Supabase:**
```sql
-- تعطيل Row Level Security مؤقتاً للاختبار
ALTER TABLE mods DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_languages DISABLE ROW LEVEL SECURITY;
ALTER TABLE free_addons DISABLE ROW LEVEL SECURITY;

-- إنشاء دالة increment_clicks
CREATE OR REPLACE FUNCTION increment_clicks(mod_id_param UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
    new_clicks INTEGER;
BEGIN
    -- إضافة عمود clicks إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'clicks'
    ) THEN
        ALTER TABLE mods ADD COLUMN clicks INTEGER DEFAULT 0;
    END IF;
    
    -- تحديث عدد النقرات
    UPDATE mods 
    SET clicks = COALESCE(clicks, 0) + 1
    WHERE id = mod_id_param;
    
    -- الحصول على العدد الجديد
    SELECT COALESCE(clicks, 0) INTO new_clicks 
    FROM mods 
    WHERE id = mod_id_param;
    
    result := json_build_object(
        'success', true,
        'mod_id', mod_id_param,
        'new_clicks', new_clicks
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    result := json_build_object(
        'success', false,
        'error', SQLERRM
    );
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## 🔍 اختبار الإصلاح:

### **أوامر الاختبار في وحدة التحكم:**
```javascript
// اختبار الاتصال
apiKeyEmergencyFix.test()

// عرض معلومات المفتاح
apiKeyEmergencyFix.showInfo()

// البحث عن مفتاح صحيح
apiKeyEmergencyFix.findKey()

// تطبيق الإصلاح
apiKeyEmergencyFix.fix()
```

---

## 📋 قائمة التحقق:

### **تأكد من:**
- [ ] **المفتاح صحيح** - منسوخ من Supabase Dashboard
- [ ] **الرابط صحيح** - `https://ytqxxodyecdeosnqoure.supabase.co`
- [ ] **RLS معطل** - للاختبار فقط
- [ ] **الدوال موجودة** - `increment_clicks` منشأة
- [ ] **الأعمدة موجودة** - `clicks`, `is_featured`, إلخ

---

## 🚨 إجراءات طارئة:

### **إذا لم يعمل أي شيء:**

#### **1. الوضع غير المتصل:**
```javascript
// تفعيل الوضع غير المتصل
apiKeyEmergencyFix.offlineMode()
```

#### **2. إنشاء مشروع Supabase جديد:**
1. اذهب إلى https://supabase.com
2. أنشئ مشروع جديد
3. استورد قاعدة البيانات
4. حدث الروابط والمفاتيح

#### **3. استخدام قاعدة بيانات محلية:**
```javascript
// استخدام بيانات محلية مؤقتة
const localData = {
    mods: [...],
    // بيانات أخرى
};
```

---

## 🎯 الهدف النهائي:

**✅ إزالة جميع أخطاء 401 Unauthorized**
**✅ عمل جميع وظائف قاعدة البيانات**
**✅ تطبيق يعمل بسلاسة**

---

## 📞 خطوات سريعة للإصلاح:

### **الآن فوراً:**
1. **افتح Supabase Dashboard**
2. **انسخ مفتاح API جديد**
3. **حدث المفتاح في الكود**
4. **اختبر الاتصال**

### **إذا لم يعمل:**
1. **عطل RLS مؤقتاً**
2. **أنشئ الدوال المطلوبة**
3. **أضف الأعمدة المفقودة**

### **كحل أخير:**
1. **فعل الوضع غير المتصل**
2. **استخدم بيانات محلية**
3. **أنشئ مشروع جديد**

---

## ⚡ ملاحظة مهمة:

**المشكلة الحالية هي مفتاح API غير صحيح أو منتهي الصلاحية.**
**الحل الأسرع هو الحصول على مفتاح جديد من Supabase Dashboard.**

**بمجرد إصلاح المفتاح، ستختفي جميع أخطاء 401!** 🎯✨
