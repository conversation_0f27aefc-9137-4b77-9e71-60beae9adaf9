// تحميل مكتبة Supabase من CDN كبديل محلي
(function() {
    'use strict';

    console.log('🔄 تحميل مكتبة Supabase...');

    // إنشاء عنصر script لتحميل مكتبة Supabase من CDN
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
    script.async = true;

    // معالج نجاح التحميل
    script.onload = function() {
        console.log('✅ تم تحميل مكتبة Supabase بنجاح من CDN');

        // إطلاق حدث مخصص لإعلام باقي الكود أن المكتبة جاهزة
        const event = new CustomEvent('supabaseLoaded');
        document.dispatchEvent(event);
    };

    // معالج فشل التحميل
    script.onerror = function() {
        console.error('❌ فشل في تحميل مكتبة Supabase من CDN');

        // محاولة تحميل من CDN بديل
        const fallbackScript = document.createElement('script');
        fallbackScript.src = 'https://unpkg.com/@supabase/supabase-js@2';
        fallbackScript.async = true;

        fallbackScript.onload = function() {
            console.log('✅ تم تحميل مكتبة Supabase من CDN البديل');
            const event = new CustomEvent('supabaseLoaded');
            document.dispatchEvent(event);
        };

        fallbackScript.onerror = function() {
            console.error('❌ فشل في تحميل مكتبة Supabase من جميع المصادر');

            // إطلاق حدث فشل التحميل
            const errorEvent = new CustomEvent('supabaseLoadError');
            document.dispatchEvent(errorEvent);
        };

        document.head.appendChild(fallbackScript);
    };

    // إضافة السكريبت إلى الصفحة
    document.head.appendChild(script);

})();