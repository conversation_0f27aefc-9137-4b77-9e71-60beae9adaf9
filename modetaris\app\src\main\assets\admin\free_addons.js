// Free Addons Admin Panel JavaScript
// Manages the Free Addons section for the Minecraft mods app

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let allMods = [];
let selectedMods = [];
let filteredMods = [];

// DOM elements
const allModsList = document.getElementById('allModsList');
const selectedModsList = document.getElementById('selectedModsList');
const searchInput = document.getElementById('searchInput');
const statusBar = document.getElementById('statusBar');
const statusMessage = document.getElementById('statusMessage');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Free Addons Admin Panel loaded');
    loadData();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();
        filterMods(searchTerm);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveChanges();
        }
    });
}

// Load all data
async function loadData() {
    showStatus('جاري تحميل البيانات...', 'info');

    try {
        await Promise.all([
            loadAllMods(),
            loadSelectedMods()
        ]);

        renderAllMods();
        renderSelectedMods();
        showStatus('تم تحميل البيانات بنجاح', 'success');
    } catch (error) {
        console.error('Error loading data:', error);
        showStatus('خطأ في تحميل البيانات', 'error');
    }
}

// Load all Addons mods from database
async function loadAllMods() {
    try {
        const { data, error } = await supabaseClient
            .from('mods')
            .select('*')
            .eq('category', 'Addons')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching mods:', error);
            throw error;
        }

        allMods = data || [];
        filteredMods = [...allMods];
        console.log(`Loaded ${allMods.length} Addons mods`);
    } catch (error) {
        console.error('Error in loadAllMods:', error);
        throw error;
    }
}

// Load currently selected Free Addons
async function loadSelectedMods() {
    try {
        const { data, error } = await supabaseClient
            .from('free_addons')
            .select(`
                *,
                mods (*)
            `)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error fetching selected mods:', error);
            throw error;
        }

        selectedMods = data || [];
        console.log(`Loaded ${selectedMods.length} selected Free Addons`);
    } catch (error) {
        console.error('Error in loadSelectedMods:', error);
        throw error;
    }
}

// Filter mods based on search term
function filterMods(searchTerm) {
    if (!searchTerm) {
        filteredMods = [...allMods];
    } else {
        filteredMods = allMods.filter(mod =>
            mod.name.toLowerCase().includes(searchTerm) ||
            mod.id.toString().includes(searchTerm)
        );
    }
    renderAllMods();
}

// Render all mods list
function renderAllMods() {
    if (filteredMods.length === 0) {
        allModsList.innerHTML = '<div class="loading">لا توجد مودات متاحة</div>';
        return;
    }

    const modsHTML = filteredMods.map(mod => {
        const isSelected = selectedMods.some(selected => selected.mods.id === mod.id);
        const mainImage = getMainImage(mod.image_urls);

        return `
            <div class="mod-item ${isSelected ? 'selected' : ''}"
                 onclick="toggleModSelection('${mod.id}')"
                 data-mod-id="${mod.id}">
                <img src="${mainImage}" alt="${mod.name}" class="mod-image"
                     onerror="this.src='../image/placeholder.svg'">
                <div class="mod-info">
                    <div class="mod-name">${mod.name}</div>
                    <div class="mod-stats">
                        📥 ${formatCount(mod.downloads || 0)} |
                        ❤️ ${formatCount(mod.likes || 0)} |
                        🆔 ${mod.id}
                    </div>
                </div>
            </div>
        `;
    }).join('');

    allModsList.innerHTML = modsHTML;
}

// Render selected mods list
function renderSelectedMods() {
    if (selectedMods.length === 0) {
        selectedModsList.innerHTML = '<div class="loading">لم يتم تحديد أي مودات بعد</div>';
        return;
    }

    const modsHTML = selectedMods.map((item, index) => {
        const mod = item.mods;
        const mainImage = getMainImage(mod.image_urls);

        return `
            <div class="mod-item selected" data-mod-id="${mod.id}">
                <img src="${mainImage}" alt="${mod.name}" class="mod-image"
                     onerror="this.src='../image/placeholder.svg'">
                <div class="mod-info">
                    <div class="mod-name">${mod.name}</div>
                    <div class="mod-stats">
                        📥 ${formatCount(mod.downloads || 0)} |
                        ❤️ ${formatCount(mod.likes || 0)} |
                        📍 ترتيب: ${item.display_order}
                    </div>
                </div>
                <button class="btn btn-danger" onclick="removeFromSelected('${mod.id}')"
                        style="margin-right: 10px; padding: 5px 10px; font-size: 12px;">
                    ❌ إزالة
                </button>
            </div>
        `;
    }).join('');

    selectedModsList.innerHTML = modsHTML;
}

// Toggle mod selection
function toggleModSelection(modId) {
    const isSelected = selectedMods.some(selected => selected.mods.id === modId);

    if (isSelected) {
        removeFromSelected(modId);
    } else {
        addToSelected(modId);
    }
}

// Add mod to selected list
function addToSelected(modId) {
    // Check if already selected
    if (selectedMods.some(selected => selected.mods.id === modId)) {
        showStatus('هذا المود محدد بالفعل', 'error');
        return;
    }

    // Check limit (20 mods max)
    if (selectedMods.length >= 20) {
        showStatus('لا يمكن إضافة أكثر من 20 مود في قسم Free Addons', 'error');
        return;
    }

    const mod = allMods.find(m => m.id === modId);
    if (!mod) {
        showStatus('لم يتم العثور على المود', 'error');
        return;
    }

    // Add to selected list with next display order
    const nextOrder = selectedMods.length > 0 ?
        Math.max(...selectedMods.map(s => s.display_order)) + 1 : 1;

    selectedMods.push({
        mod_id: modId,
        display_order: nextOrder,
        is_active: true,
        mods: mod
    });

    renderAllMods();
    renderSelectedMods();
    showStatus(`تم إضافة "${mod.name}" إلى قائمة Free Addons`, 'success');
}

// Remove mod from selected list
function removeFromSelected(modId) {
    selectedMods = selectedMods.filter(selected => selected.mods.id !== modId);

    // Reorder remaining mods
    selectedMods.forEach((item, index) => {
        item.display_order = index + 1;
    });

    renderAllMods();
    renderSelectedMods();

    const mod = allMods.find(m => m.id === modId);
    showStatus(`تم إزالة "${mod?.name || 'المود'}" من قائمة Free Addons`, 'success');
}

// Save changes to database
async function saveChanges() {
    showStatus('جاري حفظ التغييرات...', 'info');

    try {
        // First, delete all existing entries
        const { error: deleteError } = await supabaseClient
            .from('free_addons')
            .delete()
            .neq('id', 0); // Delete all rows

        if (deleteError) {
            console.error('Error deleting existing entries:', deleteError);
            throw deleteError;
        }

        // Then, insert new entries
        if (selectedMods.length > 0) {
            const insertData = selectedMods.map(item => ({
                mod_id: item.mods.id,
                display_order: item.display_order,
                is_active: true
            }));

            const { error: insertError } = await supabaseClient
                .from('free_addons')
                .insert(insertData);

            if (insertError) {
                console.error('Error inserting new entries:', insertError);
                throw insertError;
            }
        }

        showStatus(`تم حفظ ${selectedMods.length} مود في قسم Free Addons بنجاح`, 'success');

        // Reload data to ensure consistency
        await loadSelectedMods();
        renderSelectedMods();

    } catch (error) {
        console.error('Error saving changes:', error);
        showStatus('خطأ في حفظ التغييرات', 'error');
    }
}

// Utility functions
function getMainImage(imageUrls) {
    if (!imageUrls) return '../image/placeholder.svg';

    try {
        if (Array.isArray(imageUrls)) {
            return imageUrls.length > 0 ? imageUrls[0] : '../image/placeholder.svg';
        } else if (typeof imageUrls === 'string') {
            if (imageUrls.startsWith('http')) {
                return imageUrls;
            } else {
                const parsed = JSON.parse(imageUrls);
                return Array.isArray(parsed) && parsed.length > 0 ? parsed[0] : '../image/placeholder.svg';
            }
        }
    } catch (e) {
        console.error('Error parsing image URLs:', e);
    }

    return '../image/placeholder.svg';
}

function formatCount(count) {
    if (count >= 1000000) {
        return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
}

function showStatus(message, type) {
    statusMessage.textContent = message;
    statusBar.className = `status-bar ${type} show`;

    setTimeout(() => {
        statusBar.classList.remove('show');
    }, 3000);
}
