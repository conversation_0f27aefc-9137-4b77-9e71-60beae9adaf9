-- ========================================
-- التحديثات المطلوبة للجداول الموجودة
-- Required Updates for Existing Tables
-- ========================================

-- 1. تحديث جدول banner_ads لدعم حملات الاشتراك
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS campaign_id UUID;
ALTER TABLE banner_ads ADD COLUMN IF NOT EXISTS banner_type VARCHAR(20) DEFAULT 'regular';

-- إضافة المرجع بعد إنشاء جدول free_subscription_campaigns
-- (سيتم تشغيله بعد تشغيل enhanced_subscription_system.sql)

-- 2. تحديث جدول task_types الموجود
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'task_types') THEN
        -- إضافة عمود verification_method
        ALTER TABLE task_types ADD COLUMN IF NOT EXISTS verification_method VARCHAR(50) DEFAULT 'smart';
        
        -- تعطيل المنصات غير المدعومة
        UPDATE task_types SET is_active = false 
        WHERE name NOT IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join');
        
        -- تحديث المنصات المدعومة
        UPDATE task_types SET verification_method = 'smart', is_active = true
        WHERE name IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join');
        
        -- إضافة ديسكورد إذا لم يكن موجوداً
        INSERT INTO task_types (name, display_name_ar, display_name_en, icon, verification_method) VALUES
        ('discord_join', 'انضمام لخادم ديسكورد', 'Join Discord Server', 'fab fa-discord', 'smart')
        ON CONFLICT (name) DO UPDATE SET
            verification_method = 'smart',
            is_active = true;
    END IF;
END $$;

-- 3. تحديث جدول campaign_tasks الموجود
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'campaign_tasks') THEN
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS target_id VARCHAR(200);
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS retry_attempts INTEGER DEFAULT 3;
        ALTER TABLE campaign_tasks ADD COLUMN IF NOT EXISTS verification_delay_seconds INTEGER DEFAULT 30;
        
        -- تحديث طريقة التحقق
        UPDATE campaign_tasks SET verification_method = 'smart' 
        WHERE task_type IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join');
    END IF;
END $$;

-- 4. تحديث جدول user_task_progress الموجود
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_task_progress') THEN
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verification_attempts INTEGER DEFAULT 0;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verification_score INTEGER DEFAULT 0;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS error_message TEXT;
        ALTER TABLE user_task_progress ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
        
        -- تحديث الحالات الموجودة
        UPDATE user_task_progress 
        SET status = 'verified', verification_score = 100 
        WHERE status = 'completed';
    END IF;
END $$;

-- 5. تحديث جدول user_subscriptions الموجود
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_subscriptions') THEN
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS verification_score INTEGER DEFAULT 0;
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMP WITH TIME ZONE;
        ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 6. تحديث جدول free_subscription_campaigns الموجود
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'free_subscription_campaigns') THEN
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS verification_strictness VARCHAR(20) DEFAULT 'medium';
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS auto_verify_enabled BOOLEAN DEFAULT true;
        ALTER TABLE free_subscription_campaigns ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 7. إضافة المراجع المفقودة (بعد إنشاء الجداول الجديدة)
DO $$
BEGIN
    -- إضافة مرجع campaign_id في banner_ads
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'banner_ads') 
       AND EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'free_subscription_campaigns') THEN
        
        -- التحقق من عدم وجود المرجع مسبقاً
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'banner_ads_campaign_id_fkey'
        ) THEN
            ALTER TABLE banner_ads 
            ADD CONSTRAINT banner_ads_campaign_id_fkey 
            FOREIGN KEY (campaign_id) REFERENCES free_subscription_campaigns(id);
        END IF;
    END IF;
END $$;

-- 8. إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_banner_ads_campaign_id ON banner_ads(campaign_id);
CREATE INDEX IF NOT EXISTS idx_banner_ads_banner_type ON banner_ads(banner_type);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_verification_score ON user_task_progress(verification_score);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_verified_at ON user_task_progress(verified_at);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_verification_score ON user_subscriptions(verification_score);

-- 9. إنشاء دالة للتحقق من حالة اشتراك المستخدم
CREATE OR REPLACE FUNCTION get_user_subscription_status(p_user_id VARCHAR(100))
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    subscription_record RECORD;
BEGIN
    SELECT 
        us.status,
        us.expires_at,
        us.verification_score,
        fsc.title_ar,
        fsc.subscription_duration_days
    INTO subscription_record
    FROM user_subscriptions us
    JOIN free_subscription_campaigns fsc ON us.campaign_id = fsc.id
    WHERE us.user_id = p_user_id 
      AND us.status = 'active'
      AND us.expires_at > CURRENT_TIMESTAMP
    ORDER BY us.expires_at DESC
    LIMIT 1;
    
    IF FOUND THEN
        result := jsonb_build_object(
            'has_subscription', true,
            'expires_at', subscription_record.expires_at,
            'verification_score', subscription_record.verification_score,
            'campaign_title', subscription_record.title_ar,
            'days_remaining', EXTRACT(DAY FROM subscription_record.expires_at - CURRENT_TIMESTAMP)
        );
    ELSE
        result := jsonb_build_object(
            'has_subscription', false,
            'status', 'none'
        );
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 10. إنشاء دالة لتنظيف الاشتراكات المنتهية
CREATE OR REPLACE FUNCTION cleanup_expired_subscriptions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
BEGIN
    UPDATE user_subscriptions 
    SET status = 'expired', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active' AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- 11. منح الصلاحيات
GRANT EXECUTE ON FUNCTION get_user_subscription_status(VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_subscriptions() TO anon, authenticated;

-- 12. إضافة triggers للتحديث التلقائي
DO $$
BEGIN
    -- إضافة trigger لـ user_task_progress إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_user_task_progress_updated_at'
    ) THEN
        CREATE TRIGGER update_user_task_progress_updated_at
            BEFORE UPDATE ON user_task_progress
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- إضافة trigger لـ user_subscriptions إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_user_subscriptions_updated_at'
    ) THEN
        CREATE TRIGGER update_user_subscriptions_updated_at
            BEFORE UPDATE ON user_subscriptions
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 13. تنظيف البيانات القديمة
-- حذف أنواع المهام غير المدعومة
DELETE FROM task_types 
WHERE name NOT IN ('youtube_subscribe', 'telegram_subscribe', 'discord_join')
  AND is_active = false;

-- 14. رسالة النجاح
SELECT 'تم تحديث الجداول الموجودة بنجاح! ✅' as status,
       'يمكنك الآن تشغيل enhanced_subscription_system.sql' as next_step;
