{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:strider", "materials": {"default": "enderman"}, "textures": {"default": "textures/entity/strider/strider", "saddled": "textures/entity/strider/strider_saddled", "suffocated": "textures/entity/strider/strider_suffocated", "suffocated_saddled": "textures/entity/strider/strider_suffocated_saddled"}, "geometry": {"default": "geometry.strider"}, "spawn_egg": {"base_color": "#9c3436", "overlay_color": "#4d494d"}, "scripts": {"pre_animation": ["variable.animation_speed = Math.min(query.modified_move_speed, 0.25);", "variable.speed = 85.9437;", "variable.bristle_flow=Math.cos(query.modified_distance_moved * variable.speed + 180) * variable.animation_speed;", "variable.bristle_range_mod=1;", "variable.bristle_speed_mod=1;"], "animate": ["look_at_target", "walk", "bristle_flow"]}, "animations": {"look_at_target": "animation.strider.look_at_target", "walk": "animation.strider.walk", "bristle_flow": "animation.strider.bristle_flow"}, "render_controllers": ["controller.render.strider"]}}}