// ========================================
// Smart Subscription Creator JavaScript
// ========================================

// Supabase Configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr7aSgJ0ooQ4';

// Initialize Supabase client
let supabaseClient;

// Try to initialize Supabase client
function initializeSupabase() {
    try {
        // Check if Supabase is available
        if (typeof window.supabase !== 'undefined') {
            supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            console.log('✅ تم تهيئة Supabase v2 بنجاح');
            return true;
        } else if (typeof createClient !== 'undefined') {
            // Try alternative method for Supabase v2
            supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            console.log('✅ تم تهيئة Supabase بنجاح (طريقة بديلة)');
            return true;
        } else {
            console.error('❌ مكتبة Supabase غير محملة');
            console.log('💡 تأكد من تحميل مكتبة Supabase بشكل صحيح');
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في تهيئة Supabase:', error);
        console.log('💡 سيتم العمل في وضع عدم الاتصال');
        return false;
    }
}

// Global Variables
let currentSection = 0;
let validationResults = {};
let campaignData = {};
let tasksList = [];
let currentProgress = 0;

// Available Task Types
const TASK_TYPES = {
    'telegram_subscribe': {
        name_ar: 'اشتراك في قناة تيليجرام',
        name_en: 'Subscribe to Telegram Channel',
        icon: 'fab fa-telegram',
        difficulty: 2,
        completion_time: 30
    },
    'youtube_subscribe': {
        name_ar: 'اشتراك في قناة يوتيوب',
        name_en: 'Subscribe to YouTube Channel',
        icon: 'fab fa-youtube',
        difficulty: 2,
        completion_time: 45
    },
    'twitter_follow': {
        name_ar: 'متابعة على تويتر',
        name_en: 'Follow on Twitter',
        icon: 'fab fa-twitter',
        difficulty: 1,
        completion_time: 20
    },
    'facebook_like': {
        name_ar: 'إعجاب صفحة فيسبوك',
        name_en: 'Like Facebook Page',
        icon: 'fab fa-facebook',
        difficulty: 1,
        completion_time: 25
    },
    'app_download': {
        name_ar: 'تحميل تطبيق',
        name_en: 'Download App',
        icon: 'fas fa-download',
        difficulty: 3,
        completion_time: 120
    },
    'mod_download': {
        name_ar: 'تحميل مود',
        name_en: 'Download Mod',
        icon: 'fas fa-cube',
        difficulty: 2,
        completion_time: 60
    }
};

// ========================================
// Initialization
// ========================================
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل منشئ الاشتراك الذكي...');

    // Work in offline mode by default for maximum compatibility
    console.log('💡 العمل في الوضع المحلي لضمان أفضل أداء');
    showOfflineBanner();
    showNotification('المنشئ الذكي جاهز! يعمل بكامل قوته محلياً 🚀', 'success');

    // Initialize the creator
    initializeSmartCreator();
    setupEventListeners();
    loadSavedData();

    console.log('✅ تم تحميل منشئ الاشتراك الذكي بنجاح');
});

function initializeSmartCreator() {
    console.log('🚀 تهيئة منشئ الاشتراك الذكي');
    updateProgress();
    initializeValidation();
    setupCharCounters();

    // Load task types after a short delay to ensure Supabase is ready
    setTimeout(() => {
        loadTaskTypes();
    }, 500);
}

function setupEventListeners() {
    // Character counters
    document.getElementById('campaignDescAr').addEventListener('input', function() {
        updateCharCounter('descArCounter', this.value.length);
    });
    
    document.getElementById('campaignDescEn').addEventListener('input', function() {
        updateCharCounter('descEnCounter', this.value.length);
    });

    // Smart suggestions
    document.querySelectorAll('.suggestion-item').forEach(item => {
        item.addEventListener('click', function() {
            handleSuggestionClick(this.dataset.suggestion, this);
        });
    });

    // Auto-save functionality
    setInterval(autoSave, 30000); // Auto-save every 30 seconds
}

// ========================================
// Validation System
// ========================================
function validateField(field) {
    const value = field.value.trim();
    const validation = field.dataset.validation;
    const feedback = field.parentElement.querySelector('.field-feedback');
    
    if (!validation) return true;
    
    const rules = validation.split(',');
    let isValid = true;
    let message = '';
    
    for (const rule of rules) {
        const [ruleName, ruleValue] = rule.split(':');
        
        switch (ruleName) {
            case 'required':
                if (!value) {
                    isValid = false;
                    message = 'هذا الحقل مطلوب';
                }
                break;
                
            case 'min':
                if (value.length < parseInt(ruleValue)) {
                    isValid = false;
                    message = `يجب أن يكون النص ${ruleValue} أحرف على الأقل`;
                }
                break;
                
            case 'max':
                if (value.length > parseInt(ruleValue)) {
                    isValid = false;
                    message = `يجب أن يكون النص ${ruleValue} أحرف كحد أقصى`;
                }
                break;
        }
        
        if (!isValid) break;
    }
    
    // Update field appearance
    if (isValid && value) {
        field.style.borderColor = '#22c55e';
        feedback.textContent = '✓ صحيح';
        feedback.className = 'field-feedback valid';
    } else if (!isValid) {
        field.style.borderColor = '#ef4444';
        feedback.textContent = message;
        feedback.className = 'field-feedback invalid';
    } else {
        field.style.borderColor = 'rgba(255, 215, 0, 0.3)';
        feedback.textContent = '';
        feedback.className = 'field-feedback';
    }
    
    // Update validation results
    validationResults[field.id] = isValid && value;
    updateValidationStatus();
    updateProgress();
    
    return isValid;
}

function initializeValidation() {
    const validationItems = document.querySelectorAll('.validation-item');
    validationItems.forEach(item => {
        item.classList.add('pending');
    });
}

function updateValidationStatus() {
    // Update sidebar validation indicators
    const titleValid = validationResults['campaignTitleAr'] && validationResults['campaignTitleEn'];
    const descValid = validationResults['campaignDescAr'] && validationResults['campaignDescEn'];
    
    updateValidationItem('title', titleValid);
    updateValidationItem('description', descValid);
    updateValidationItem('tasks', tasksList.length > 0);
    updateValidationItem('settings', true); // Settings are optional
}

function updateValidationItem(field, isValid) {
    const item = document.querySelector(`[data-field="${field}"]`);
    if (!item) return;
    
    item.classList.remove('pending', 'valid', 'invalid');
    
    if (isValid) {
        item.classList.add('valid');
        item.querySelector('i').className = 'fas fa-check-circle';
    } else {
        item.classList.add('invalid');
        item.querySelector('i').className = 'fas fa-times-circle';
    }
}

// ========================================
// Progress Management
// ========================================
function updateProgress() {
    const totalFields = 6; // Basic fields count
    const completedFields = Object.values(validationResults).filter(Boolean).length;
    const tasksProgress = Math.min(tasksList.length / 3, 1); // Assume 3 tasks minimum
    
    currentProgress = ((completedFields + tasksProgress) / (totalFields + 1)) * 100;
    
    document.getElementById('progressFill').style.width = `${currentProgress}%`;
    document.getElementById('progressText').textContent = `${Math.round(currentProgress)}% مكتمل`;
    
    // Update validation status text
    const statusElement = document.getElementById('validationStatus');
    if (currentProgress >= 80) {
        statusElement.textContent = '✓ جاهز للنشر';
        statusElement.style.color = '#22c55e';
    } else if (currentProgress >= 50) {
        statusElement.textContent = '⚠ يحتاج مراجعة';
        statusElement.style.color = '#f59e0b';
    } else {
        statusElement.textContent = '✗ غير مكتمل';
        statusElement.style.color = '#ef4444';
    }
}

// ========================================
// Character Counters
// ========================================
function setupCharCounters() {
    updateCharCounter('descArCounter', 0);
    updateCharCounter('descEnCounter', 0);
}

function updateCharCounter(counterId, length) {
    const counter = document.getElementById(counterId);
    if (counter) {
        counter.textContent = length;
        
        // Color coding
        if (length > 400) {
            counter.style.color = '#ef4444';
        } else if (length > 300) {
            counter.style.color = '#f59e0b';
        } else {
            counter.style.color = '#22c55e';
        }
    }
}

// ========================================
// Duration Management
// ========================================
function updateDurationValue(value) {
    document.getElementById('subscriptionDuration').value = value;
    updateDurationPresets(parseInt(value));
}

function updateDurationSlider(value) {
    document.getElementById('durationSlider').value = value;
    updateDurationPresets(parseInt(value));
}

function setDuration(days) {
    document.getElementById('subscriptionDuration').value = days;
    document.getElementById('durationSlider').value = days;
    updateDurationPresets(days);
}

function updateDurationPresets(currentValue) {
    document.querySelectorAll('.duration-presets .preset-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const presetValues = [7, 30, 90, 365];
    const closestPreset = presetValues.find(val => val === currentValue);
    
    if (closestPreset) {
        const presetBtn = document.querySelector(`[onclick="setDuration(${closestPreset})"]`);
        if (presetBtn) presetBtn.classList.add('active');
    }
}

// ========================================
// User Limit Management
// ========================================
function toggleUserLimit(checkbox) {
    const userLimitGroup = document.getElementById('userLimitGroup');
    if (checkbox.checked) {
        userLimitGroup.style.display = 'block';
    } else {
        userLimitGroup.style.display = 'none';
        document.getElementById('maxUsers').value = '';
    }
}

function setUserLimit(limit) {
    document.getElementById('maxUsers').value = limit;
    document.getElementById('enableUserLimit').checked = true;
    toggleUserLimit(document.getElementById('enableUserLimit'));
    
    // Update preset buttons
    document.querySelectorAll('.limit-presets .preset-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[onclick="setUserLimit(${limit})"]`);
    if (activeBtn) activeBtn.classList.add('active');
}

// ========================================
// Verification Settings
// ========================================
function updateVerificationInfo(select) {
    const info = document.getElementById('verificationInfo');
    const value = select.value;
    
    const infoTexts = {
        'low': 'تحقق بسيط - قبول سريع مع مراجعة أقل',
        'medium': 'تحقق متوازن مع إمكانية إعادة المحاولة',
        'high': 'تحقق صارم - مراجعة دقيقة لكل مهمة'
    };
    
    info.querySelector('span').textContent = infoTexts[value];
}

// ========================================
// End Date Validation
// ========================================
function validateEndDate(input) {
    const selectedDate = new Date(input.value);
    const now = new Date();
    const feedback = input.parentElement.querySelector('.field-feedback');
    
    if (selectedDate <= now) {
        input.style.borderColor = '#ef4444';
        feedback.textContent = 'يجب أن يكون التاريخ في المستقبل';
        feedback.className = 'field-feedback invalid';
        return false;
    } else {
        input.style.borderColor = '#22c55e';
        feedback.textContent = '✓ تاريخ صحيح';
        feedback.className = 'field-feedback valid';
        return true;
    }
}

// ========================================
// Smart Suggestions
// ========================================
function handleSuggestionClick(suggestion, element) {
    // Remove active class from all suggestions
    document.querySelectorAll('.suggestion-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to clicked suggestion
    if (element) {
        element.classList.add('active');
    }

    switch (suggestion) {
        case 'quick-setup':
            applyQuickSetup();
            break;
        case 'advanced-setup':
            applyAdvancedSetup();
            break;
        case 'expert-setup':
            applyExpertSetup();
            break;
        case 'ai-optimize':
            optimizeWithAI();
            break;
    }
}

function applyQuickSetup() {
    // Fill with quick setup defaults
    document.getElementById('campaignTitleAr').value = 'احصل على اشتراك مجاني لمدة شهر!';
    document.getElementById('campaignTitleEn').value = 'Get Free 1-Month Subscription!';
    document.getElementById('campaignDescAr').value = 'أكمل المهام البسيطة التالية واحصل على اشتراك مجاني لمدة شهر كامل مع وصول لجميع الميزات المميزة.';
    document.getElementById('campaignDescEn').value = 'Complete the following simple tasks and get a free one-month subscription with access to all premium features.';
    
    setDuration(30);
    document.getElementById('verificationStrictness').value = 'medium';
    
    // Trigger validation
    document.querySelectorAll('input[data-validation], textarea[data-validation]').forEach(field => {
        validateField(field);
    });
    
    showNotification('تم تطبيق الإعداد السريع بنجاح!', 'success');
}

function applyAdvancedSetup() {
    // إعداد متقدم للحملات الاحترافية
    showNotification('جاري تطبيق الإعداد المتقدم...', 'info');

    // إعدادات متقدمة للحملة
    document.getElementById('campaignTitleAr').value = '🎯 حملة اشتراك مميز - وصول حصري للمحتوى المتقدم';
    document.getElementById('campaignTitleEn').value = '🎯 Premium Subscription Campaign - Exclusive Access to Advanced Content';

    document.getElementById('campaignDescAr').value = 'انضم إلى مجتمعنا المميز واحصل على وصول حصري للمحتوى المتقدم، التحديثات المبكرة، والدعم الفني المباشر. هذه فرصة محدودة للحصول على تجربة فريدة مع جميع الميزات المتقدمة بدون أي قيود.';

    document.getElementById('campaignDescEn').value = 'Join our premium community and get exclusive access to advanced content, early updates, and direct technical support. This is a limited opportunity to get a unique experience with all advanced features without any restrictions.';

    // إعدادات متقدمة للمدة والحدود
    setDuration(60); // شهرين للحملات المتقدمة

    // تفعيل حد المستخدمين مع عدد محدود
    document.getElementById('enableUserLimit').checked = true;
    toggleUserLimit(document.getElementById('enableUserLimit'));
    setUserLimit(500); // حد متقدم

    // إعدادات تحقق متقدمة
    document.getElementById('verificationStrictness').value = 'high';
    updateVerificationInfo(document.getElementById('verificationStrictness'));

    // تفعيل التحقق التلقائي
    document.getElementById('autoVerifyEnabled').checked = true;

    // تطبيق قالب المهام الشامل تلقائياً
    setTimeout(() => {
        applyTaskTemplate('complete');

        // إضافة مهام متقدمة إضافية
        addAdvancedTasks();

        showNotification('✅ تم تطبيق الإعداد المتقدم بنجاح! تم إعداد حملة احترافية مع 5 مهام متنوعة.', 'success');
    }, 1000);

    // تحديث التحقق
    setTimeout(() => {
        document.querySelectorAll('input[data-validation], textarea[data-validation]').forEach(field => {
            validateField(field);
        });
        calculateSmartAnalytics();
    }, 1500);
}

function addAdvancedTasks() {
    // إضافة مهام متقدمة إضافية للحملات الاحترافية
    const advancedTasks = [
        {
            type: 'youtube_subscribe',
            title_ar: 'اشترك في القناة الرسمية وفعل الجرس',
            title_en: 'Subscribe to Official Channel and Enable Notifications',
            description_ar: 'اشترك في قناتنا الرسمية على يوتيوب وفعل جرس الإشعارات للحصول على آخر التحديثات والمحتوى الحصري',
            description_en: 'Subscribe to our official YouTube channel and enable notifications to get latest updates and exclusive content',
            target_url: 'https://youtube.com/@AgentO_MCPE',
            is_required: true
        },
        {
            type: 'telegram_subscribe',
            title_ar: 'انضم لمجموعة التيليجرام المميزة',
            title_en: 'Join Premium Telegram Group',
            description_ar: 'انضم لمجموعتنا المميزة على التيليجرام للحصول على الدعم الفني المباشر والمحتوى الحصري',
            description_en: 'Join our premium Telegram group for direct technical support and exclusive content',
            target_url: 'https://t.me/modetaris_premium',
            is_required: true
        },
        {
            type: 'app_download',
            title_ar: 'حمل التطبيق المساعد وقيمه',
            title_en: 'Download Helper App and Rate It',
            description_ar: 'حمل تطبيقنا المساعد من متجر جوجل بلاي وأعطه تقييم 5 نجوم لدعم المطورين',
            description_en: 'Download our helper app from Google Play Store and give it a 5-star rating to support developers',
            target_url: 'https://play.google.com/store/apps/details?id=com.modetaris.helper',
            is_required: false
        }
    ];

    // إضافة المهام المتقدمة للقائمة الحالية
    advancedTasks.forEach((taskData, index) => {
        const existingTask = tasksList.find(task => task.type === taskData.type);
        if (!existingTask) {
            tasksList.push({
                id: Date.now().toString() + '_advanced_' + index,
                ...taskData,
                display_order: tasksList.length + 1
            });
        }
    });

    // تحديث واجهة المستخدم
    renderTasksList();
    updateValidationStatus();

    console.log('✅ تم إضافة المهام المتقدمة:', advancedTasks.length, 'مهمة');
}

function applyExpertSetup() {
    // إعداد خبير للحملات الاحترافية المتقدمة
    showNotification('جاري تطبيق الإعداد الخبير...', 'info');

    // إعدادات خبير للحملة
    document.getElementById('campaignTitleAr').value = '👑 حملة VIP حصرية - عضوية مدى الحياة مع مزايا لا محدودة';
    document.getElementById('campaignTitleEn').value = '👑 Exclusive VIP Campaign - Lifetime Membership with Unlimited Benefits';

    document.getElementById('campaignDescAr').value = 'عرض استثنائي للخبراء والمحترفين! احصل على عضوية VIP مدى الحياة مع وصول كامل لجميع الميزات المتقدمة، الدعم الفني المباشر على مدار الساعة، المحتوى الحصري، التحديثات المبكرة، وأولوية في جميع الخدمات. هذا العرض محدود جداً ومتاح فقط لأول 100 مستخدم.';

    document.getElementById('campaignDescEn').value = 'Exceptional offer for experts and professionals! Get lifetime VIP membership with full access to all advanced features, 24/7 direct technical support, exclusive content, early updates, and priority in all services. This offer is very limited and available only for the first 100 users.';

    // إعدادات خبير للمدة والحدود
    setDuration(365); // سنة كاملة للخبراء

    // تفعيل حد المستخدمين مع عدد محدود جداً
    document.getElementById('enableUserLimit').checked = true;
    toggleUserLimit(document.getElementById('enableUserLimit'));
    setUserLimit(100); // حد خبير محدود

    // إعدادات تحقق خبير
    document.getElementById('verificationStrictness').value = 'high';
    updateVerificationInfo(document.getElementById('verificationStrictness'));

    // تفعيل التحقق التلقائي
    document.getElementById('autoVerifyEnabled').checked = true;

    // تطبيق مهام خبير مخصصة
    setTimeout(() => {
        applyExpertTasks();

        showNotification('🎖️ تم تطبيق الإعداد الخبير بنجاح! تم إعداد حملة VIP مع 8 مهام متنوعة للخبراء.', 'success');
    }, 1000);

    // تحديث التحقق والتحليلات
    setTimeout(() => {
        document.querySelectorAll('input[data-validation], textarea[data-validation]').forEach(field => {
            validateField(field);
        });
        calculateSmartAnalytics();
    }, 1500);
}

function applyExpertTasks() {
    // مسح المهام الحالية
    tasksList = [];

    // مهام خبير شاملة ومتنوعة
    const expertTasks = [
        {
            type: 'youtube_subscribe',
            title_ar: 'اشترك في القناة الرسمية وفعل الجرس',
            title_en: 'Subscribe to Official Channel and Enable Bell',
            description_ar: 'اشترك في قناتنا الرسمية على يوتيوب وفعل جرس الإشعارات للحصول على المحتوى الحصري للخبراء',
            description_en: 'Subscribe to our official YouTube channel and enable notifications for exclusive expert content',
            target_url: 'https://youtube.com/@AgentO_MCPE',
            is_required: true
        },
        {
            type: 'telegram_subscribe',
            title_ar: 'انضم لمجموعة الخبراء على التيليجرام',
            title_en: 'Join Expert Group on Telegram',
            description_ar: 'انضم لمجموعة الخبراء الحصرية على التيليجرام للدعم الفني المتقدم والنقاشات التقنية',
            description_en: 'Join exclusive expert group on Telegram for advanced technical support and discussions',
            target_url: 'https://t.me/modetaris_experts',
            is_required: true
        },
        {
            type: 'twitter_follow',
            title_ar: 'تابع الحساب الرسمي على تويتر',
            title_en: 'Follow Official Twitter Account',
            description_ar: 'تابع حسابنا الرسمي على تويتر للحصول على آخر الأخبار والتحديثات الفورية',
            description_en: 'Follow our official Twitter account for latest news and instant updates',
            target_url: 'https://twitter.com/modetaris_official',
            is_required: true
        },
        {
            type: 'facebook_like',
            title_ar: 'أعجب بالصفحة الرسمية على فيسبوك',
            title_en: 'Like Official Facebook Page',
            description_ar: 'اضغط إعجاب على صفحتنا الرسمية على فيسبوك وشارك المنشورات مع أصدقائك',
            description_en: 'Like our official Facebook page and share posts with your friends',
            target_url: 'https://facebook.com/modetaris.official',
            is_required: true
        },
        {
            type: 'app_download',
            title_ar: 'حمل التطبيق الرئيسي وقيمه 5 نجوم',
            title_en: 'Download Main App and Rate 5 Stars',
            description_ar: 'حمل تطبيقنا الرئيسي من متجر جوجل بلاي وأعطه تقييم 5 نجوم مع تعليق إيجابي',
            description_en: 'Download our main app from Google Play Store and give it 5 stars with positive review',
            target_url: 'https://play.google.com/store/apps/details?id=com.modetaris.main',
            is_required: true
        },
        {
            type: 'app_download',
            title_ar: 'حمل التطبيق المساعد وقيمه',
            title_en: 'Download Helper App and Rate',
            description_ar: 'حمل التطبيق المساعد أيضاً وأعطه تقييم عالي لدعم فريق التطوير',
            description_en: 'Download helper app too and give it high rating to support development team',
            target_url: 'https://play.google.com/store/apps/details?id=com.modetaris.helper',
            is_required: false
        },
        {
            type: 'mod_download',
            title_ar: 'حمل مود VIP الحصري',
            title_en: 'Download Exclusive VIP Mod',
            description_ar: 'حمل المود الحصري للأعضاء VIP واستمتع بميزات لا تتوفر في أي مكان آخر',
            description_en: 'Download exclusive VIP mod and enjoy features not available anywhere else',
            target_url: 'https://modetaris.com/vip-exclusive-mod',
            is_required: false
        },
        {
            type: 'mod_download',
            title_ar: 'حمل حزمة الخبراء الكاملة',
            title_en: 'Download Complete Expert Pack',
            description_ar: 'حمل حزمة الخبراء الكاملة التي تحتوي على أفضل المودات والإضافات المتقدمة',
            description_en: 'Download complete expert pack containing best mods and advanced addons',
            target_url: 'https://modetaris.com/expert-complete-pack',
            is_required: false
        }
    ];

    // إضافة جميع المهام
    expertTasks.forEach((taskData, index) => {
        tasksList.push({
            id: Date.now().toString() + '_expert_' + index,
            ...taskData,
            display_order: index + 1
        });
    });

    // تحديث واجهة المستخدم
    renderTasksList();
    updateValidationStatus();

    console.log('👑 تم إضافة مهام الخبراء:', expertTasks.length, 'مهمة');
}

function optimizeWithAI() {
    showNotification('جاري تحسين الحملة بالذكاء الاصطناعي...', 'info');

    // Simulate AI optimization with real improvements
    setTimeout(() => {
        applyAIOptimizations();
        calculateSmartAnalytics();
        showNotification('✨ تم تحسين الحملة بالذكاء الاصطناعي بنجاح!', 'success');
    }, 2000);
}

function applyAIOptimizations() {
    // تحسينات ذكية للحملة
    const currentTitle = document.getElementById('campaignTitleAr').value;
    const currentDesc = document.getElementById('campaignDescAr').value;

    // تحسين العنوان إذا كان فارغاً أو بسيطاً
    if (!currentTitle || currentTitle.length < 20) {
        document.getElementById('campaignTitleAr').value = '🚀 عرض حصري محدود - اشتراك مجاني مع مزايا لا تُقاوم!';
        document.getElementById('campaignTitleEn').value = '🚀 Exclusive Limited Offer - Free Subscription with Irresistible Benefits!';
    }

    // تحسين الوصف إذا كان فارغاً أو قصيراً
    if (!currentDesc || currentDesc.length < 50) {
        document.getElementById('campaignDescAr').value = 'فرصة ذهبية لا تُفوت! احصل على اشتراك مجاني كامل مع وصول لجميع الميزات المتقدمة. أكمل المهام البسيطة أدناه واستمتع بتجربة مميزة بدون أي قيود أو إعلانات مزعجة.';
        document.getElementById('campaignDescEn').value = 'Golden opportunity not to be missed! Get a complete free subscription with access to all advanced features. Complete the simple tasks below and enjoy a premium experience without any restrictions or annoying ads.';
    }

    // تحسين إعدادات المدة بناءً على عدد المهام
    const tasksCount = tasksList.length;
    if (tasksCount <= 2) {
        setDuration(14); // أسبوعين للمهام القليلة
    } else if (tasksCount <= 4) {
        setDuration(30); // شهر للمهام المتوسطة
    } else {
        setDuration(45); // شهر ونصف للمهام الكثيرة
    }

    // تحسين مستوى التحقق بناءً على المدة
    const duration = parseInt(document.getElementById('subscriptionDuration').value);
    if (duration <= 14) {
        document.getElementById('verificationStrictness').value = 'low';
    } else if (duration <= 30) {
        document.getElementById('verificationStrictness').value = 'medium';
    } else {
        document.getElementById('verificationStrictness').value = 'high';
    }

    // تحديث التحقق
    document.querySelectorAll('input[data-validation], textarea[data-validation]').forEach(field => {
        validateField(field);
    });

    console.log('🤖 تم تطبيق التحسينات الذكية');
}

// ========================================
// Smart Analytics
// ========================================
function calculateSmartAnalytics() {
    const duration = parseInt(document.getElementById('subscriptionDuration').value) || 30;
    const tasksCount = tasksList.length || 3;
    const strictness = document.getElementById('verificationStrictness').value;
    
    // Calculate success rate based on difficulty
    let baseSuccessRate = 85;
    if (strictness === 'high') baseSuccessRate -= 15;
    if (strictness === 'low') baseSuccessRate += 10;
    if (tasksCount > 5) baseSuccessRate -= (tasksCount - 5) * 5;
    
    const successRate = Math.max(Math.min(baseSuccessRate, 95), 40);
    
    // Calculate completion time
    const avgTaskTime = tasksList.reduce((sum, task) => {
        return sum + (TASK_TYPES[task.type]?.completion_time || 60);
    }, 0) / Math.max(tasksCount, 1);
    
    const completionTime = Math.round(avgTaskTime * tasksCount / 60); // in minutes
    
    // Calculate difficulty rating
    const avgDifficulty = tasksList.reduce((sum, task) => {
        return sum + (TASK_TYPES[task.type]?.difficulty || 2);
    }, 0) / Math.max(tasksCount, 1);
    
    const difficultyLabels = ['سهل', 'متوسط', 'صعب'];
    const difficultyRating = difficultyLabels[Math.round(avgDifficulty) - 1] || 'متوسط';
    
    // Update UI
    document.getElementById('successRate').textContent = `${successRate}%`;
    document.getElementById('completionTime').textContent = `${completionTime} دقيقة`;
    document.getElementById('difficultyRating').textContent = difficultyRating;
}

// ========================================
// Utility Functions
// ========================================
function showNotification(message, type = 'info') {
    // Remove existing notifications
    document.querySelectorAll('.notification').forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = 'notification';

    // Define colors for different types
    let backgroundColor;
    switch (type) {
        case 'success':
            backgroundColor = '#22c55e';
            break;
        case 'error':
            backgroundColor = '#ef4444';
            break;
        case 'warning':
            backgroundColor = '#f59e0b';
            break;
        default:
            backgroundColor = '#3b82f6';
    }

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${backgroundColor};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.3s ease-out;
        max-width: 500px;
        text-align: center;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds for warnings, 3 seconds for others
    const duration = type === 'warning' ? 5000 : 3000;
    setTimeout(() => {
        notification.style.animation = 'slideOutUp 0.3s ease-in';
        setTimeout(() => notification.remove(), 300);
    }, duration);
}

function autoSave() {
    const data = collectFormData();
    localStorage.setItem('smartSubscriptionDraft', JSON.stringify(data));
    console.log('💾 تم الحفظ التلقائي');
}

function loadSavedData() {
    const saved = localStorage.getItem('smartSubscriptionDraft');
    if (saved) {
        try {
            const data = JSON.parse(saved);
            populateFormData(data);
            console.log('📂 تم تحميل البيانات المحفوظة');
        } catch (error) {
            console.error('خطأ في تحميل البيانات المحفوظة:', error);
        }
    }
}

function collectFormData() {
    return {
        title_ar: document.getElementById('campaignTitleAr').value,
        title_en: document.getElementById('campaignTitleEn').value,
        description_ar: document.getElementById('campaignDescAr').value,
        description_en: document.getElementById('campaignDescEn').value,
        duration: document.getElementById('subscriptionDuration').value,
        max_users: document.getElementById('maxUsers').value,
        end_date: document.getElementById('campaignEndDate').value,
        verification_strictness: document.getElementById('verificationStrictness').value,
        auto_verify: document.getElementById('autoVerifyEnabled').checked,
        user_limit_enabled: document.getElementById('enableUserLimit').checked
    };
}

function populateFormData(data) {
    if (data.title_ar) document.getElementById('campaignTitleAr').value = data.title_ar;
    if (data.title_en) document.getElementById('campaignTitleEn').value = data.title_en;
    if (data.description_ar) document.getElementById('campaignDescAr').value = data.description_ar;
    if (data.description_en) document.getElementById('campaignDescEn').value = data.description_en;
    if (data.duration) setDuration(parseInt(data.duration));
    if (data.max_users) document.getElementById('maxUsers').value = data.max_users;
    if (data.end_date) document.getElementById('campaignEndDate').value = data.end_date;
    if (data.verification_strictness) document.getElementById('verificationStrictness').value = data.verification_strictness;
    if (data.auto_verify !== undefined) document.getElementById('autoVerifyEnabled').checked = data.auto_verify;
    if (data.user_limit_enabled !== undefined) {
        document.getElementById('enableUserLimit').checked = data.user_limit_enabled;
        toggleUserLimit(document.getElementById('enableUserLimit'));
    }
    
    // Trigger validation for all fields
    document.querySelectorAll('input[data-validation], textarea[data-validation]').forEach(field => {
        validateField(field);
    });
}

// ========================================
// Load Task Types
// ========================================
function loadTaskTypes() {
    console.log('📋 استخدام أنواع المهام المحددة مسبقًا');
    console.log('✅ أنواع المهام جاهزة:', Object.keys(TASK_TYPES).length, 'نوع');

    // Task types are already defined in TASK_TYPES constant
    // Working completely offline for maximum compatibility
    console.log('💡 العمل في الوضع المحلي - لا حاجة لقاعدة البيانات');
}

// ========================================
// Template Management
// ========================================
function saveTemplate() {
    const data = collectFormData();
    const templateName = prompt('اسم القالب:');
    
    if (templateName) {
        const templates = JSON.parse(localStorage.getItem('subscriptionTemplates') || '{}');
        templates[templateName] = data;
        localStorage.setItem('subscriptionTemplates', JSON.stringify(templates));
        showNotification(`تم حفظ القالب "${templateName}" بنجاح!`, 'success');
    }
}

function loadTemplate() {
    const templates = JSON.parse(localStorage.getItem('subscriptionTemplates') || '{}');
    const templateNames = Object.keys(templates);
    
    if (templateNames.length === 0) {
        showNotification('لا توجد قوالب محفوظة', 'info');
        return;
    }
    
    const templateName = prompt(`اختر قالب:\n${templateNames.join('\n')}`);
    
    if (templateName && templates[templateName]) {
        populateFormData(templates[templateName]);
        showNotification(`تم تحميل القالب "${templateName}" بنجاح!`, 'success');
    }
}

// ========================================
// Section Navigation
// ========================================
const sections = ['basicInfo', 'campaignSettings', 'tasksManagement', 'displayCustomization', 'previewPublish'];

function nextSection() {
    if (currentSection < sections.length - 1) {
        // Validate current section before proceeding
        if (validateCurrentSection()) {
            currentSection++;
            showSection(sections[currentSection]);
            updateNavigationButtons();
        }
    }
}

function previousSection() {
    if (currentSection > 0) {
        currentSection--;
        showSection(sections[currentSection]);
        updateNavigationButtons();
    }
}

function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.form-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    document.getElementById(sectionId).classList.add('active');

    // Update progress
    updateProgress();

    // Special handling for preview section
    if (sectionId === 'previewPublish') {
        updatePreview();
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    // Previous button
    if (currentSection === 0) {
        prevBtn.style.display = 'none';
    } else {
        prevBtn.style.display = 'inline-flex';
    }

    // Next button
    if (currentSection === sections.length - 1) {
        nextBtn.style.display = 'none';
    } else {
        nextBtn.style.display = 'inline-flex';
        nextBtn.textContent = currentSection === sections.length - 2 ? 'معاينة ونشر' : 'التالي';
    }
}

function validateCurrentSection() {
    switch (sections[currentSection]) {
        case 'basicInfo':
            return validateBasicInfo();
        case 'campaignSettings':
            return validateCampaignSettings();
        case 'tasksManagement':
            return validateTasks();
        default:
            return true;
    }
}

function validateBasicInfo() {
    const titleAr = document.getElementById('campaignTitleAr').value.trim();
    const titleEn = document.getElementById('campaignTitleEn').value.trim();
    const descAr = document.getElementById('campaignDescAr').value.trim();
    const descEn = document.getElementById('campaignDescEn').value.trim();

    if (!titleAr || !titleEn || !descAr || !descEn) {
        showNotification('يرجى ملء جميع الحقول الأساسية', 'error');
        return false;
    }

    return true;
}

function validateCampaignSettings() {
    const duration = document.getElementById('subscriptionDuration').value;
    const endDate = document.getElementById('campaignEndDate').value;

    if (!duration || duration < 1) {
        showNotification('يرجى تحديد مدة صحيحة للاشتراك', 'error');
        return false;
    }

    if (endDate && new Date(endDate) <= new Date()) {
        showNotification('تاريخ انتهاء الحملة يجب أن يكون في المستقبل', 'error');
        return false;
    }

    return true;
}

function validateTasks() {
    if (tasksList.length === 0) {
        showNotification('يجب إضافة مهمة واحدة على الأقل', 'error');
        return false;
    }

    return true;
}

// ========================================
// AI Generation Functions (to be implemented)
// ========================================
function generateWithAI(type) {
    showNotification('ميزة التوليد بالذكاء الاصطناعي قيد التطوير', 'info');
}

function optimizeSettings() {
    showNotification('جاري تحسين الإعدادات...', 'info');

    setTimeout(() => {
        calculateSmartAnalytics();
        showNotification('تم تحسين الإعدادات بنجاح!', 'success');
    }, 1500);
}

// ========================================
// Tasks Management
// ========================================
function addCustomTask() {
    document.getElementById('taskForm').style.display = 'block';
    document.getElementById('taskForm').scrollIntoView({ behavior: 'smooth' });
}

function cancelTask() {
    document.getElementById('taskForm').style.display = 'none';
    clearTaskForm();
}

function clearTaskForm() {
    document.getElementById('taskType').value = '';
    document.getElementById('taskTitleAr').value = '';
    document.getElementById('taskTitleEn').value = '';
    document.getElementById('taskDescAr').value = '';
    document.getElementById('taskDescEn').value = '';
    document.getElementById('taskUrl').value = '';
    document.getElementById('taskRequired').checked = true;
}

function updateTaskForm(taskType) {
    if (!taskType) return;

    const taskInfo = TASK_TYPES[taskType];
    if (!taskInfo) return;

    // Auto-fill task titles
    document.getElementById('taskTitleAr').value = taskInfo.name_ar;
    document.getElementById('taskTitleEn').value = taskInfo.name_en;

    // Auto-fill descriptions based on task type
    const descriptions = {
        'telegram_subscribe': {
            ar: 'اشترك في قناتنا على التيليجرام للحصول على آخر التحديثات والمودات الجديدة',
            en: 'Subscribe to our Telegram channel for latest updates and new mods'
        },
        'youtube_subscribe': {
            ar: 'اشترك في قناتنا على اليوتيوب ولا تنس تفعيل الجرس للإشعارات',
            en: 'Subscribe to our YouTube channel and don\'t forget to ring the bell for notifications'
        },
        'twitter_follow': {
            ar: 'تابعنا على تويتر للحصول على آخر الأخبار والتحديثات',
            en: 'Follow us on Twitter for latest news and updates'
        },
        'facebook_like': {
            ar: 'اضغط إعجاب على صفحتنا على فيسبوك وشارك المحتوى مع أصدقائك',
            en: 'Like our Facebook page and share content with your friends'
        },
        'app_download': {
            ar: 'حمل التطبيق الجديد من متجر جوجل بلاي واستمتع بالميزات الجديدة',
            en: 'Download the new app from Google Play Store and enjoy new features'
        },
        'mod_download': {
            ar: 'حمل هذا المود الرائع واستمتع بتجربة لعب محسنة',
            en: 'Download this amazing mod and enjoy enhanced gaming experience'
        }
    };

    if (descriptions[taskType]) {
        document.getElementById('taskDescAr').value = descriptions[taskType].ar;
        document.getElementById('taskDescEn').value = descriptions[taskType].en;
    }
}

function saveTask() {
    const taskType = document.getElementById('taskType').value;
    const titleAr = document.getElementById('taskTitleAr').value.trim();
    const titleEn = document.getElementById('taskTitleEn').value.trim();
    const descAr = document.getElementById('taskDescAr').value.trim();
    const descEn = document.getElementById('taskDescEn').value.trim();
    const url = document.getElementById('taskUrl').value.trim();
    const required = document.getElementById('taskRequired').checked;

    // Validation
    if (!taskType || !titleAr || !titleEn || !url) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // URL validation
    try {
        new URL(url);
    } catch {
        showNotification('يرجى إدخال رابط صحيح', 'error');
        return;
    }

    // Create task object
    const task = {
        id: Date.now().toString(),
        type: taskType,
        title_ar: titleAr,
        title_en: titleEn,
        description_ar: descAr,
        description_en: descEn,
        target_url: url,
        is_required: required,
        display_order: tasksList.length + 1
    };

    // Add to tasks list
    tasksList.push(task);

    // Update UI
    renderTasksList();
    cancelTask();
    updateValidationStatus();
    calculateSmartAnalytics();

    showNotification('تم إضافة المهمة بنجاح!', 'success');
}

function renderTasksList() {
    const container = document.getElementById('tasksList');

    if (tasksList.length === 0) {
        container.innerHTML = `
            <div class="empty-tasks">
                <i class="fas fa-inbox"></i>
                <p>لم يتم إضافة أي مهام بعد</p>
                <button class="btn btn-primary" onclick="addCustomTask()">
                    <i class="fas fa-plus"></i> إضافة أول مهمة
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = tasksList.map(task => {
        const taskInfo = TASK_TYPES[task.type];
        return `
            <div class="task-item" data-task-id="${task.id}">
                <div class="task-info">
                    <div class="task-icon">
                        <i class="${taskInfo?.icon || 'fas fa-tasks'}"></i>
                    </div>
                    <div class="task-details">
                        <h5>${task.title_ar}</h5>
                        <p>${task.description_ar || 'لا يوجد وصف'}</p>
                        <small style="color: #999;">
                            ${task.is_required ? 'مهمة إجبارية' : 'مهمة اختيارية'} •
                            <a href="${task.target_url}" target="_blank" style="color: #ffd700;">عرض الرابط</a>
                        </small>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-ghost btn-sm" onclick="editTask('${task.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-ghost btn-sm" onclick="deleteTask('${task.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function deleteTask(taskId) {
    if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
        tasksList = tasksList.filter(task => task.id !== taskId);
        renderTasksList();
        updateValidationStatus();
        calculateSmartAnalytics();
        showNotification('تم حذف المهمة بنجاح', 'success');
    }
}

function editTask(taskId) {
    const task = tasksList.find(t => t.id === taskId);
    if (!task) return;

    // Fill form with task data
    document.getElementById('taskType').value = task.type;
    document.getElementById('taskTitleAr').value = task.title_ar;
    document.getElementById('taskTitleEn').value = task.title_en;
    document.getElementById('taskDescAr').value = task.description_ar;
    document.getElementById('taskDescEn').value = task.description_en;
    document.getElementById('taskUrl').value = task.target_url;
    document.getElementById('taskRequired').checked = task.is_required;

    // Show form
    document.getElementById('taskForm').style.display = 'block';
    document.getElementById('taskForm').scrollIntoView({ behavior: 'smooth' });

    // Change save button to update
    const saveBtn = document.querySelector('#taskForm .btn-primary');
    saveBtn.innerHTML = '<i class="fas fa-save"></i> تحديث المهمة';
    saveBtn.onclick = () => updateTask(taskId);
}

function updateTask(taskId) {
    const taskIndex = tasksList.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return;

    const taskType = document.getElementById('taskType').value;
    const titleAr = document.getElementById('taskTitleAr').value.trim();
    const titleEn = document.getElementById('taskTitleEn').value.trim();
    const descAr = document.getElementById('taskDescAr').value.trim();
    const descEn = document.getElementById('taskDescEn').value.trim();
    const url = document.getElementById('taskUrl').value.trim();
    const required = document.getElementById('taskRequired').checked;

    // Validation
    if (!taskType || !titleAr || !titleEn || !url) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Update task
    tasksList[taskIndex] = {
        ...tasksList[taskIndex],
        type: taskType,
        title_ar: titleAr,
        title_en: titleEn,
        description_ar: descAr,
        description_en: descEn,
        target_url: url,
        is_required: required
    };

    // Update UI
    renderTasksList();
    cancelTask();

    // Reset save button
    const saveBtn = document.querySelector('#taskForm .btn-primary');
    saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ المهمة';
    saveBtn.onclick = saveTask;

    showNotification('تم تحديث المهمة بنجاح!', 'success');
}

// ========================================
// Task Templates
// ========================================
function applyTaskTemplate(templateType) {
    const templates = {
        'social': [
            {
                type: 'telegram_subscribe',
                title_ar: 'اشترك في قناة التيليجرام',
                title_en: 'Subscribe to Telegram Channel',
                description_ar: 'اشترك في قناتنا على التيليجرام للحصول على آخر التحديثات',
                description_en: 'Subscribe to our Telegram channel for latest updates',
                target_url: 'https://t.me/modetaris',
                is_required: true
            },
            {
                type: 'youtube_subscribe',
                title_ar: 'اشترك في قناة اليوتيوب',
                title_en: 'Subscribe to YouTube Channel',
                description_ar: 'اشترك في قناتنا على اليوتيوب ولا تنس تفعيل الجرس',
                description_en: 'Subscribe to our YouTube channel and ring the bell',
                target_url: 'https://youtube.com/@modetaris',
                is_required: true
            },
            {
                type: 'twitter_follow',
                title_ar: 'تابعنا على تويتر',
                title_en: 'Follow us on Twitter',
                description_ar: 'تابعنا على تويتر للحصول على آخر الأخبار',
                description_en: 'Follow us on Twitter for latest news',
                target_url: 'https://twitter.com/modetaris',
                is_required: true
            }
        ],
        'apps': [
            {
                type: 'app_download',
                title_ar: 'حمل التطبيق الجديد',
                title_en: 'Download New App',
                description_ar: 'حمل تطبيقنا الجديد من متجر جوجل بلاي',
                description_en: 'Download our new app from Google Play Store',
                target_url: 'https://play.google.com/store/apps/details?id=com.modetaris.newapp',
                is_required: true
            },
            {
                type: 'mod_download',
                title_ar: 'حمل مود مميز',
                title_en: 'Download Featured Mod',
                description_ar: 'حمل هذا المود الرائع واستمتع بتجربة محسنة',
                description_en: 'Download this amazing mod and enjoy enhanced experience',
                target_url: 'https://modetaris.com/featured-mod',
                is_required: false
            }
        ],
        'complete': [
            {
                type: 'telegram_subscribe',
                title_ar: 'اشترك في التيليجرام',
                title_en: 'Subscribe to Telegram',
                description_ar: 'اشترك في قناتنا على التيليجرام',
                description_en: 'Subscribe to our Telegram channel',
                target_url: 'https://t.me/modetaris',
                is_required: true
            },
            {
                type: 'youtube_subscribe',
                title_ar: 'اشترك في اليوتيوب',
                title_en: 'Subscribe to YouTube',
                description_ar: 'اشترك في قناتنا على اليوتيوب',
                description_en: 'Subscribe to our YouTube channel',
                target_url: 'https://youtube.com/@modetaris',
                is_required: true
            },
            {
                type: 'app_download',
                title_ar: 'حمل التطبيق',
                title_en: 'Download App',
                description_ar: 'حمل تطبيقنا من متجر جوجل بلاي',
                description_en: 'Download our app from Google Play Store',
                target_url: 'https://play.google.com/store/apps/details?id=com.modetaris.app',
                is_required: true
            },
            {
                type: 'facebook_like',
                title_ar: 'أعجب بصفحة فيسبوك',
                title_en: 'Like Facebook Page',
                description_ar: 'اضغط إعجاب على صفحتنا على فيسبوك',
                description_en: 'Like our Facebook page',
                target_url: 'https://facebook.com/modetaris',
                is_required: false
            },
            {
                type: 'mod_download',
                title_ar: 'حمل مود مجاني',
                title_en: 'Download Free Mod',
                description_ar: 'حمل مود مجاني من مجموعتنا',
                description_en: 'Download a free mod from our collection',
                target_url: 'https://modetaris.com/free-mod',
                is_required: false
            }
        ]
    };

    const template = templates[templateType];
    if (!template) return;

    // Clear existing tasks
    tasksList = [];

    // Add template tasks
    template.forEach((taskData, index) => {
        tasksList.push({
            id: Date.now().toString() + index,
            ...taskData,
            display_order: index + 1
        });
    });

    // Update UI
    renderTasksList();
    updateValidationStatus();
    calculateSmartAnalytics();

    showNotification(`تم تطبيق قالب ${templateType === 'social' ? 'وسائل التواصل' : templateType === 'apps' ? 'التطبيقات' : 'المجموعة الشاملة'} بنجاح!`, 'success');
}

function addSmartTasks() {
    // Analyze current campaign and suggest smart tasks
    const duration = parseInt(document.getElementById('subscriptionDuration').value) || 30;
    const strictness = document.getElementById('verificationStrictness').value;

    let suggestedTemplate = 'social';

    if (duration >= 90) {
        suggestedTemplate = 'complete';
    } else if (duration >= 30) {
        suggestedTemplate = 'social';
    } else {
        suggestedTemplate = 'apps';
    }

    if (confirm(`بناءً على إعدادات حملتك، نقترح استخدام قالب "${suggestedTemplate === 'social' ? 'وسائل التواصل' : suggestedTemplate === 'apps' ? 'التطبيقات' : 'المجموعة الشاملة'}". هل تريد تطبيقه؟`)) {
        applyTaskTemplate(suggestedTemplate);
    }
}

// ========================================
// Preview and Publishing
// ========================================
function updatePreview() {
    // Update campaign summary
    const titleAr = document.getElementById('campaignTitleAr').value || 'عنوان الحملة';
    const duration = document.getElementById('subscriptionDuration').value || '30';
    const verification = document.getElementById('verificationStrictness').value || 'medium';

    document.getElementById('previewTitle').textContent = titleAr;
    document.getElementById('previewDuration').textContent = `${duration} يوم`;
    document.getElementById('previewTasksCount').textContent = tasksList.length;
    document.getElementById('previewVerification').textContent =
        verification === 'low' ? 'منخفض' : verification === 'high' ? 'عالي' : 'متوسط';

    // Update live preview
    document.getElementById('previewTitleDisplay').textContent = titleAr;
    document.getElementById('previewDescDisplay').textContent =
        document.getElementById('campaignDescAr').value || 'وصف الحملة';

    // Update tasks preview
    const tasksContainer = document.getElementById('previewTasksDisplay');
    if (tasksList.length === 0) {
        tasksContainer.innerHTML = '<p style="color: #999; text-align: center;">لا توجد مهام</p>';
    } else {
        tasksContainer.innerHTML = tasksList.map(task => {
            const taskInfo = TASK_TYPES[task.type];
            return `
                <div class="preview-task">
                    <i class="${taskInfo?.icon || 'fas fa-tasks'}"></i>
                    <span>${task.title_ar}</span>
                </div>
            `;
        }).join('');
    }

    // Update final validation
    updateFinalValidation();
}

function updateFinalValidation() {
    const validationContainer = document.getElementById('finalValidation');
    const publishBtn = document.getElementById('publishBtn');

    const validations = [
        {
            key: 'basic_info',
            label: 'المعلومات الأساسية',
            valid: validationResults['campaignTitleAr'] && validationResults['campaignTitleEn'] &&
                   validationResults['campaignDescAr'] && validationResults['campaignDescEn']
        },
        {
            key: 'settings',
            label: 'إعدادات الحملة',
            valid: document.getElementById('subscriptionDuration').value > 0
        },
        {
            key: 'tasks',
            label: 'المهام المطلوبة',
            valid: tasksList.length > 0
        },
        {
            key: 'urls',
            label: 'صحة الروابط',
            valid: tasksList.every(task => {
                try {
                    new URL(task.target_url);
                    return true;
                } catch {
                    return false;
                }
            })
        }
    ];

    const allValid = validations.every(v => v.valid);

    validationContainer.innerHTML = validations.map(validation => `
        <div class="validation-item ${validation.valid ? 'valid' : 'invalid'}">
            <i class="fas fa-${validation.valid ? 'check-circle' : 'times-circle'}"></i>
            <span>${validation.label}</span>
        </div>
    `).join('');

    // Enable/disable publish button
    publishBtn.disabled = !allValid;

    if (allValid) {
        publishBtn.textContent = '🚀 نشر الحملة';
    } else {
        publishBtn.textContent = '❌ يجب إكمال جميع المتطلبات';
    }
}

function refreshPreview() {
    updatePreview();
    showNotification('تم تحديث المعاينة', 'success');
}

// ========================================
// Campaign Publishing
// ========================================
function publishCampaign() {
    try {
        showNotification('جاري إنشاء الحملة...', 'info');

        // Collect all campaign data
        const campaignData = {
            title_ar: document.getElementById('campaignTitleAr').value,
            title_en: document.getElementById('campaignTitleEn').value,
            description_ar: document.getElementById('campaignDescAr').value,
            description_en: document.getElementById('campaignDescEn').value,
            subscription_duration_days: parseInt(document.getElementById('subscriptionDuration').value),
            max_users: document.getElementById('enableUserLimit').checked ?
                      parseInt(document.getElementById('maxUsers').value) || null : null,
            end_date: document.getElementById('campaignEndDate').value ?
                     new Date(document.getElementById('campaignEndDate').value).toISOString() : null,
            verification_strictness: document.getElementById('verificationStrictness').value,
            auto_verify_enabled: document.getElementById('autoVerifyEnabled').checked,
            is_active: true
        };

        // Save campaign locally (working in offline mode)
        console.log('💾 حفظ الحملة محلياً');

        // Collect display settings
        const displaySettings = collectDisplaySettings();

        const campaignWithTasks = {
            ...campaignData,
            tasks: tasksList,
            display_settings: displaySettings,
            id: Date.now().toString(),
            created_at: new Date().toISOString()
        };

        // Save to localStorage
        const savedCampaigns = JSON.parse(localStorage.getItem('savedCampaigns') || '[]');
        savedCampaigns.push(campaignWithTasks);
        localStorage.setItem('savedCampaigns', JSON.stringify(savedCampaigns));
        localStorage.setItem('lastCreatedCampaign', JSON.stringify(campaignWithTasks));

        console.log('✅ تم حفظ الحملة محلياً بنجاح');

        // Clear saved draft
        localStorage.removeItem('smartSubscriptionDraft');

        showNotification('🎉 تم نشر الحملة بنجاح!', 'success');

        // Show success dialog
        setTimeout(() => {
            if (confirm('تم نشر الحملة بنجاح! هل تريد إنشاء حملة جديدة؟')) {
                location.reload();
            } else {
                window.close();
            }
        }, 2000);

    } catch (error) {
        console.error('خطأ في نشر الحملة:', error);
        showNotification('❌ حدث خطأ أثناء نشر الحملة: ' + error.message, 'error');
    }
}

function saveDraft() {
    autoSave();
    showNotification('تم حفظ المسودة بنجاح', 'success');
}

function exportCampaign() {
    const data = {
        campaign: collectFormData(),
        tasks: tasksList,
        metadata: {
            created_at: new Date().toISOString(),
            version: '1.0',
            creator: 'Smart Subscription Creator'
        }
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `campaign_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('تم تصدير بيانات الحملة', 'success');
}

// ========================================
// Offline Mode Functions
// ========================================
function showOfflineBanner() {
    const banner = document.getElementById('statusBanner');
    if (banner) {
        banner.style.display = 'block';
    }
}

function hideOfflineBanner() {
    const banner = document.getElementById('statusBanner');
    if (banner) {
        banner.style.display = 'none';
    }
}

function testDatabaseConnection() {
    // Working in offline mode by default
    console.log('💡 العمل في الوضع المحلي - لا حاجة لاختبار الاتصال');
    showOfflineBanner();
    return false;
}

// ========================================
// Display Customization Functions
// ========================================
function updateDisplayPreview() {
    const style = document.getElementById('displayStyle').value;
    const primaryColor = document.getElementById('primaryColor').value;
    const secondaryColor = document.getElementById('secondaryColor').value;
    const icon = document.getElementById('displayIcon').value;
    const animationType = document.getElementById('animationType').value;

    // Update popup preview
    const popupPreview = document.getElementById('popupPreview');
    const popupIcon = popupPreview.querySelector('.popup-icon');
    const popupBtn = popupPreview.querySelector('.popup-btn');

    // Update icon
    popupIcon.className = `${icon} popup-icon`;

    // Update colors
    popupPreview.style.borderColor = primaryColor;
    popupBtn.style.background = `linear-gradient(45deg, ${primaryColor}, ${secondaryColor})`;

    // Update banner preview
    const bannerPreview = document.getElementById('bannerPreview');
    const bannerIcon = bannerPreview.querySelector('i');
    bannerIcon.className = icon;
    bannerPreview.style.background = `linear-gradient(45deg, ${primaryColor}, ${secondaryColor})`;

    // Update floating preview
    const floatingPreview = document.getElementById('floatingPreview');
    const floatingIcon = floatingPreview.querySelector('i');
    floatingIcon.className = icon;
    floatingPreview.style.background = `linear-gradient(45deg, ${primaryColor}, ${secondaryColor})`;

    // Apply style-specific changes
    applyDisplayStyle(style);

    // Apply animation
    applyAnimation(animationType);

    console.log('🎨 تم تحديث معاينة العرض');
}

function applyDisplayStyle(style) {
    const popupPreview = document.getElementById('popupPreview');
    const bannerPreview = document.getElementById('bannerPreview');
    const floatingPreview = document.getElementById('floatingPreview');

    // Remove existing style classes
    [popupPreview, bannerPreview, floatingPreview].forEach(element => {
        element.classList.remove('modern', 'classic', 'neon', 'minimal', 'gaming');
    });

    // Apply new style
    switch (style) {
        case 'modern':
            popupPreview.style.background = 'linear-gradient(135deg, rgba(0,0,0,0.9), rgba(26,26,46,0.9))';
            popupPreview.style.backdropFilter = 'blur(10px)';
            break;
        case 'classic':
            popupPreview.style.background = 'rgba(0,0,0,0.95)';
            popupPreview.style.backdropFilter = 'none';
            break;
        case 'neon':
            popupPreview.style.background = 'rgba(0,0,0,0.8)';
            popupPreview.style.boxShadow = `0 0 20px ${document.getElementById('primaryColor').value}`;
            break;
        case 'minimal':
            popupPreview.style.background = 'rgba(255,255,255,0.95)';
            popupPreview.style.color = '#000';
            break;
        case 'gaming':
            popupPreview.style.background = 'linear-gradient(45deg, rgba(0,0,0,0.9), rgba(26,26,46,0.9))';
            popupPreview.style.border = '3px solid';
            popupPreview.style.borderImage = `linear-gradient(45deg, ${document.getElementById('primaryColor').value}, ${document.getElementById('secondaryColor').value}) 1`;
            break;
    }
}

function applyAnimation(animationType) {
    const popupPreview = document.getElementById('popupPreview');

    // Remove existing animation classes
    popupPreview.classList.remove('fade', 'slide', 'bounce', 'zoom', 'rotate', 'pulse', 'shake', 'glow');

    // Apply new animation
    popupPreview.classList.add(animationType);

    // Restart animation
    popupPreview.style.animation = 'none';
    setTimeout(() => {
        switch (animationType) {
            case 'fade':
                popupPreview.style.animation = 'fadeIn 1s ease-out';
                break;
            case 'slide':
                popupPreview.style.animation = 'slideDown 1s ease-out';
                break;
            case 'bounce':
                popupPreview.style.animation = 'bounce 1s ease-out';
                break;
            case 'zoom':
                popupPreview.style.animation = 'zoomIn 1s ease-out';
                break;
            case 'pulse':
                popupPreview.style.animation = 'pulse 2s infinite';
                break;
            case 'glow':
                popupPreview.style.animation = 'glow 2s infinite';
                break;
        }
    }, 10);
}

function setColor(inputId, color) {
    document.getElementById(inputId).value = color;
    updateDisplayPreview();
}

function updateDelayValue(value) {
    document.getElementById('delayValue').textContent = `${value} ثواني`;
}

function updateDurationValue(value) {
    document.getElementById('durationValue').textContent = `${value} ثانية`;
}

function previewAppDisplay() {
    // Collect display settings
    const displaySettings = collectDisplaySettings();

    // Show preview in new window or modal
    showDisplayPreviewModal(displaySettings);
}

function collectDisplaySettings() {
    return {
        locations: {
            popup: document.getElementById('showInPopup').checked,
            banner: document.getElementById('showInBanner').checked,
            menu: document.getElementById('showInMenu').checked,
            download: document.getElementById('showInDownload').checked,
            notification: document.getElementById('showInNotification').checked,
            floating: document.getElementById('showInFloating').checked
        },
        appearance: {
            style: document.getElementById('displayStyle').value,
            primaryColor: document.getElementById('primaryColor').value,
            secondaryColor: document.getElementById('secondaryColor').value,
            icon: document.getElementById('displayIcon').value
        },
        animation: {
            type: document.getElementById('animationType').value,
            speed: document.getElementById('animationSpeed').value,
            particles: document.getElementById('enableParticles').checked,
            sound: document.getElementById('enableSound').checked
        },
        timing: {
            frequency: document.getElementById('showFrequency').value,
            customFrequency: document.getElementById('customFrequency').value,
            delay: document.getElementById('showDelay').value,
            duration: document.getElementById('displayDuration').value
        }
    };
}

function showDisplayPreviewModal(settings) {
    // Create modal for full preview
    const modal = document.createElement('div');
    modal.className = 'display-preview-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;

    modal.innerHTML = `
        <div class="preview-modal-content" style="
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 90%;
            overflow-y: auto;
            border: 2px solid #ffd700;
        ">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="color: #ffd700; margin: 0;">معاينة العرض في التطبيق</h3>
                <button onclick="this.closest('.display-preview-modal').remove()" style="
                    background: #ef4444;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                ">✕</button>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                ${generatePreviewCards(settings)}
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 10px;">
                <h4 style="color: #ffd700; margin-bottom: 10px;">إعدادات العرض:</h4>
                <p style="color: #ccc; margin: 5px 0;">التكرار: ${getFrequencyText(settings.timing.frequency)}</p>
                <p style="color: #ccc; margin: 5px 0;">التأخير: ${settings.timing.delay} ثواني</p>
                <p style="color: #ccc; margin: 5px 0;">المدة: ${settings.timing.duration} ثانية</p>
                <p style="color: #ccc; margin: 5px 0;">الحركة: ${getAnimationText(settings.animation.type)}</p>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function generatePreviewCards(settings) {
    const cards = [];

    if (settings.locations.popup) {
        cards.push(`
            <div class="preview-card" style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 10px; border: 1px solid rgba(255,215,0,0.3);">
                <h5 style="color: #ffd700; margin-bottom: 10px;">نافذة منبثقة</h5>
                <div style="background: rgba(0,0,0,0.8); padding: 15px; border-radius: 8px; text-align: center; border: 2px solid ${settings.appearance.primaryColor};">
                    <i class="${settings.appearance.icon}" style="font-size: 1.5rem; color: ${settings.appearance.primaryColor}; margin-bottom: 8px;"></i>
                    <div style="color: white; font-weight: bold;">اشتراك مجاني!</div>
                    <div style="color: #ccc; font-size: 0.8rem; margin: 5px 0;">احصل على اشتراك مجاني الآن</div>
                    <button style="background: linear-gradient(45deg, ${settings.appearance.primaryColor}, ${settings.appearance.secondaryColor}); color: #000; border: none; padding: 8px 16px; border-radius: 6px; font-weight: bold;">ابدأ الآن</button>
                </div>
            </div>
        `);
    }

    if (settings.locations.banner) {
        cards.push(`
            <div class="preview-card" style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 10px; border: 1px solid rgba(255,215,0,0.3);">
                <h5 style="color: #ffd700; margin-bottom: 10px;">بانر علوي</h5>
                <div style="background: linear-gradient(45deg, ${settings.appearance.primaryColor}, ${settings.appearance.secondaryColor}); padding: 10px; border-radius: 6px; text-align: center; color: #000; font-weight: bold;">
                    <i class="${settings.appearance.icon}" style="margin-left: 8px;"></i>
                    اشتراك مجاني متاح!
                </div>
            </div>
        `);
    }

    if (settings.locations.floating) {
        cards.push(`
            <div class="preview-card" style="background: rgba(255,255,255,0.05); padding: 15px; border-radius: 10px; border: 1px solid rgba(255,215,0,0.3);">
                <h5 style="color: #ffd700; margin-bottom: 10px;">أيقونة عائمة</h5>
                <div style="position: relative; height: 100px; background: rgba(0,0,0,0.3); border-radius: 8px;">
                    <div style="position: absolute; bottom: 10px; right: 10px; width: 40px; height: 40px; background: linear-gradient(45deg, ${settings.appearance.primaryColor}, ${settings.appearance.secondaryColor}); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #000;">
                        <i class="${settings.appearance.icon}"></i>
                    </div>
                </div>
            </div>
        `);
    }

    return cards.join('');
}

function getFrequencyText(frequency) {
    const frequencies = {
        'once': 'مرة واحدة فقط',
        'daily': 'يومياً',
        'session': 'كل جلسة',
        'hourly': 'كل ساعة',
        'custom': 'مخصص'
    };
    return frequencies[frequency] || frequency;
}

function getAnimationText(animation) {
    const animations = {
        'fade': 'تلاشي تدريجي',
        'slide': 'انزلاق من الأعلى',
        'bounce': 'ارتداد',
        'zoom': 'تكبير تدريجي',
        'rotate': 'دوران',
        'pulse': 'نبضة',
        'shake': 'اهتزاز',
        'glow': 'توهج'
    };
    return animations[animation] || animation;
}

function resetDisplaySettings() {
    // Reset to default values
    document.getElementById('showInPopup').checked = true;
    document.getElementById('showInBanner').checked = false;
    document.getElementById('showInMenu').checked = false;
    document.getElementById('showInDownload').checked = false;
    document.getElementById('showInNotification').checked = false;
    document.getElementById('showInFloating').checked = false;

    document.getElementById('displayStyle').value = 'modern';
    document.getElementById('primaryColor').value = '#ffd700';
    document.getElementById('secondaryColor').value = '#ffcc00';
    document.getElementById('displayIcon').value = 'fas fa-crown';

    document.getElementById('animationType').value = 'fade';
    document.getElementById('animationSpeed').value = 'normal';
    document.getElementById('enableParticles').checked = false;
    document.getElementById('enableSound').checked = false;

    document.getElementById('showFrequency').value = 'once';
    document.getElementById('showDelay').value = 3;
    document.getElementById('displayDuration').value = 15;

    updateDelayValue(3);
    updateDurationValue(15);
    updateDisplayPreview();

    showNotification('تم إعادة تعيين إعدادات العرض للقيم الافتراضية', 'success');
}

function toggleCustomFrequency(value) {
    const customGroup = document.getElementById('customFrequencyGroup');
    if (value === 'custom') {
        customGroup.style.display = 'block';
    } else {
        customGroup.style.display = 'none';
    }
}

// Initialize display customization when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set up event listeners for display customization
    setTimeout(() => {
        if (document.getElementById('displayStyle')) {
            updateDisplayPreview();

            // Update preview when location checkboxes change
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', updateLocationPreview);
            });
        }
    }, 1000);
});

function updateLocationPreview() {
    const showBanner = document.getElementById('showInBanner').checked;
    const showFloating = document.getElementById('showInFloating').checked;

    const bannerPreview = document.getElementById('bannerPreview');
    const floatingPreview = document.getElementById('floatingPreview');

    bannerPreview.style.display = showBanner ? 'flex' : 'none';
    floatingPreview.style.display = showFloating ? 'flex' : 'none';
}

// ========================================
// Advanced Display Functions
// ========================================
function testDisplayModes() {
    const modes = ['modern', 'classic', 'neon', 'minimal', 'gaming'];
    let currentMode = 0;

    const testInterval = setInterval(() => {
        document.getElementById('displayStyle').value = modes[currentMode];
        updateDisplayPreview();

        showNotification(`اختبار النمط: ${modes[currentMode]}`, 'info');

        currentMode++;
        if (currentMode >= modes.length) {
            clearInterval(testInterval);
            showNotification('انتهى اختبار جميع الأنماط!', 'success');
        }
    }, 2000);
}

function copyDisplaySettings() {
    const settings = collectDisplaySettings();
    localStorage.setItem('copiedDisplaySettings', JSON.stringify(settings));
    showNotification('تم نسخ إعدادات العرض', 'success');
}

function pasteDisplaySettings() {
    const copiedSettings = localStorage.getItem('copiedDisplaySettings');
    if (!copiedSettings) {
        showNotification('لا توجد إعدادات منسوخة', 'error');
        return;
    }

    try {
        const settings = JSON.parse(copiedSettings);
        applyDisplaySettings(settings);
        showNotification('تم لصق إعدادات العرض', 'success');
    } catch (error) {
        showNotification('خطأ في لصق الإعدادات', 'error');
    }
}

function saveDisplayTemplate() {
    const settings = collectDisplaySettings();
    const templateName = prompt('أدخل اسم القالب:');

    if (templateName) {
        const savedTemplates = JSON.parse(localStorage.getItem('displayTemplates') || '{}');
        savedTemplates[templateName] = settings;
        localStorage.setItem('displayTemplates', JSON.stringify(savedTemplates));
        showNotification(`تم حفظ القالب: ${templateName}`, 'success');
    }
}

function applyDisplayTemplate(templateType) {
    const templates = {
        minimal: {
            style: 'minimal',
            primaryColor: '#6c757d',
            secondaryColor: '#adb5bd',
            icon: 'fas fa-circle',
            animation: 'fade',
            locations: { popup: true, banner: false, menu: false, download: false, notification: false, floating: false }
        },
        premium: {
            style: 'modern',
            primaryColor: '#ffd700',
            secondaryColor: '#ffcc00',
            icon: 'fas fa-crown',
            animation: 'zoom',
            locations: { popup: true, banner: true, menu: true, download: true, notification: false, floating: true }
        },
        gaming: {
            style: 'gaming',
            primaryColor: '#ff6b6b',
            secondaryColor: '#4ecdc4',
            icon: 'fas fa-gamepad',
            animation: 'bounce',
            locations: { popup: true, banner: true, menu: false, download: true, notification: true, floating: true }
        },
        professional: {
            style: 'classic',
            primaryColor: '#2c3e50',
            secondaryColor: '#34495e',
            icon: 'fas fa-briefcase',
            animation: 'slide',
            locations: { popup: true, banner: true, menu: true, download: false, notification: false, floating: false }
        },
        colorful: {
            style: 'neon',
            primaryColor: '#ff6b6b',
            secondaryColor: '#4ecdc4',
            icon: 'fas fa-rainbow',
            animation: 'pulse',
            locations: { popup: true, banner: true, menu: true, download: true, notification: true, floating: true }
        },
        dark: {
            style: 'minimal',
            primaryColor: '#1a1a1a',
            secondaryColor: '#2d2d2d',
            icon: 'fas fa-moon',
            animation: 'fade',
            locations: { popup: true, banner: false, menu: true, download: false, notification: false, floating: false }
        }
    };

    const template = templates[templateType];
    if (template) {
        applyDisplaySettings({
            appearance: template,
            locations: template.locations,
            animation: { type: template.animation, speed: 'normal', particles: false, sound: false },
            timing: { frequency: 'once', delay: 3, duration: 15 }
        });

        showNotification(`تم تطبيق قالب: ${templateType}`, 'success');
    }
}

function applyDisplaySettings(settings) {
    // Apply appearance settings
    if (settings.appearance) {
        document.getElementById('displayStyle').value = settings.appearance.style || 'modern';
        document.getElementById('primaryColor').value = settings.appearance.primaryColor || '#ffd700';
        document.getElementById('secondaryColor').value = settings.appearance.secondaryColor || '#ffcc00';
        document.getElementById('displayIcon').value = settings.appearance.icon || 'fas fa-crown';
    }

    // Apply location settings
    if (settings.locations) {
        document.getElementById('showInPopup').checked = settings.locations.popup || false;
        document.getElementById('showInBanner').checked = settings.locations.banner || false;
        document.getElementById('showInMenu').checked = settings.locations.menu || false;
        document.getElementById('showInDownload').checked = settings.locations.download || false;
        document.getElementById('showInNotification').checked = settings.locations.notification || false;
        document.getElementById('showInFloating').checked = settings.locations.floating || false;
    }

    // Apply animation settings
    if (settings.animation) {
        document.getElementById('animationType').value = settings.animation.type || 'fade';
        document.getElementById('animationSpeed').value = settings.animation.speed || 'normal';
        document.getElementById('enableParticles').checked = settings.animation.particles || false;
        document.getElementById('enableSound').checked = settings.animation.sound || false;
    }

    // Apply timing settings
    if (settings.timing) {
        document.getElementById('showFrequency').value = settings.timing.frequency || 'once';
        document.getElementById('showDelay').value = settings.timing.delay || 3;
        document.getElementById('displayDuration').value = settings.timing.duration || 15;
        updateDelayValue(settings.timing.delay || 3);
        updateDurationValue(settings.timing.duration || 15);
    }

    // Update preview
    updateDisplayPreview();
    updateLocationPreview();
}

function updateBlurValue(value) {
    document.getElementById('blurValue').textContent = `${value}px`;
}

function updateOpacityValue(value) {
    document.getElementById('opacityValue').textContent = `${value}%`;
}

// Handle click action change
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        const clickActionSelect = document.getElementById('clickAction');
        if (clickActionSelect) {
            clickActionSelect.addEventListener('change', function() {
                const redirectGroup = document.getElementById('redirectUrlGroup');
                if (this.value === 'redirect_page') {
                    redirectGroup.style.display = 'block';
                } else {
                    redirectGroup.style.display = 'none';
                }
            });
        }
    }, 1000);
});

// ========================================
// Header Functions
// ========================================
function openDisplayCustomizationGuide() {
    window.open('test-display-customization.html', '_blank');
    showNotification('تم فتح دليل تخصيص العرض', 'info');
}

function previewAllDisplayModes() {
    // Create comprehensive preview modal
    const modal = document.createElement('div');
    modal.className = 'comprehensive-preview-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        overflow-y: auto;
    `;

    modal.innerHTML = `
        <div class="comprehensive-preview-content" style="
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 20px;
            padding: 30px;
            max-width: 95%;
            width: 1200px;
            max-height: 90%;
            overflow-y: auto;
            border: 2px solid #ffd700;
        ">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h2 style="color: #ffd700; margin: 0;">معاينة شاملة لجميع أنماط العرض</h2>
                <button onclick="this.closest('.comprehensive-preview-modal').remove()" style="
                    background: #ef4444;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 1.1rem;
                ">✕</button>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                ${generateAllDisplayPreviews()}
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <button onclick="this.closest('.comprehensive-preview-modal').remove()" style="
                    background: linear-gradient(45deg, #ffd700, #ffcc00);
                    color: #000;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 10px;
                    cursor: pointer;
                    font-weight: bold;
                    font-size: 1.1rem;
                ">إغلاق المعاينة</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function generateAllDisplayPreviews() {
    const templates = [
        { name: 'بسيط', type: 'minimal', color: '#6c757d', icon: 'fas fa-circle' },
        { name: 'مميز', type: 'premium', color: '#ffd700', icon: 'fas fa-crown' },
        { name: 'ألعاب', type: 'gaming', color: '#ff6b6b', icon: 'fas fa-gamepad' },
        { name: 'احترافي', type: 'professional', color: '#2c3e50', icon: 'fas fa-briefcase' },
        { name: 'ملون', type: 'colorful', color: '#ff6b6b', icon: 'fas fa-rainbow' },
        { name: 'داكن', type: 'dark', color: '#1a1a1a', icon: 'fas fa-moon' }
    ];

    return templates.map(template => `
        <div style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 15px; border: 1px solid rgba(255,215,0,0.3);">
            <h4 style="color: #ffd700; margin-bottom: 15px; text-align: center;">${template.name}</h4>
            <div style="background: rgba(0,0,0,0.8); padding: 20px; border-radius: 10px; text-align: center; border: 2px solid ${template.color};">
                <i class="${template.icon}" style="font-size: 2rem; color: ${template.color}; margin-bottom: 10px;"></i>
                <div style="color: white; font-weight: bold; margin-bottom: 8px;">اشتراك مجاني!</div>
                <div style="color: #ccc; font-size: 0.9rem; margin-bottom: 15px;">احصل على اشتراك مجاني الآن</div>
                <button style="background: linear-gradient(45deg, ${template.color}, ${template.color}dd); color: ${template.type === 'minimal' ? '#000' : '#fff'}; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer;" onclick="applyDisplayTemplate('${template.type}'); this.closest('.comprehensive-preview-modal').remove();">تطبيق هذا النمط</button>
            </div>
        </div>
    `).join('');
}

function loadDisplayTemplate() {
    const savedTemplates = JSON.parse(localStorage.getItem('displayTemplates') || '{}');
    const templateNames = Object.keys(savedTemplates);

    if (templateNames.length === 0) {
        showNotification('لا توجد قوالب عرض محفوظة', 'info');
        return;
    }

    const templateName = prompt(`اختر قالب العرض:\n${templateNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}\n\nأدخل اسم القالب:`);

    if (templateName && savedTemplates[templateName]) {
        applyDisplaySettings(savedTemplates[templateName]);
        showNotification(`تم تحميل قالب العرض: ${templateName}`, 'success');
    } else if (templateName) {
        showNotification('القالب غير موجود', 'error');
    }
}

function exportFullCampaign() {
    const campaignData = collectFormData();
    const displaySettings = collectDisplaySettings();
    const tasks = tasksList;

    const fullExport = {
        campaign: campaignData,
        display_settings: displaySettings,
        tasks: tasks,
        metadata: {
            created_at: new Date().toISOString(),
            version: '2.0',
            creator: 'Smart Subscription Creator',
            features: [
                'Basic Campaign Info',
                'Advanced Settings',
                'Task Management',
                'Display Customization',
                'Templates Support'
            ]
        }
    };

    const blob = new Blob([JSON.stringify(fullExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `full_campaign_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('تم تصدير الحملة الشاملة مع إعدادات العرض', 'success');
}
