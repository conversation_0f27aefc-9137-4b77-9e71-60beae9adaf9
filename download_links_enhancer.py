#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن روابط التحميل لتطبيق Modetaris
Download Links Enhancer for Modetaris App
"""

import re
import requests
from urllib.parse import urlparse, quote, unquote
from typing import Dict, List, Optional, Tuple

class DownloadLinksEnhancer:
    """فئة لتحسين روابط التحميل لتكون متوافقة مع التطبيقات"""
    
    def __init__(self):
        self.test_timeout = 10
        
    def enhance_firebase_url(self, original_url: str, filename: str = None) -> Dict[str, str]:
        """تحسين رابط Firebase لجعله متوافق مع التطبيقات"""
        try:
            enhanced_formats = {}
            
            if "firebasestorage.googleapis.com" in original_url:
                # استخراج معلومات الرابط
                if "/v0/b/" in original_url and "/o/" in original_url:
                    parts = original_url.split("/v0/b/")[1].split("/o/")
                    bucket_name = parts[0]
                    encoded_path = parts[1].split("?")[0]
                    file_path = unquote(encoded_path)
                    
                    # تنسيق 1: رابط مباشر (الأفضل للتطبيقات)
                    enhanced_formats['direct'] = f"https://storage.googleapis.com/{bucket_name}/{file_path}"
                    
                    # تنسيق 2: رابط مباشر مع اسم الملف
                    if filename:
                        enhanced_formats['direct_with_filename'] = f"https://storage.googleapis.com/{bucket_name}/{file_path}?filename={quote(filename)}"
                    
                    # تنسيق 3: رابط Firebase بدون تشفير
                    enhanced_formats['firebase_clean'] = f"https://firebasestorage.googleapis.com/v0/b/{bucket_name}/o/{file_path}?alt=media"
                    
                    # تنسيق 4: الرابط الأصلي (للمتصفحات)
                    enhanced_formats['original'] = original_url
                    
            else:
                enhanced_formats['original'] = original_url
            
            return enhanced_formats
            
        except Exception as e:
            print(f"❌ خطأ في تحسين رابط Firebase: {e}")
            return {'original': original_url}
    
    def test_url_compatibility(self, url: str) -> Tuple[bool, Dict[str, any]]:
        """اختبار توافق الرابط مع التطبيقات"""
        try:
            # اختبار الاتصال
            response = requests.head(url, timeout=self.test_timeout, allow_redirects=True)
            
            # فحص خصائص التوافق
            compatibility_checks = {
                'http_status_ok': response.status_code == 200,
                'is_https': url.startswith('https://'),
                'has_file_extension': self._has_file_extension(url),
                'no_query_params': '?' not in url or self._has_simple_params(url),
                'no_url_encoding': '%' not in url,
                'direct_link': not ('alt=media' in url or '/v0/b/' in url),
                'content_length_available': 'content-length' in response.headers,
                'proper_content_type': self._has_proper_content_type(response.headers.get('content-type', ''))
            }
            
            # حساب نقاط التوافق
            compatibility_score = sum(1 for check in compatibility_checks.values() if check)
            total_checks = len(compatibility_checks)
            
            result = {
                'status_code': response.status_code,
                'content_length': response.headers.get('content-length', 'Unknown'),
                'content_type': response.headers.get('content-type', 'Unknown'),
                'compatibility_checks': compatibility_checks,
                'compatibility_score': compatibility_score,
                'total_checks': total_checks,
                'compatibility_percentage': (compatibility_score / total_checks) * 100
            }
            
            return response.status_code == 200, result
            
        except Exception as e:
            return False, {'error': str(e)}
    
    def _has_file_extension(self, url: str) -> bool:
        """فحص وجود امتداد ملف في الرابط"""
        path = urlparse(url).path
        return any(path.lower().endswith(ext) for ext in ['.mcpack', '.mcaddon', '.zip', '.rar'])
    
    def _has_simple_params(self, url: str) -> bool:
        """فحص ما إذا كانت معاملات الرابط بسيطة"""
        query = urlparse(url).query
        if not query:
            return True
        # السماح فقط بمعامل filename
        return query.startswith('filename=')
    
    def _has_proper_content_type(self, content_type: str) -> bool:
        """فحص نوع المحتوى المناسب"""
        proper_types = [
            'application/octet-stream',
            'application/zip',
            'application/mcpack',
            'application/mcaddon'
        ]
        return any(ptype in content_type.lower() for ptype in proper_types)
    
    def find_best_url(self, url_formats: Dict[str, str]) -> Tuple[str, str]:
        """العثور على أفضل رابط من التنسيقات المتاحة"""
        best_url = None
        best_format = None
        best_score = -1
        
        print(f"🔍 اختبار {len(url_formats)} تنسيق للرابط...")
        
        for format_name, url in url_formats.items():
            print(f"\n📋 اختبار تنسيق: {format_name}")
            
            success, result = self.test_url_compatibility(url)
            
            if success:
                score = result.get('compatibility_score', 0)
                percentage = result.get('compatibility_percentage', 0)
                
                print(f"✅ نجح الاختبار - نقاط التوافق: {score}/{result.get('total_checks', 0)} ({percentage:.1f}%)")
                
                if score > best_score:
                    best_score = score
                    best_url = url
                    best_format = format_name
            else:
                print(f"❌ فشل الاختبار: {result.get('error', 'Unknown error')}")
        
        if best_url:
            print(f"\n🏆 أفضل تنسيق: {best_format} (نقاط: {best_score})")
        else:
            print(f"\n⚠️ لم يتم العثور على تنسيق مناسب، استخدام الرابط الأصلي")
            best_url = list(url_formats.values())[0] if url_formats else None
            best_format = "fallback"
        
        return best_url, best_format
    
    def enhance_download_url(self, original_url: str, filename: str = None) -> str:
        """تحسين رابط التحميل الرئيسي"""
        print(f"🔧 تحسين رابط التحميل...")
        print(f"الرابط الأصلي: {original_url}")
        
        # إنشاء تنسيقات مختلفة
        formats = self.enhance_firebase_url(original_url, filename)
        
        # العثور على أفضل تنسيق
        best_url, best_format = self.find_best_url(formats)
        
        if best_url:
            print(f"✅ تم تحسين الرابط بنجاح")
            print(f"الرابط المحسن: {best_url}")
            return best_url
        else:
            print(f"⚠️ فشل في تحسين الرابط، استخدام الرابط الأصلي")
            return original_url

# إنشاء مثيل عام
download_enhancer = DownloadLinksEnhancer()

def enhance_url_for_app(url: str, filename: str = None) -> str:
    """دالة مساعدة لتحسين الرابط"""
    return download_enhancer.enhance_download_url(url, filename)

def test_url_for_app(url: str) -> bool:
    """دالة مساعدة لاختبار الرابط"""
    success, _ = download_enhancer.test_url_compatibility(url)
    return success

if __name__ == "__main__":
    # اختبار سريع
    test_url = "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2Ftest_mod_20250803_133629_1754230485_lt62ygox.mcpack?alt=media"
    
    enhancer = DownloadLinksEnhancer()
    enhanced_url = enhancer.enhance_download_url(test_url, "test_mod.mcpack")
    
    print(f"\n📊 النتيجة النهائية:")
    print(f"الرابط الأصلي: {test_url}")
    print(f"الرابط المحسن: {enhanced_url}")
