-- ========================================
-- نظام التحميل الاحتياطي الذكي
-- Smart Download Fallback System Tables
-- ========================================

-- جدول تتبع أخطاء التحميل
-- Download Errors Tracking Table
CREATE TABLE IF NOT EXISTS download_errors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    mod_name TEXT NOT NULL,
    original_url TEXT NOT NULL,
    backup_url TEXT,
    error_type TEXT NOT NULL CHECK (error_type IN (
        'original_failed_backup_success',
        'complete_failure',
        'timeout',
        'network_error',
        'file_corrupted',
        'url_not_found',
        'permission_denied'
    )),
    error_message TEXT,
    user_agent TEXT,
    user_ip INET,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    resolved_by TEXT,
    resolution_notes TEXT,
    reported_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- إحصائيات
    retry_count INTEGER DEFAULT 0,
    last_retry_at TIMESTAMPTZ,
    
    -- معلومات إضافية
    device_info JSONB DEFAULT '{}'::jsonb,
    browser_info JSONB DEFAULT '{}'::jsonb
);

-- جدول روابط التحميل الاحتياطية
-- Backup Download URLs Table
CREATE TABLE IF NOT EXISTS mod_backup_urls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    backup_url TEXT NOT NULL,
    backup_type TEXT DEFAULT 'firebase' CHECK (backup_type IN ('firebase', 'external', 'mirror')),
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 10),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by TEXT,
    
    -- معلومات الملف
    file_size BIGINT,
    file_hash TEXT,
    last_verified_at TIMESTAMPTZ,
    verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'failed')),
    
    -- إحصائيات الاستخدام
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    
    UNIQUE(mod_id, backup_url)
);

-- جدول إحصائيات التحميل
-- Download Statistics Table
CREATE TABLE IF NOT EXISTS download_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    date DATE DEFAULT CURRENT_DATE,
    
    -- إحصائيات التحميل
    total_attempts INTEGER DEFAULT 0,
    successful_downloads INTEGER DEFAULT 0,
    failed_downloads INTEGER DEFAULT 0,
    backup_downloads INTEGER DEFAULT 0,
    
    -- أنواع الأخطاء
    timeout_errors INTEGER DEFAULT 0,
    network_errors INTEGER DEFAULT 0,
    file_errors INTEGER DEFAULT 0,
    url_errors INTEGER DEFAULT 0,
    
    -- معدلات النجاح
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    backup_usage_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- أوقات الاستجابة
    avg_download_time DECIMAL(8,2) DEFAULT 0.00,
    max_download_time DECIMAL(8,2) DEFAULT 0.00,
    min_download_time DECIMAL(8,2) DEFAULT 0.00,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(mod_id, date)
);

-- جدول تنبيهات الأدمن
-- Admin Notifications Table
CREATE TABLE IF NOT EXISTS admin_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    type TEXT NOT NULL CHECK (type IN (
        'download_error',
        'backup_needed',
        'url_broken',
        'high_failure_rate',
        'system_alert'
    )),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- البيانات المرتبطة
    related_mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    related_error_id UUID REFERENCES download_errors(id) ON DELETE CASCADE,
    data JSONB DEFAULT '{}'::jsonb,
    
    -- حالة التنبيه
    is_read BOOLEAN DEFAULT false,
    is_resolved BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    resolved_by TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days')
);

-- جدول سجل إصلاح الروابط
-- URL Fix History Table
CREATE TABLE IF NOT EXISTS url_fix_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mod_id UUID REFERENCES mods(id) ON DELETE CASCADE,
    error_id UUID REFERENCES download_errors(id) ON DELETE CASCADE,
    
    -- الروابط
    old_url TEXT NOT NULL,
    new_url TEXT NOT NULL,
    backup_url TEXT,
    
    -- معلومات الإصلاح
    fix_type TEXT NOT NULL CHECK (fix_type IN (
        'url_update',
        'backup_promotion',
        'new_backup_added',
        'url_redirect_fix'
    )),
    fix_description TEXT,
    
    -- من قام بالإصلاح
    fixed_by TEXT NOT NULL,
    fix_method TEXT DEFAULT 'manual' CHECK (fix_method IN ('manual', 'automatic', 'bulk')),
    
    -- التحقق من الإصلاح
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMPTZ,
    verification_result TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ========================================
-- الفهارس لتحسين الأداء
-- Indexes for Performance
-- ========================================

-- فهارس جدول أخطاء التحميل
CREATE INDEX IF NOT EXISTS idx_download_errors_mod_id ON download_errors(mod_id);
CREATE INDEX IF NOT EXISTS idx_download_errors_timestamp ON download_errors(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_download_errors_is_resolved ON download_errors(is_resolved);
CREATE INDEX IF NOT EXISTS idx_download_errors_error_type ON download_errors(error_type);

-- فهارس جدول الروابط الاحتياطية
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_mod_id ON mod_backup_urls(mod_id);
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_active ON mod_backup_urls(is_active);
CREATE INDEX IF NOT EXISTS idx_mod_backup_urls_priority ON mod_backup_urls(priority);

-- فهارس جدول الإحصائيات
CREATE INDEX IF NOT EXISTS idx_download_statistics_mod_id ON download_statistics(mod_id);
CREATE INDEX IF NOT EXISTS idx_download_statistics_date ON download_statistics(date DESC);

-- فهارس جدول التنبيهات
CREATE INDEX IF NOT EXISTS idx_admin_notifications_type ON admin_notifications(type);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_is_read ON admin_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_severity ON admin_notifications(severity);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_created_at ON admin_notifications(created_at DESC);

-- ========================================
-- الدوال المساعدة
-- Helper Functions
-- ========================================

-- دالة تحديث إحصائيات التحميل
CREATE OR REPLACE FUNCTION update_download_statistics(
    p_mod_id UUID,
    p_success BOOLEAN,
    p_used_backup BOOLEAN DEFAULT false,
    p_error_type TEXT DEFAULT NULL,
    p_download_time DECIMAL DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO download_statistics (
        mod_id, date, total_attempts, successful_downloads, failed_downloads, backup_downloads
    ) VALUES (
        p_mod_id, CURRENT_DATE, 1, 
        CASE WHEN p_success THEN 1 ELSE 0 END,
        CASE WHEN NOT p_success THEN 1 ELSE 0 END,
        CASE WHEN p_used_backup THEN 1 ELSE 0 END
    )
    ON CONFLICT (mod_id, date) DO UPDATE SET
        total_attempts = download_statistics.total_attempts + 1,
        successful_downloads = download_statistics.successful_downloads + 
            CASE WHEN p_success THEN 1 ELSE 0 END,
        failed_downloads = download_statistics.failed_downloads + 
            CASE WHEN NOT p_success THEN 1 ELSE 0 END,
        backup_downloads = download_statistics.backup_downloads + 
            CASE WHEN p_used_backup THEN 1 ELSE 0 END,
        success_rate = ROUND(
            (download_statistics.successful_downloads + CASE WHEN p_success THEN 1 ELSE 0 END) * 100.0 / 
            (download_statistics.total_attempts + 1), 2
        ),
        backup_usage_rate = ROUND(
            (download_statistics.backup_downloads + CASE WHEN p_used_backup THEN 1 ELSE 0 END) * 100.0 / 
            (download_statistics.total_attempts + 1), 2
        ),
        updated_at = NOW();
        
    -- تحديث إحصائيات الأخطاء
    IF NOT p_success AND p_error_type IS NOT NULL THEN
        CASE p_error_type
            WHEN 'timeout' THEN
                UPDATE download_statistics 
                SET timeout_errors = timeout_errors + 1 
                WHERE mod_id = p_mod_id AND date = CURRENT_DATE;
            WHEN 'network_error' THEN
                UPDATE download_statistics 
                SET network_errors = network_errors + 1 
                WHERE mod_id = p_mod_id AND date = CURRENT_DATE;
            WHEN 'file_corrupted' THEN
                UPDATE download_statistics 
                SET file_errors = file_errors + 1 
                WHERE mod_id = p_mod_id AND date = CURRENT_DATE;
            WHEN 'url_not_found' THEN
                UPDATE download_statistics 
                SET url_errors = url_errors + 1 
                WHERE mod_id = p_mod_id AND date = CURRENT_DATE;
        END CASE;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- دالة إنشاء تنبيه للأدمن
CREATE OR REPLACE FUNCTION create_admin_notification(
    p_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_severity TEXT DEFAULT 'medium',
    p_mod_id UUID DEFAULT NULL,
    p_error_id UUID DEFAULT NULL,
    p_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO admin_notifications (
        type, title, message, severity, related_mod_id, related_error_id, data
    ) VALUES (
        p_type, p_title, p_message, p_severity, p_mod_id, p_error_id, p_data
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_download_data()
RETURNS VOID AS $$
BEGIN
    -- حذف أخطاء التحميل المحلولة القديمة (أكثر من 30 يوم)
    DELETE FROM download_errors 
    WHERE is_resolved = true 
    AND resolved_at < NOW() - INTERVAL '30 days';
    
    -- حذف الإحصائيات القديمة (أكثر من 90 يوم)
    DELETE FROM download_statistics 
    WHERE date < CURRENT_DATE - INTERVAL '90 days';
    
    -- حذف التنبيهات المنتهية الصلاحية
    DELETE FROM admin_notifications 
    WHERE expires_at < NOW();
    
    -- حذف سجل الإصلاحات القديمة (أكثر من 60 يوم)
    DELETE FROM url_fix_history 
    WHERE created_at < NOW() - INTERVAL '60 days';
    
    RAISE NOTICE 'تم تنظيف البيانات القديمة لنظام التحميل الاحتياطي';
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- المشغلات (Triggers)
-- ========================================

-- مشغل لتحديث timestamp عند التعديل
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المشغل على الجداول المناسبة
CREATE TRIGGER update_mod_backup_urls_updated_at
    BEFORE UPDATE ON mod_backup_urls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_statistics_updated_at
    BEFORE UPDATE ON download_statistics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- منح الصلاحيات
-- Grant Permissions
-- ========================================

-- منح صلاحيات للمستخدمين المجهولين والمسجلين
GRANT SELECT, INSERT, UPDATE ON download_errors TO anon, authenticated;
GRANT SELECT ON mod_backup_urls TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON download_statistics TO anon, authenticated;
GRANT SELECT, INSERT ON admin_notifications TO anon, authenticated;
GRANT SELECT ON url_fix_history TO anon, authenticated;

-- منح صلاحيات تنفيذ الدوال
GRANT EXECUTE ON FUNCTION update_download_statistics(UUID, BOOLEAN, BOOLEAN, TEXT, DECIMAL) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_admin_notification(TEXT, TEXT, TEXT, TEXT, UUID, UUID, JSONB) TO anon, authenticated;

-- ========================================
-- بيانات تجريبية (اختيارية)
-- Sample Data (Optional)
-- ========================================

-- إدراج بعض أنواع الأخطاء الشائعة للاختبار
-- INSERT INTO download_errors (mod_name, original_url, error_type, error_message) VALUES
-- ('Test Mod 1', 'https://example.com/mod1.mcpack', 'url_not_found', 'URL returns 404'),
-- ('Test Mod 2', 'https://example.com/mod2.mcpack', 'timeout', 'Download timeout after 30 seconds');

COMMENT ON TABLE download_errors IS 'جدول تتبع أخطاء تحميل المودات';
COMMENT ON TABLE mod_backup_urls IS 'جدول روابط التحميل الاحتياطية للمودات';
COMMENT ON TABLE download_statistics IS 'جدول إحصائيات التحميل اليومية';
COMMENT ON TABLE admin_notifications IS 'جدول تنبيهات الإدارة';
COMMENT ON TABLE url_fix_history IS 'جدول سجل إصلاح روابط التحميل';
