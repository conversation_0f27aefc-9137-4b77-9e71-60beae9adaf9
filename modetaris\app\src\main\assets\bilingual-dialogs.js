// ========================================
// نظام المربعات المخصصة ثنائية اللغة
// Bilingual Custom Dialogs System
// ========================================

class BilingualDialogManager {
    constructor() {
        this.userLanguage = this.getUserLanguage();
        this.supabaseClient = null;
        this.shownDialogs = new Set(); // لتتبع المربعات التي تم عرضها

        // مراقبة تغييرات اللغة
        this.setupLanguageChangeListener();
    }

    // الحصول على لغة المستخدم
    getUserLanguage() {
        // استخدام نفس مفتاح localStorage المستخدم في نظام اللغة الموجود
        const savedLanguage = localStorage.getItem('selectedLanguage');
        if (savedLanguage) {
            console.log(`🌍 User language from localStorage: ${savedLanguage}`);
            return savedLanguage;
        }

        // التحقق من TranslationManager إذا كان متوفراً
        if (window.translationManager) {
            const currentLang = window.translationManager.getCurrentLanguage();
            console.log(`🌍 User language from TranslationManager: ${currentLang}`);
            return currentLang;
        }

        // محاولة الحصول على اللغة من قاعدة البيانات
        const userId = this.getUserId();
        if (userId) {
            this.fetchUserLanguageFromDB(userId);
        }

        // افتراضي: الإنجليزية (متوافق مع النظام الموجود)
        console.log('🌍 Using default language: en');
        return 'en';
    }

    // إعداد مراقب تغييرات اللغة
    setupLanguageChangeListener() {
        // مراقبة الحدث المخصص من TranslationManager
        window.addEventListener('languageChanged', (event) => {
            const newLanguage = event.detail.language;
            console.log(`🔄 Language changed to: ${newLanguage}`);
            this.userLanguage = newLanguage;

            // إعادة تعيين المربعات المعروضة لإظهارها باللغة الجديدة
            this.resetShownDialogs();
        });

        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (event) => {
            if (event.key === 'selectedLanguage') {
                const newLanguage = event.newValue;
                console.log(`🔄 Language changed in localStorage to: ${newLanguage}`);
                this.userLanguage = newLanguage || 'en';
                this.resetShownDialogs();
            }
        });
    }

    // إعادة تعيين المربعات المعروضة
    resetShownDialogs() {
        this.shownDialogs.clear();
        console.log('🔄 Reset shown dialogs for new language');
    }

    // الحصول على معرف المستخدم
    getUserId() {
        // يمكن تخصيص هذه الدالة حسب نظام المصادقة المستخدم
        return localStorage.getItem('user_id') || 'anonymous_' + Date.now();
    }

    // جلب لغة المستخدم من قاعدة البيانات
    async fetchUserLanguageFromDB(userId) {
        try {
            if (!this.supabaseClient) {
                this.supabaseClient = window.supabaseManager?.getMainClient();
            }

            if (!this.supabaseClient) {
                console.warn('Supabase client not available');
                return;
            }

            const { data, error } = await this.supabaseClient
                .from('user_languages')
                .select('selected_language')
                .eq('user_id', userId)
                .single();

            if (data && !error) {
                this.userLanguage = data.selected_language;
                // استخدام نفس مفتاح localStorage المستخدم في النظام الموجود
                localStorage.setItem('selectedLanguage', this.userLanguage);
                console.log(`🌍 Updated user language from DB: ${this.userLanguage}`);
            }
        } catch (error) {
            console.warn('Could not fetch user language:', error);
        }
    }

    // الحصول على المربعات المرتبطة بمود معين
    async getModDialogs(modId) {
        try {
            if (!this.supabaseClient) {
                this.supabaseClient = window.supabaseManager?.getMainClient();
            }

            if (!this.supabaseClient) {
                console.warn('Supabase client not available');
                return [];
            }

            // جلب المربعات المرتبطة بالمود
            const { data: dialogMods, error: dialogModsError } = await this.supabaseClient
                .from('custom_dialog_mods')
                .select(`
                    dialog_id,
                    custom_mod_dialogs (
                        id,
                        title,
                        title_en,
                        description,
                        description_en,
                        image_url,
                        button_text,
                        button_text_en,
                        show_dont_show_again,
                        is_active
                    )
                `)
                .eq('mod_id', modId);

            if (dialogModsError) {
                console.error('Error fetching mod dialogs:', dialogModsError);
                return [];
            }

            // تصفية المربعات النشطة وتحويلها للصيغة المناسبة
            return dialogMods
                .filter(dm => dm.custom_mod_dialogs && dm.custom_mod_dialogs.is_active)
                .map(dm => this.formatDialogForLanguage(dm.custom_mod_dialogs));

        } catch (error) {
            console.error('Error in getModDialogs:', error);
            return [];
        }
    }

    // تنسيق المربع حسب اللغة
    formatDialogForLanguage(dialog) {
        const isArabic = this.userLanguage === 'ar';

        return {
            id: dialog.id,
            title: isArabic
                ? (dialog.title || dialog.title_en || 'عنوان غير محدد')
                : (dialog.title_en || dialog.title || 'No title'),
            description: isArabic
                ? (dialog.description || dialog.description_en || '')
                : (dialog.description_en || dialog.description || ''),
            imageUrl: dialog.image_url,
            buttonText: isArabic
                ? (dialog.button_text || dialog.button_text_en || 'تم')
                : (dialog.button_text_en || dialog.button_text || 'OK'),
            showDontShowAgain: dialog.show_dont_show_again,
            language: this.userLanguage
        };
    }

    // عرض مربع حوار
    showDialog(dialogData) {
        return new Promise((resolve) => {
            // التحقق من عدم عرض المربع مسبقاً
            const dialogKey = `dialog_${dialogData.id}_${this.getUserId()}`;

            if (this.shownDialogs.has(dialogKey)) {
                resolve({ action: 'already_shown' });
                return;
            }

            // التحقق من إعداد "عدم الإظهار مرة أخرى"
            if (dialogData.showDontShowAgain) {
                const dontShowKey = `dont_show_dialog_${dialogData.id}_${this.getUserId()}`;
                if (localStorage.getItem(dontShowKey) === 'true') {
                    resolve({ action: 'dont_show_again' });
                    return;
                }
            }

            // إنشاء المربع
            const modal = this.createDialogModal(dialogData);
            document.body.appendChild(modal);

            // إضافة المربع للمربعات المعروضة
            this.shownDialogs.add(dialogKey);

            // معالجة الأحداث
            const handleClose = (action) => {
                if (action === 'dont_show_again' && dialogData.showDontShowAgain) {
                    const dontShowKey = `dont_show_dialog_${dialogData.id}_${this.getUserId()}`;
                    localStorage.setItem(dontShowKey, 'true');
                }

                modal.remove();
                resolve({ action });
            };

            // أزرار المربع
            const okButton = modal.querySelector('.dialog-ok-button');
            const dontShowButton = modal.querySelector('.dialog-dont-show-button');
            const closeButton = modal.querySelector('.dialog-close-button');

            okButton?.addEventListener('click', () => handleClose('ok'));
            dontShowButton?.addEventListener('click', () => handleClose('dont_show_again'));
            closeButton?.addEventListener('click', () => handleClose('close'));

            // إغلاق عند النقر خارج المربع
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    handleClose('outside_click');
                }
            });
        });
    }

    // إنشاء مربع الحوار
    createDialogModal(dialogData) {
        const modal = document.createElement('div');
        modal.className = 'custom-dialog-modal';
        modal.setAttribute('dir', dialogData.language === 'ar' ? 'rtl' : 'ltr');

        modal.innerHTML = `
            <div class="custom-dialog-content">
                <button class="dialog-close-button" aria-label="إغلاق">×</button>

                ${dialogData.imageUrl ? `
                    <div class="dialog-image-container">
                        <img src="${dialogData.imageUrl}" alt="صورة المربع" class="dialog-image">
                    </div>
                ` : ''}

                <div class="dialog-text-content">
                    <h3 class="dialog-title">${dialogData.title}</h3>
                    ${dialogData.description ? `
                        <p class="dialog-description">${dialogData.description}</p>
                    ` : ''}
                </div>

                <div class="dialog-buttons">
                    <button class="dialog-ok-button">${dialogData.buttonText}</button>
                    ${dialogData.showDontShowAgain ? `
                        <button class="dialog-dont-show-button">
                            ${dialogData.language === 'ar' ? 'عدم الإظهار مرة أخرى' : "Don't show again"}
                        </button>
                    ` : ''}
                </div>
            </div>
        `;

        // إضافة الأنماط
        this.addDialogStyles();

        return modal;
    }

    // إضافة أنماط CSS للمربع
    addDialogStyles() {
        if (document.getElementById('custom-dialog-styles')) {
            return; // الأنماط موجودة مسبقاً
        }

        const styles = document.createElement('style');
        styles.id = 'custom-dialog-styles';
        styles.textContent = `
            .custom-dialog-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }

            .custom-dialog-content {
                background: white;
                border-radius: 12px;
                max-width: 400px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }

            .dialog-close-button {
                position: absolute;
                top: 10px;
                right: 10px;
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.2s;
            }

            .dialog-close-button:hover {
                background: #f0f0f0;
            }

            .dialog-image-container {
                width: 100%;
                text-align: center;
                padding: 20px 20px 0;
            }

            .dialog-image {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
            }

            .dialog-text-content {
                padding: 20px;
            }

            .dialog-title {
                margin: 0 0 15px 0;
                color: #2c3e50;
                font-size: 1.3em;
                text-align: center;
            }

            .dialog-description {
                margin: 0;
                color: #555;
                line-height: 1.6;
                text-align: center;
            }

            .dialog-buttons {
                padding: 0 20px 20px;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .dialog-ok-button, .dialog-dont-show-button {
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.2s;
            }

            .dialog-ok-button {
                background: linear-gradient(45deg, #3498db, #2980b9);
                color: white;
            }

            .dialog-ok-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
            }

            .dialog-dont-show-button {
                background: #ecf0f1;
                color: #7f8c8d;
                font-size: 14px;
            }

            .dialog-dont-show-button:hover {
                background: #d5dbdb;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideIn {
                from { transform: translateY(-50px) scale(0.9); opacity: 0; }
                to { transform: translateY(0) scale(1); opacity: 1; }
            }

            /* تنسيق RTL */
            .custom-dialog-modal[dir="rtl"] .dialog-close-button {
                right: auto;
                left: 10px;
            }
        `;

        document.head.appendChild(styles);
    }

    // عرض جميع المربعات المرتبطة بمود
    async showModDialogs(modId) {
        console.log(`🎯 Showing dialogs for mod: ${modId} in language: ${this.userLanguage}`);

        const dialogs = await this.getModDialogs(modId);
        console.log(`📋 Found ${dialogs.length} dialogs for mod ${modId}`);

        for (const dialog of dialogs) {
            console.log(`📱 Showing dialog: ${dialog.title} (${dialog.language})`);
            const result = await this.showDialog(dialog);
            console.log(`✅ Dialog ${dialog.id} result:`, result);

            // إذا اختار المستخدم "عدم الإظهار مرة أخرى"، توقف عن عرض المربعات
            if (result.action === 'dont_show_again') {
                console.log('🛑 User chose "don\'t show again", stopping dialog sequence');
                break;
            }
        }
    }

    // دالة اختبار لعرض مربع تجريبي
    async showTestDialog(language = null) {
        const testLanguage = language || this.userLanguage;
        console.log(`🧪 Showing test dialog in language: ${testLanguage}`);

        const testDialog = {
            id: 'test_dialog',
            title: testLanguage === 'ar' ? 'مربع تجريبي' : 'Test Dialog',
            description: testLanguage === 'ar'
                ? 'هذا مربع تجريبي لاختبار النظام ثنائي اللغة'
                : 'This is a test dialog to verify the bilingual system',
            imageUrl: null,
            buttonText: testLanguage === 'ar' ? 'تم' : 'OK',
            showDontShowAgain: true,
            language: testLanguage
        };

        return await this.showDialog(testDialog);
    }
}

// إنشاء مثيل عام
window.bilingualDialogManager = new BilingualDialogManager();

// دوال مساعدة للاستخدام السهل
window.showModDialogs = function(modId) {
    return window.bilingualDialogManager.showModDialogs(modId);
};

// دالة اختبار المربعات
window.testBilingualDialog = function(language = null) {
    return window.bilingualDialogManager.showTestDialog(language);
};

// دالة للحصول على اللغة الحالية
window.getCurrentDialogLanguage = function() {
    return window.bilingualDialogManager.userLanguage;
};

// دالة لإعادة تعيين المربعات المعروضة
window.resetShownDialogs = function() {
    window.bilingualDialogManager.resetShownDialogs();
};

// دالة لتغيير لغة المربعات يدوياً
window.setDialogLanguage = function(language) {
    window.bilingualDialogManager.userLanguage = language;
    window.bilingualDialogManager.resetShownDialogs();
    console.log(`🌍 Dialog language manually set to: ${language}`);
};

console.log('✅ Bilingual Dialog Manager loaded successfully');
console.log(`🌍 Current dialog language: ${window.bilingualDialogManager.userLanguage}`);
