<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل الإصلاحات - Mod Etaris</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .fix-section {
            margin-bottom: 25px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }

        .fix-header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .fix-content {
            padding: 20px;
            background: #f9f9f9;
        }

        .run-button {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .run-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .run-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-running {
            background: #cce5ff;
            color: #004085;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            border: 2px solid #333;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 10px;
        }

        .main-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .main-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .back-link {
            display: inline-block;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 20px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .summary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ مشغل الإصلاحات</h1>
            <p>تشغيل جميع ملفات الإصلاحات المطلوبة لحل مشاكل التطبيق</p>
        </div>

        <div class="content">
            <div class="controls">
                <button class="main-button" onclick="runAllFixes()">🚀 تشغيل جميع الإصلاحات</button>
                <button class="main-button" onclick="runQuickFix()">⚡ إصلاح سريع</button>
                <button class="main-button" onclick="clearLogs()">🧹 مسح السجلات</button>
                <a href="index.html" class="back-link">← العودة للتطبيق</a>
                <a href="firebase-fix-page.html" class="back-link">🔥 إصلاح Firebase</a>
                <a href="undo-fixes.html" class="back-link" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">↩️ التراجع عن الإصلاحات</a>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>

            <!-- Fix 1: No Database Fix -->
            <div class="fix-section">
                <div class="fix-header">
                    🔄 إصلاح بدون قاعدة البيانات
                    <span class="status status-pending" id="status-1">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>يحل المشاكل عبر تعديل الاستعلامات والبيانات المحلية فقط</p>
                    <button class="run-button" onclick="runFix('no-database-fix')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 2: Emergency Fix -->
            <div class="fix-section">
                <div class="fix-header">
                    🚨 الإصلاح الطارئ
                    <span class="status status-pending" id="status-2">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>إصلاح طارئ للمشاكل المستمرة مع محاولة إصلاح قاعدة البيانات</p>
                    <button class="run-button" onclick="runFix('emergency-fix')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 3: Final Fix Executor -->
            <div class="fix-section">
                <div class="fix-header">
                    🎯 المنفذ النهائي للإصلاحات
                    <span class="status status-pending" id="status-3">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>تشغيل جميع الإصلاحات بالترتيب الصحيح مع 8 خطوات شاملة</p>
                    <button class="run-button" onclick="runFix('final-fix-executor')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 4: SQL Fixes -->
            <div class="fix-section">
                <div class="fix-header">
                    🗄️ إصلاحات SQL
                    <span class="status status-pending" id="status-4">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>تشغيل إصلاحات قاعدة البيانات وإنشاء الجداول المفقودة</p>
                    <button class="run-button" onclick="runFix('run-sql-fixes')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 5: Critical Fixes -->
            <div class="fix-section">
                <div class="fix-header">
                    🚀 الإصلاحات الحرجة
                    <span class="status status-pending" id="status-5">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>إصلاحات فورية للمشاكل الأساسية مثل Firebase و Supabase</p>
                    <button class="run-button" onclick="runFix('critical-fixes')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 6: Auto Fix Runner -->
            <div class="fix-section">
                <div class="fix-header">
                    🤖 التشغيل التلقائي
                    <span class="status status-pending" id="status-6">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>تشغيل تلقائي لجميع الإصلاحات مع تقارير مفصلة</p>
                    <button class="run-button" onclick="runFix('auto-fix-runner')">تشغيل</button>
                </div>
            </div>

            <!-- Fix 7: Additional Fixes -->
            <div class="fix-section">
                <div class="fix-header">
                    🔧 الإصلاحات الإضافية
                    <span class="status status-pending" id="status-7">في الانتظار</span>
                </div>
                <div class="fix-content">
                    <p>إصلاحات للأخطاء الجديدة: Canvas Security و Firebase Firestore</p>
                    <button class="run-button" onclick="runFix('additional-fixes')">تشغيل</button>
                </div>
            </div>

            <!-- Log Container -->
            <div class="log-container" id="logContainer">
                <div>🔧 مشغل الإصلاحات جاهز...</div>
                <div>📋 اختر الإصلاحات المطلوبة أو اضغط "تشغيل جميع الإصلاحات"</div>
            </div>

            <!-- Summary -->
            <div class="summary" id="summary" style="display: none;">
                <h2>📊 ملخص الإصلاحات</h2>
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-number" id="totalFixes">0</span>
                        إجمالي الإصلاحات
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="successFixes">0</span>
                        نجحت
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="failedFixes">0</span>
                        فشلت
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="executionTime">0</span>
                        ثانية
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load all fix scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-manager.js"></script>
    <script src="additional-fixes.js"></script>
    <script src="no-database-fix.js"></script>
    <script src="emergency-fix.js"></script>
    <script src="final-fix-executor.js"></script>
    <script src="admin/run-sql-fixes.js"></script>
    <script src="critical-fixes.js"></script>
    <script src="auto-fix-runner.js"></script>
    <script src="database-error-fixes.js"></script>
    <script src="quick-fixes.js"></script>

    <script>
        // Fix Runner Controller
        class FixRunner {
            constructor() {
                this.fixes = {
                    'no-database-fix': { name: 'إصلاح بدون قاعدة البيانات', status: 'pending' },
                    'emergency-fix': { name: 'الإصلاح الطارئ', status: 'pending' },
                    'final-fix-executor': { name: 'المنفذ النهائي', status: 'pending' },
                    'run-sql-fixes': { name: 'إصلاحات SQL', status: 'pending' },
                    'critical-fixes': { name: 'الإصلاحات الحرجة', status: 'pending' },
                    'auto-fix-runner': { name: 'التشغيل التلقائي', status: 'pending' },
                    'additional-fixes': { name: 'الإصلاحات الإضافية', status: 'pending' }
                };
                this.startTime = null;
                this.completedFixes = 0;
                this.totalFixes = Object.keys(this.fixes).length;
            }

            log(message) {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span style="color: #888;">[${timestamp}]</span> ${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            updateStatus(fixId, status) {
                const statusElement = document.getElementById(`status-${Object.keys(this.fixes).indexOf(fixId) + 1}`);
                if (statusElement) {
                    statusElement.className = `status status-${status}`;
                    statusElement.textContent = this.getStatusText(status);
                }
                this.fixes[fixId].status = status;
            }

            getStatusText(status) {
                const statusTexts = {
                    'pending': 'في الانتظار',
                    'running': 'قيد التشغيل',
                    'success': 'نجح',
                    'error': 'فشل'
                };
                return statusTexts[status] || status;
            }

            updateProgress() {
                const progress = (this.completedFixes / this.totalFixes) * 100;
                document.getElementById('progressBar').style.width = `${progress}%`;
            }

            async runFix(fixId) {
                this.log(`🔧 بدء تشغيل: ${this.fixes[fixId].name}`);
                this.updateStatus(fixId, 'running');

                try {
                    let result = false;

                    switch (fixId) {
                        case 'no-database-fix':
                            if (window.noDatabaseFix) {
                                await window.noDatabaseFix.init();
                                result = true;
                            }
                            break;

                        case 'emergency-fix':
                            if (window.emergencyFix) {
                                result = await window.emergencyFix.init();
                            }
                            break;

                        case 'final-fix-executor':
                            if (window.finalFixExecutor) {
                                await window.finalFixExecutor.executeAllFixes();
                                result = true;
                            }
                            break;

                        case 'run-sql-fixes':
                            if (window.sqlFixesRunner) {
                                result = await window.sqlFixesRunner.runAllSQLFixes();
                            }
                            break;

                        case 'critical-fixes':
                            if (window.criticalFixes) {
                                await window.criticalFixes.initializeCriticalFixes();
                                result = true;
                            }
                            break;

                        case 'auto-fix-runner':
                            if (window.autoFixRunner) {
                                await window.autoFixRunner.runAllFixes();
                                result = true;
                            }
                            break;

                        case 'additional-fixes':
                            if (window.additionalFixes) {
                                await window.additionalFixes.init();
                                result = true;
                            }
                            break;
                    }

                    if (result) {
                        this.updateStatus(fixId, 'success');
                        this.log(`✅ نجح: ${this.fixes[fixId].name}`);
                    } else {
                        this.updateStatus(fixId, 'error');
                        this.log(`❌ فشل: ${this.fixes[fixId].name} - الوحدة غير متاحة`);
                    }

                } catch (error) {
                    this.updateStatus(fixId, 'error');
                    this.log(`❌ خطأ في ${this.fixes[fixId].name}: ${error.message}`);
                }

                this.completedFixes++;
                this.updateProgress();
            }

            async runAllFixes() {
                this.log('🚀 بدء تشغيل جميع الإصلاحات...');
                this.startTime = Date.now();
                this.completedFixes = 0;

                for (const fixId of Object.keys(this.fixes)) {
                    await this.runFix(fixId);
                    await new Promise(resolve => setTimeout(resolve, 500)); // تأخير قصير بين الإصلاحات
                }

                this.showSummary();
                this.log('🎉 تم الانتهاء من جميع الإصلاحات!');
            }

            async runQuickFix() {
                this.log('⚡ بدء الإصلاح السريع...');
                this.startTime = Date.now();
                this.completedFixes = 0;

                // تشغيل الإصلاحات الأساسية فقط
                const quickFixes = ['additional-fixes', 'no-database-fix', 'critical-fixes'];

                for (const fixId of quickFixes) {
                    await this.runFix(fixId);
                }

                this.showSummary();
                this.log('⚡ تم الانتهاء من الإصلاح السريع!');
            }

            showSummary() {
                const executionTime = ((Date.now() - this.startTime) / 1000).toFixed(1);
                const successCount = Object.values(this.fixes).filter(f => f.status === 'success').length;
                const failedCount = Object.values(this.fixes).filter(f => f.status === 'error').length;

                document.getElementById('totalFixes').textContent = this.completedFixes;
                document.getElementById('successFixes').textContent = successCount;
                document.getElementById('failedFixes').textContent = failedCount;
                document.getElementById('executionTime').textContent = executionTime;

                document.getElementById('summary').style.display = 'block';
            }

            clearLogs() {
                document.getElementById('logContainer').innerHTML = `
                    <div>🔧 مشغل الإصلاحات جاهز...</div>
                    <div>📋 اختر الإصلاحات المطلوبة أو اضغط "تشغيل جميع الإصلاحات"</div>
                `;
                document.getElementById('summary').style.display = 'none';
                document.getElementById('progressBar').style.width = '0%';
                
                // إعادة تعيين حالة الإصلاحات
                for (let i = 1; i <= this.totalFixes; i++) {
                    const statusElement = document.getElementById(`status-${i}`);
                    if (statusElement) {
                        statusElement.className = 'status status-pending';
                        statusElement.textContent = 'في الانتظار';
                    }
                }
                
                this.completedFixes = 0;
                this.log('🧹 تم مسح السجلات');
            }
        }

        // إنشاء مثيل من مشغل الإصلاحات
        const fixRunner = new FixRunner();

        // الدوال العامة
        function runAllFixes() {
            fixRunner.runAllFixes();
        }

        function runQuickFix() {
            fixRunner.runQuickFix();
        }

        function runFix(fixId) {
            fixRunner.runFix(fixId);
        }

        function clearLogs() {
            fixRunner.clearLogs();
        }

        // تسجيل رسالة الترحيب
        fixRunner.log('🎯 مشغل الإصلاحات محمل وجاهز للاستخدام');
        fixRunner.log('💡 نصيحة: ابدأ بـ "إصلاح سريع" للحصول على نتائج فورية');
    </script>
</body>
</html>
