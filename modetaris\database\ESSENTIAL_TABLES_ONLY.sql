-- ========================================
-- الجداول الأساسية المطلوبة فقط
-- Essential Tables Only - Quick Setup
-- ========================================

-- 1. جدول أنواع المهام (بسيط)
CREATE TABLE IF NOT EXISTS task_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name_ar VARCHAR(200) NOT NULL,
    display_name_en VARCHAR(200) NOT NULL,
    icon VARCHAR(100),
    verification_method VARCHAR(50) DEFAULT 'smart',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إدراج أنواع المهام الأساسية
INSERT INTO task_types (name, display_name_ar, display_name_en, icon, verification_method) VALUES
('youtube_subscribe', 'اشتراك في قناة يوتيوب', 'Subscribe to YouTube Channel', 'fab fa-youtube', 'smart'),
('telegram_subscribe', 'اشتراك في قناة تيليجرام', 'Subscribe to Telegram Channel', 'fab fa-telegram', 'smart'),
('discord_join', 'انضمام لخادم ديسكورد', 'Join Discord Server', 'fab fa-discord', 'smart')
ON CONFLICT (name) DO NOTHING;

-- 2. جدول حملات الاشتراك المجاني
CREATE TABLE IF NOT EXISTS free_subscription_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT NOT NULL,
    description_en TEXT NOT NULL,
    banner_image_url TEXT DEFAULT '',
    popup_image_url TEXT,
    subscription_duration_days INTEGER NOT NULL DEFAULT 30,
    max_users INTEGER DEFAULT NULL,
    current_users INTEGER DEFAULT 0,
    verification_strictness VARCHAR(20) DEFAULT 'medium',
    auto_verify_enabled BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. جدول مهام الحملة
CREATE TABLE IF NOT EXISTS campaign_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id) ON DELETE CASCADE,
    task_type VARCHAR(100) NOT NULL REFERENCES task_types(name),
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    target_url TEXT NOT NULL,
    target_id VARCHAR(200),
    verification_method VARCHAR(50) DEFAULT 'smart',
    verification_config JSONB DEFAULT '{}',
    retry_attempts INTEGER DEFAULT 3,
    verification_delay_seconds INTEGER DEFAULT 30,
    display_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. جدول اشتراكات المستخدمين
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id),
    status VARCHAR(20) DEFAULT 'pending',
    verification_score INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, campaign_id)
);

-- 5. جدول تقدم المستخدم في المهام
CREATE TABLE IF NOT EXISTS user_task_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    campaign_id UUID NOT NULL REFERENCES free_subscription_campaigns(id),
    task_id UUID NOT NULL REFERENCES campaign_tasks(id),
    status VARCHAR(20) DEFAULT 'pending',
    verification_attempts INTEGER DEFAULT 0,
    verification_score INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_data JSONB DEFAULT '{}',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, task_id)
);

-- 6. جدول سجلات التحقق
CREATE TABLE IF NOT EXISTS verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100) NOT NULL,
    task_id UUID NOT NULL REFERENCES campaign_tasks(id),
    verification_type VARCHAR(50) NOT NULL,
    verification_method VARCHAR(50) NOT NULL,
    success BOOLEAN NOT NULL,
    response_data JSONB DEFAULT '{}',
    error_message TEXT,
    verification_time_ms INTEGER,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 7. الفهارس الأساسية
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_user_id ON user_task_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_task_progress_status ON user_task_progress(status);
CREATE INDEX IF NOT EXISTS idx_campaign_tasks_campaign_id ON campaign_tasks(campaign_id);
CREATE INDEX IF NOT EXISTS idx_verification_logs_user_id ON verification_logs(user_id);

-- 8. دالة التحديث التلقائي
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. إنشاء triggers للتحديث التلقائي
DROP TRIGGER IF EXISTS update_free_subscription_campaigns_updated_at ON free_subscription_campaigns;
CREATE TRIGGER update_free_subscription_campaigns_updated_at
    BEFORE UPDATE ON free_subscription_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_subscriptions_updated_at ON user_subscriptions;
CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_task_progress_updated_at ON user_task_progress;
CREATE TRIGGER update_user_task_progress_updated_at
    BEFORE UPDATE ON user_task_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. دوال التحقق الأساسية
CREATE OR REPLACE FUNCTION smart_verify_task(
    p_user_id VARCHAR(100),
    p_task_id UUID
)
RETURNS JSONB AS $$
DECLARE
    task_record RECORD;
    verification_result JSONB;
    verification_score INTEGER;
BEGIN
    -- الحصول على تفاصيل المهمة
    SELECT ct.*, tt.verification_method as default_verification_method
    INTO task_record
    FROM campaign_tasks ct
    JOIN task_types tt ON ct.task_type = tt.name
    WHERE ct.id = p_task_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Task not found'
        );
    END IF;
    
    -- محاكاة التحقق (نجح دائماً للاختبار)
    verification_score := 85 + (RANDOM() * 15)::INTEGER;
    
    verification_result := jsonb_build_object(
        'success', true,
        'platform', task_record.task_type,
        'verification_score', verification_score,
        'verified_at', CURRENT_TIMESTAMP,
        'method', 'smart_check'
    );
    
    -- تحديث حالة المهمة
    INSERT INTO user_task_progress (user_id, task_id, campaign_id, status, verification_score, verified_at, verification_data)
    VALUES (p_user_id, p_task_id, task_record.campaign_id, 'verified', verification_score, CURRENT_TIMESTAMP, verification_result)
    ON CONFLICT (user_id, task_id) 
    DO UPDATE SET 
        status = 'verified',
        verification_score = verification_score,
        verified_at = CURRENT_TIMESTAMP,
        verification_data = verification_result,
        updated_at = CURRENT_TIMESTAMP;
    
    -- تسجيل في السجل
    INSERT INTO verification_logs (user_id, task_id, verification_type, verification_method, success, response_data)
    VALUES (p_user_id, p_task_id, task_record.task_type, 'smart', true, verification_result);
    
    RETURN verification_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql;

-- 11. دالة تفعيل الاشتراك
CREATE OR REPLACE FUNCTION activate_free_subscription(
    p_user_id VARCHAR(100),
    p_campaign_id UUID
)
RETURNS JSONB AS $$
DECLARE
    campaign_record RECORD;
    required_tasks_count INTEGER;
    completed_tasks_count INTEGER;
    avg_verification_score NUMERIC;
BEGIN
    -- الحصول على تفاصيل الحملة
    SELECT * INTO campaign_record
    FROM free_subscription_campaigns
    WHERE id = p_campaign_id AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Campaign not found or inactive'
        );
    END IF;
    
    -- عد المهام المطلوبة والمكتملة
    SELECT COUNT(*) INTO required_tasks_count
    FROM campaign_tasks
    WHERE campaign_id = p_campaign_id AND is_required = true;
    
    SELECT 
        COUNT(*) as completed_count,
        AVG(verification_score) as avg_score
    INTO completed_tasks_count, avg_verification_score
    FROM user_task_progress utp
    JOIN campaign_tasks ct ON utp.task_id = ct.id
    WHERE utp.user_id = p_user_id 
      AND ct.campaign_id = p_campaign_id 
      AND ct.is_required = true
      AND utp.status = 'verified';
    
    -- التحقق من إكمال جميع المهام
    IF completed_tasks_count < required_tasks_count THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Not all required tasks completed',
            'completed_tasks', completed_tasks_count,
            'required_tasks', required_tasks_count
        );
    END IF;
    
    -- تفعيل الاشتراك
    INSERT INTO user_subscriptions (
        user_id, 
        campaign_id, 
        status, 
        verification_score,
        started_at, 
        expires_at
    ) VALUES (
        p_user_id,
        p_campaign_id,
        'active',
        COALESCE(avg_verification_score, 100)::integer,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP + INTERVAL '1 day' * campaign_record.subscription_duration_days
    )
    ON CONFLICT (user_id, campaign_id) 
    DO UPDATE SET
        status = 'active',
        verification_score = COALESCE(avg_verification_score, 100)::integer,
        started_at = CURRENT_TIMESTAMP,
        expires_at = CURRENT_TIMESTAMP + INTERVAL '1 day' * campaign_record.subscription_duration_days,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN jsonb_build_object(
        'success', true,
        'expires_at', CURRENT_TIMESTAMP + INTERVAL '1 day' * campaign_record.subscription_duration_days,
        'verification_score', COALESCE(avg_verification_score, 100)::integer,
        'duration_days', campaign_record.subscription_duration_days
    );
END;
$$ LANGUAGE plpgsql;

-- 12. منح الصلاحيات
GRANT EXECUTE ON FUNCTION smart_verify_task(VARCHAR, UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION activate_free_subscription(VARCHAR, UUID) TO anon, authenticated;

-- 13. إنشاء حملة تجريبية
DO $$
DECLARE
    campaign_uuid UUID;
BEGIN
    -- إنشاء حملة تجريبية
    INSERT INTO free_subscription_campaigns (
        title_ar, title_en, description_ar, description_en,
        subscription_duration_days, max_users
    ) VALUES (
        'اشتراك مجاني تجريبي',
        'Test Free Subscription',
        'حملة تجريبية للاختبار',
        'Test campaign for testing',
        1, 100
    ) RETURNING id INTO campaign_uuid;

    -- إضافة مهمة يوتيوب
    INSERT INTO campaign_tasks (
        campaign_id, task_type, title_ar, title_en,
        description_ar, description_en,
        target_url, target_id, display_order
    ) VALUES (
        campaign_uuid, 'youtube_subscribe',
        'اشترك في قناة يوتيوب', 'Subscribe to YouTube Channel',
        'اشترك في قناتنا على يوتيوب', 'Subscribe to our YouTube channel',
        'https://youtube.com/@modetaris', 'UCxxxxxxxxxxxxxxxxxxxxx', 1
    );

    RAISE NOTICE 'تم إنشاء حملة تجريبية بنجاح! Campaign ID: %', campaign_uuid;
END $$;

-- رسالة النجاح
SELECT 'تم إنشاء الجداول الأساسية بنجاح! ✅' as status,
       'يمكنك الآن استخدام صفحة إنشاء الحملات' as message;
