# -*- coding: utf-8 -*-
"""
نظام تحسين الصور المتقدم
يستخدم OpenCV وخوارزميات متقدمة لتحسين جودة الصور
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from io import BytesIO
import os

class AdvancedImageEnhancer:
    """فئة تحسين الصور المتقدمة"""
    
    def __init__(self):
        self.enhancement_methods = {
            'auto_enhance': self.auto_enhance,
            'super_resolution': self.super_resolution,
            'denoise': self.denoise_image,
            'sharpen': self.sharpen_image,
            'contrast_enhance': self.enhance_contrast,
            'color_enhance': self.enhance_colors,
            'upscale_2x': self.upscale_2x,
            'upscale_4x': self.upscale_4x
        }
    
    def enhance_image(self, image_bytes: bytes, enhancement_type: str = 'auto_enhance', 
                     original_content_type: str = 'image/jpeg') -> bytes:
        """
        تحسين الصورة باستخدام النوع المحدد
        
        Args:
            image_bytes: بيانات الصورة
            enhancement_type: نوع التحسين
            original_content_type: نوع المحتوى الأصلي
            
        Returns:
            بيانات الصورة المحسنة
        """
        try:
            # تحويل البيانات إلى صورة PIL
            pil_image = Image.open(BytesIO(image_bytes))
            original_format = pil_image.format or 'JPEG'
            
            # تحويل إلى OpenCV format
            cv_image = self._pil_to_cv2(pil_image)
            
            # تطبيق التحسين المطلوب
            if enhancement_type in self.enhancement_methods:
                enhanced_cv = self.enhancement_methods[enhancement_type](cv_image)
            else:
                enhanced_cv = self.auto_enhance(cv_image)
            
            # تحويل العودة إلى PIL
            enhanced_pil = self._cv2_to_pil(enhanced_cv)
            
            # حفظ الصورة المحسنة
            output_buffer = BytesIO()
            
            # تحديد جودة الحفظ حسب التنسيق
            if original_format.upper() in ['JPEG', 'JPG']:
                enhanced_pil.save(output_buffer, format='JPEG', quality=95, optimize=True)
            elif original_format.upper() == 'PNG':
                enhanced_pil.save(output_buffer, format='PNG', optimize=True)
            elif original_format.upper() == 'WEBP':
                enhanced_pil.save(output_buffer, format='WEBP', quality=95)
            else:
                enhanced_pil.save(output_buffer, format='JPEG', quality=95, optimize=True)
            
            return output_buffer.getvalue()
            
        except Exception as e:
            print(f"❌ خطأ في تحسين الصورة: {e}")
            return image_bytes
    
    def auto_enhance(self, cv_image):
        """تحسين تلقائي شامل للصورة"""
        # تحسين التباين التكيفي
        enhanced = self._adaptive_histogram_equalization(cv_image)
        
        # تقليل الضوضاء
        enhanced = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # تحسين الحدة
        enhanced = self._unsharp_mask(enhanced)
        
        # تحسين الألوان
        enhanced = self._enhance_colors_cv(enhanced)
        
        return enhanced
    
    def super_resolution(self, cv_image):
        """تحسين الدقة الفائقة"""
        # تكبير الصورة باستخدام EDSR
        height, width = cv_image.shape[:2]
        
        # تكبير بمعامل 2 باستخدام INTER_CUBIC
        upscaled = cv2.resize(cv_image, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)
        
        # تطبيق تحسين الحدة
        upscaled = self._unsharp_mask(upscaled, strength=1.5)
        
        # تقليل الضوضاء الناتجة عن التكبير
        upscaled = cv2.bilateralFilter(upscaled, 5, 50, 50)
        
        return upscaled
    
    def denoise_image(self, cv_image):
        """إزالة الضوضاء من الصورة"""
        if len(cv_image.shape) == 3:
            # صورة ملونة
            denoised = cv2.fastNlMeansDenoisingColored(cv_image, None, 10, 10, 7, 21)
        else:
            # صورة رمادية
            denoised = cv2.fastNlMeansDenoising(cv_image, None, 10, 7, 21)
        
        return denoised
    
    def sharpen_image(self, cv_image):
        """تحسين حدة الصورة"""
        return self._unsharp_mask(cv_image, strength=1.2)
    
    def enhance_contrast(self, cv_image):
        """تحسين التباين"""
        return self._adaptive_histogram_equalization(cv_image)
    
    def enhance_colors(self, cv_image):
        """تحسين الألوان"""
        return self._enhance_colors_cv(cv_image)
    
    def upscale_2x(self, cv_image):
        """تكبير الصورة بمعامل 2"""
        height, width = cv_image.shape[:2]
        upscaled = cv2.resize(cv_image, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)
        return self._unsharp_mask(upscaled)
    
    def upscale_4x(self, cv_image):
        """تكبير الصورة بمعامل 4"""
        height, width = cv_image.shape[:2]
        upscaled = cv2.resize(cv_image, (width * 4, height * 4), interpolation=cv2.INTER_CUBIC)
        return self._unsharp_mask(upscaled, strength=0.8)
    
    def _pil_to_cv2(self, pil_image):
        """تحويل صورة PIL إلى OpenCV"""
        # تحويل إلى RGB إذا لزم الأمر
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # تحويل إلى numpy array
        cv_image = np.array(pil_image)
        
        # تحويل من RGB إلى BGR (OpenCV format)
        cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)
        
        return cv_image
    
    def _cv2_to_pil(self, cv_image):
        """تحويل صورة OpenCV إلى PIL"""
        # تحويل من BGR إلى RGB
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        
        # تحويل إلى PIL
        pil_image = Image.fromarray(rgb_image)
        
        return pil_image
    
    def _adaptive_histogram_equalization(self, cv_image):
        """تحسين التباين التكيفي"""
        if len(cv_image.shape) == 3:
            # تحويل إلى LAB color space
            lab = cv2.cvtColor(cv_image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            # تطبيق CLAHE على قناة L
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            
            # دمج القنوات
            enhanced = cv2.merge([l, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        else:
            # صورة رمادية
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(cv_image)
        
        return enhanced
    
    def _unsharp_mask(self, cv_image, strength=1.0):
        """تطبيق Unsharp Mask لتحسين الحدة"""
        # إنشاء نسخة مشوشة
        blurred = cv2.GaussianBlur(cv_image, (0, 0), 2.0)
        
        # حساب الفرق
        unsharp_mask = cv2.addWeighted(cv_image, 1.0 + strength, blurred, -strength, 0)
        
        return unsharp_mask
    
    def _enhance_colors_cv(self, cv_image):
        """تحسين الألوان باستخدام OpenCV"""
        if len(cv_image.shape) == 3:
            # تحويل إلى HSV
            hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
            h, s, v = cv2.split(hsv)
            
            # زيادة التشبع بنسبة 10%
            s = cv2.multiply(s, 1.1)
            s = np.clip(s, 0, 255).astype(np.uint8)
            
            # دمج القنوات
            enhanced_hsv = cv2.merge([h, s, v])
            enhanced = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
        else:
            enhanced = cv_image
        
        return enhanced

# إنشاء مثيل عام للاستخدام
advanced_enhancer = AdvancedImageEnhancer()

def enhance_image_advanced(image_bytes: bytes, enhancement_type: str = 'auto_enhance', 
                          original_content_type: str = 'image/jpeg') -> bytes:
    """
    دالة مساعدة لتحسين الصور
    
    Args:
        image_bytes: بيانات الصورة
        enhancement_type: نوع التحسين
        original_content_type: نوع المحتوى الأصلي
        
    Returns:
        بيانات الصورة المحسنة
    """
    return advanced_enhancer.enhance_image(image_bytes, enhancement_type, original_content_type)

def get_available_enhancements():
    """الحصول على قائمة بأنواع التحسين المتاحة"""
    return {
        'auto_enhance': 'تحسين تلقائي شامل',
        'super_resolution': 'دقة فائقة (2x)',
        'denoise': 'إزالة الضوضاء',
        'sharpen': 'تحسين الحدة',
        'contrast_enhance': 'تحسين التباين',
        'color_enhance': 'تحسين الألوان',
        'upscale_2x': 'تكبير 2x',
        'upscale_4x': 'تكبير 4x'
    }
