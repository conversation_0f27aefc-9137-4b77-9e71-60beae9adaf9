-- ========================================
-- إنشاء جدول إعدادات المودات الجديدة
-- Create New Mods Settings Table
-- ========================================

-- إنشاء جدول إعدادات المودات الجديدة
CREATE TABLE IF NOT EXISTS new_mods_settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    duration INTEGER NOT NULL DEFAULT 7,
    cache_minutes INTEGER NOT NULL DEFAULT 3,
    home_page_limit INTEGER NOT NULL DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- قيود للتأكد من صحة البيانات
    CONSTRAINT check_duration CHECK (duration >= 1 AND duration <= 365),
    CONSTRAINT check_cache_minutes CHECK (cache_minutes >= 1 AND cache_minutes <= 60),
    CONSTRAINT check_home_page_limit CHECK (home_page_limit >= 1 AND home_page_limit <= 50),
    CONSTRAINT single_row CHECK (id = 1)
);

-- إدراج القيم الافتراضية
INSERT INTO new_mods_settings (id, duration, cache_minutes, home_page_limit)
VALUES (1, 7, 3, 10)
ON CONFLICT (id) DO NOTHING;

-- إنشاء دالة لإنشاء الجدول (للاستخدام من JavaScript)
CREATE OR REPLACE FUNCTION create_new_mods_settings_table()
RETURNS VOID AS $$
BEGIN
    -- إنشاء الجدول إذا لم يكن موجوداً
    CREATE TABLE IF NOT EXISTS new_mods_settings (
        id INTEGER PRIMARY KEY DEFAULT 1,
        duration INTEGER NOT NULL DEFAULT 7,
        cache_minutes INTEGER NOT NULL DEFAULT 3,
        home_page_limit INTEGER NOT NULL DEFAULT 10,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        CONSTRAINT check_duration CHECK (duration >= 1 AND duration <= 365),
        CONSTRAINT check_cache_minutes CHECK (cache_minutes >= 1 AND cache_minutes <= 60),
        CONSTRAINT check_home_page_limit CHECK (home_page_limit >= 1 AND home_page_limit <= 50),
        CONSTRAINT single_row CHECK (id = 1)
    );
    
    -- إدراج القيم الافتراضية إذا لم تكن موجودة
    INSERT INTO new_mods_settings (id, duration, cache_minutes, home_page_limit)
    VALUES (1, 7, 3, 10)
    ON CONFLICT (id) DO NOTHING;
    
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة للحصول على إعدادات المودات الجديدة
CREATE OR REPLACE FUNCTION get_new_mods_settings()
RETURNS TABLE(
    duration INTEGER,
    cache_minutes INTEGER,
    home_page_limit INTEGER,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- التأكد من وجود الجدول والبيانات
    PERFORM create_new_mods_settings_table();
    
    -- إرجاع الإعدادات
    RETURN QUERY
    SELECT 
        s.duration,
        s.cache_minutes,
        s.home_page_limit,
        s.updated_at
    FROM new_mods_settings s
    WHERE s.id = 1;
    
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتحديث إعدادات المودات الجديدة
CREATE OR REPLACE FUNCTION update_new_mods_settings(
    new_duration INTEGER DEFAULT NULL,
    new_cache_minutes INTEGER DEFAULT NULL,
    new_home_page_limit INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    update_count INTEGER;
BEGIN
    -- التأكد من وجود الجدول
    PERFORM create_new_mods_settings_table();
    
    -- تحديث الإعدادات (فقط القيم المرسلة)
    UPDATE new_mods_settings 
    SET 
        duration = COALESCE(new_duration, duration),
        cache_minutes = COALESCE(new_cache_minutes, cache_minutes),
        home_page_limit = COALESCE(new_home_page_limit, home_page_limit),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = 1;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    
    -- إرجاع true إذا تم التحديث بنجاح
    RETURN update_count > 0;
    
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة للحصول على المودات الجديدة بناءً على الإعدادات
CREATE OR REPLACE FUNCTION get_new_mods_with_settings(limit_count INTEGER DEFAULT NULL)
RETURNS TABLE(
    id UUID,
    title TEXT,
    description TEXT,
    description_ar TEXT,
    image_urls TEXT[],
    category TEXT,
    downloads INTEGER,
    likes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    settings_duration INTEGER;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- الحصول على مدة العرض من الإعدادات
    SELECT duration INTO settings_duration
    FROM new_mods_settings
    WHERE id = 1;
    
    -- استخدام القيمة الافتراضية إذا لم توجد إعدادات
    IF settings_duration IS NULL THEN
        settings_duration := 7;
    END IF;
    
    -- حساب تاريخ القطع
    cutoff_date := CURRENT_TIMESTAMP - (settings_duration || ' days')::INTERVAL;
    
    -- إرجاع المودات الجديدة
    RETURN QUERY
    SELECT 
        m.id,
        m.title,
        m.description,
        m.description_ar,
        m.image_urls,
        m.category,
        m.downloads,
        m.likes,
        m.created_at,
        m.updated_at
    FROM mods m
    WHERE m.created_at >= cutoff_date
    ORDER BY m.created_at DESC
    LIMIT COALESCE(limit_count, 10);
    
END;
$$ LANGUAGE plpgsql;

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_mods_created_at_desc ON mods(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_new_mods_settings_updated_at ON new_mods_settings(updated_at);

-- منح الصلاحيات اللازمة
GRANT SELECT, INSERT, UPDATE ON new_mods_settings TO anon;
GRANT SELECT, INSERT, UPDATE ON new_mods_settings TO authenticated;
GRANT EXECUTE ON FUNCTION create_new_mods_settings_table() TO anon;
GRANT EXECUTE ON FUNCTION get_new_mods_settings() TO anon;
GRANT EXECUTE ON FUNCTION update_new_mods_settings(INTEGER, INTEGER, INTEGER) TO anon;
GRANT EXECUTE ON FUNCTION get_new_mods_with_settings(INTEGER) TO anon;

-- تعليقات للتوثيق
COMMENT ON TABLE new_mods_settings IS 'جدول إعدادات المودات الجديدة - يحتوي على إعدادات مدة العرض والتخزين المؤقت';
COMMENT ON COLUMN new_mods_settings.duration IS 'مدة عرض المودات الجديدة بالأيام (1-365)';
COMMENT ON COLUMN new_mods_settings.cache_minutes IS 'مدة التخزين المؤقت بالدقائق (1-60)';
COMMENT ON COLUMN new_mods_settings.home_page_limit IS 'عدد المودات المعروضة في الصفحة الرئيسية (1-50)';

COMMENT ON FUNCTION get_new_mods_settings() IS 'دالة للحصول على إعدادات المودات الجديدة الحالية';
COMMENT ON FUNCTION update_new_mods_settings(INTEGER, INTEGER, INTEGER) IS 'دالة لتحديث إعدادات المودات الجديدة';
COMMENT ON FUNCTION get_new_mods_with_settings(INTEGER) IS 'دالة للحصول على المودات الجديدة بناءً على الإعدادات المحفوظة';
