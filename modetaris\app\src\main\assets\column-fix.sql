-- إصلاح سريع للأعمدة المفقودة
-- يحل مشكلة "column does not exist"

-- 1. إضافة عمود is_featured إلى جدول free_addons
DO $$ BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'free_addons' AND column_name = 'is_featured'
    ) THEN
        ALTER TABLE free_addons ADD COLUMN is_featured BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة عمود is_featured إلى جدول free_addons';
    ELSE
        RAISE NOTICE 'عمود is_featured موجود بالفعل في جدول free_addons';
    END IF;
END $$;

-- 2. إضافة عمود clicks إلى جدول mods إذا لم يكن موجوداً
DO $$ BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'clicks'
    ) THEN
        ALTER TABLE mods ADD COLUMN clicks INTEGER DEFAULT 0;
        RAISE NOTICE 'تم إضافة عمود clicks إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود clicks موجود بالفعل في جدول mods';
    END IF;
END $$;

-- 3. إضافة عمود description_ar إلى جدول mods إذا لم يكن موجوداً
DO $$ BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'description_ar'
    ) THEN
        ALTER TABLE mods ADD COLUMN description_ar TEXT;
        RAISE NOTICE 'تم إضافة عمود description_ar إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود description_ar موجود بالفعل في جدول mods';
    END IF;
END $$;

-- 4. إضافة أعمدة المنشئ إلى جدول mods
DO $$ BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'creator_name'
    ) THEN
        ALTER TABLE mods ADD COLUMN creator_name TEXT;
        RAISE NOTICE 'تم إضافة عمود creator_name إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود creator_name موجود بالفعل في جدول mods';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'creator_social_media'
    ) THEN
        ALTER TABLE mods ADD COLUMN creator_social_media JSONB;
        RAISE NOTICE 'تم إضافة عمود creator_social_media إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود creator_social_media موجود بالفعل في جدول mods';
    END IF;
END $$;

-- 5. إضافة الأعلام المنطقية إلى جدول mods
DO $$ BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'is_featured'
    ) THEN
        ALTER TABLE mods ADD COLUMN is_featured BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة عمود is_featured إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود is_featured موجود بالفعل في جدول mods';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'is_popular'
    ) THEN
        ALTER TABLE mods ADD COLUMN is_popular BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة عمود is_popular إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود is_popular موجود بالفعل في جدول mods';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'is_free_addon'
    ) THEN
        ALTER TABLE mods ADD COLUMN is_free_addon BOOLEAN DEFAULT false;
        RAISE NOTICE 'تم إضافة عمود is_free_addon إلى جدول mods';
    ELSE
        RAISE NOTICE 'عمود is_free_addon موجود بالفعل في جدول mods';
    END IF;
END $$;

-- 6. إنشاء الفهارس بعد التأكد من وجود الأعمدة
DO $$ BEGIN
    -- فهرس free_addons.is_featured
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'free_addons' AND column_name = 'is_featured'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_free_addons_featured ON free_addons(is_featured) WHERE is_featured = true;
        RAISE NOTICE 'تم إنشاء فهرس idx_free_addons_featured';
    END IF;
    
    -- فهرس mods.is_featured
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'is_featured'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_mods_featured ON mods(is_featured) WHERE is_featured = true;
        RAISE NOTICE 'تم إنشاء فهرس idx_mods_featured';
    END IF;
    
    -- فهرس mods.is_popular
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'mods' AND column_name = 'is_popular'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_mods_popular ON mods(is_popular) WHERE is_popular = true;
        RAISE NOTICE 'تم إنشاء فهرس idx_mods_popular';
    END IF;
END $$;

-- 7. إنشاء الفهارس الأساسية
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads DESC);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes DESC);
CREATE INDEX IF NOT EXISTS idx_free_addons_mod_id ON free_addons(mod_id);

-- 8. تحديث البيانات الفارغة
UPDATE mods SET 
    downloads = 0 WHERE downloads IS NULL;

UPDATE mods SET 
    likes = 0 WHERE likes IS NULL;

UPDATE mods SET 
    clicks = 0 WHERE clicks IS NULL;

UPDATE mods SET 
    is_featured = false WHERE is_featured IS NULL;

UPDATE mods SET 
    is_popular = false WHERE is_popular IS NULL;

UPDATE mods SET 
    is_free_addon = false WHERE is_free_addon IS NULL;

UPDATE free_addons SET 
    is_featured = false WHERE is_featured IS NULL;

-- 9. رسالة نجاح
DO $$ BEGIN
    RAISE NOTICE '✅ تم تطبيق جميع إصلاحات الأعمدة بنجاح';
END $$;
