{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:shulker", "min_engine_version": "1.8.0", "materials": {"default": "enderman"}, "textures": {"undyed": "textures/entity/shulker/shulker_undyed", "white": "textures/entity/shulker/shulker_white", "orange": "textures/entity/shulker/shulker_orange", "magenta": "textures/entity/shulker/shulker_magenta", "light_blue": "textures/entity/shulker/shulker_light_blue", "yellow": "textures/entity/shulker/shulker_yellow", "lime": "textures/entity/shulker/shulker_lime", "pink": "textures/entity/shulker/shulker_pink", "gray": "textures/entity/shulker/shulker_gray", "silver": "textures/entity/shulker/shulker_silver", "cyan": "textures/entity/shulker/shulker_cyan", "purple": "textures/entity/shulker/shulker_purple", "blue": "textures/entity/shulker/shulker_blue", "brown": "textures/entity/shulker/shulker_brown", "green": "textures/entity/shulker/shulker_green", "red": "textures/entity/shulker/shulker_red", "black": "textures/entity/shulker/shulker_black"}, "geometry": {"default": "geometry.shulker.v1.8"}, "spawn_egg": {"texture": "textures/entity/shulker/shulker", "texture_index": 34}, "scripts": {"pre_animation": ["variable.Shulker.LidPositionFactor = 180 * (0.5 + variable.Shulker.PeekAmount);", "variable.Shulker.LidRotationFactor = -1 + Math.sin(180 * (0.5 + variable.Shulker.PeekAmount));", "variable.Shulker.UpFacing = variable.Shulker.FacingDirection == 1;", "variable.Shulker.NorthFacing = variable.Shulker.FacingDirection == 2;", "variable.Shulker.SouthFacing = variable.Shulker.FacingDirection == 3;", "variable.Shulker.WestFacing = variable.Shulker.FacingDirection == 4;", "variable.Shulker.EastFacing = variable.Shulker.FacingDirection == 5;", "variable.Shulker.XPreRotation = variable.Shulker.UpFacing * 180 + variable.Shulker.NorthFacing * 90 - variable.Shulker.SouthFacing * 90;", "variable.Shulker.ZPreRotation = variable.Shulker.NorthFacing * 180 + variable.Shulker.WestFacing * 90 - variable.Shulker.EastFacing * 90;", "variable.Shulker.XOffset = -variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.YOffset = variable.Shulker.UpFacing * 16 + variable.Shulker.NorthFacing * 7.99 + variable.Shulker.SouthFacing * 7.99 + variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.ZOffset = variable.Shulker.NorthFacing * 7.99 - variable.Shulker.SouthFacing * 7.99;"]}, "animations": {"facing": "animation.shulker.facing", "move": "animation.shulker.move", "look_at_target": "animation.common.look_at_target"}, "animation_controllers": [{"facing": "controller.animation.shulker.facing"}, {"move": "controller.animation.shulker.move"}], "render_controllers": ["controller.render.shulker"]}}}