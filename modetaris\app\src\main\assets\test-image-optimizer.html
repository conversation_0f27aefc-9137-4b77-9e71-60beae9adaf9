<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار محسن الصور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .test-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .test-image.loaded {
            border-color: #4CAF50;
        }
        .test-image.placeholder-image {
            border-color: #ff9800;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #1976D2;
        }
        .stats {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ اختبار محسن الصور المحدث</h1>
        
        <div class="controls">
            <button onclick="enableDebugMode()">تفعيل وضع التصحيح</button>
            <button onclick="disableDebugMode()">إلغاء وضع التصحيح</button>
            <button onclick="addTestImages()">إضافة صور اختبار</button>
            <button onclick="showStats()">عرض الإحصائيات</button>
            <button onclick="resetOptimizer()">إعادة تعيين المحسن</button>
            <button onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="stats" id="stats">
            <h3>📊 إحصائيات المحسن</h3>
            <div id="statsContent">انقر على "عرض الإحصائيات" لرؤية البيانات</div>
        </div>

        <div class="test-section">
            <h3>🧪 صور الاختبار</h3>
            <div class="test-images" id="testImages">
                <!-- Test images will be added here -->
            </div>
        </div>

        <div class="log" id="log">
            <div>📝 سجل الأحداث:</div>
        </div>
    </div>

    <!-- Load the image optimizer -->
    <script src="image-optimizer.js"></script>
    
    <script>
        // Test functions
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function enableDebugMode() {
            if (window.imageOptimizer) {
                window.imageOptimizer.setDebugMode(true);
                log('✅ تم تفعيل وضع التصحيح');
            } else {
                log('❌ محسن الصور غير متاح');
            }
        }

        function disableDebugMode() {
            if (window.imageOptimizer) {
                window.imageOptimizer.setDebugMode(false);
                log('✅ تم إلغاء وضع التصحيح');
            } else {
                log('❌ محسن الصور غير متاح');
            }
        }

        function addTestImages() {
            const container = document.getElementById('testImages');
            const testUrls = [
                'image/placeholder.svg',
                'image/placeholder.png', // This should redirect
                'https://via.placeholder.com/300x200/ff6b00/ffffff?text=Test+Image+1',
                'https://via.placeholder.com/300x200/ffa500/ffffff?text=Test+Image+2',
                'invalid-image-url.jpg', // This should fail and show placeholder
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI5NkYzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkRhdGEgVVJMPC90ZXh0Pjwvc3ZnPg=='
            ];

            container.innerHTML = '';
            
            testUrls.forEach((url, index) => {
                const img = document.createElement('img');
                img.className = 'test-image';
                img.dataset.src = url;
                img.alt = `Test Image ${index + 1}`;
                img.title = url;
                
                container.appendChild(img);
                
                // Trigger optimization
                if (window.imageOptimizer) {
                    window.imageOptimizer.optimizeImage(img);
                }
            });
            
            log(`📸 تم إضافة ${testUrls.length} صورة اختبار`);
        }

        function showStats() {
            if (window.imageOptimizer) {
                const stats = window.imageOptimizer.getStats();
                const statsContent = document.getElementById('statsContent');
                statsContent.innerHTML = `
                    <div><strong>الصور المخزنة مؤقتاً:</strong> ${stats.cachedImages}</div>
                    <div><strong>الصور قيد التحميل:</strong> ${stats.loadingImages}</div>
                    <div><strong>الصور المعالجة:</strong> ${stats.processedImages}</div>
                    <div><strong>جودة الضغط:</strong> ${stats.compressionQuality}</div>
                    <div><strong>الحد الأقصى لحجم الصورة:</strong> ${(stats.maxImageSize / 1024).toFixed(0)} KB</div>
                    <div><strong>وضع التصحيح:</strong> ${stats.debugMode ? 'مفعل' : 'معطل'}</div>
                `;
                log('📊 تم عرض الإحصائيات');
            } else {
                log('❌ محسن الصور غير متاح');
            }
        }

        function resetOptimizer() {
            if (window.imageOptimizer) {
                window.imageOptimizer.reset();
                log('🔄 تم إعادة تعيين محسن الصور');
                showStats();
            } else {
                log('❌ محسن الصور غير متاح');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📝 سجل الأحداث:</div>';
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            log('🚀 تم تحميل صفحة الاختبار');
            
            setTimeout(() => {
                if (window.imageOptimizer) {
                    log('✅ محسن الصور متاح ومجهز');
                    showStats();
                } else {
                    log('❌ محسن الصور غير متاح');
                }
            }, 1000);
        });

        // Override console methods to capture logs
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        console.log = function(...args) {
            if (args[0] && typeof args[0] === 'string' && args[0].includes('🖼️')) {
                log(`LOG: ${args.join(' ')}`);
            }
            return originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            if (args[0] && typeof args[0] === 'string' && (args[0].includes('🖼️') || args[0].includes('⚠️'))) {
                log(`WARN: ${args.join(' ')}`);
            }
            return originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            if (args[0] && typeof args[0] === 'string' && (args[0].includes('🖼️') || args[0].includes('❌'))) {
                log(`ERROR: ${args.join(' ')}`);
            }
            return originalError.apply(console, args);
        };
    </script>
</body>
</html>
