// Subscription Settings Admin JavaScript
// إدارة إعدادات الاشتراك المجاني

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let campaigns = [];
let currentSettings = {};
let currentCampaignBanner = null; // New: To store the banner associated with the default campaign

// DOM Elements for Campaign Banner
const defaultCampaignSelector = document.getElementById('defaultCampaign');
const campaignBannerSettingsDiv = document.getElementById('campaignBannerSettings');
const campaignBannerTitleInput = document.getElementById('campaignBannerTitle');
const campaignBannerDescriptionInput = document.getElementById('campaignBannerDescription');
const campaignBannerImageUrlInput = document.getElementById('campaignBannerImageUrl');
const campaignBannerTargetUrlInput = document.getElementById('campaignBannerTargetUrl');
const campaignBannerDisplayOrderInput = document.getElementById('campaignBannerDisplayOrder');
const campaignBannerIsActiveInput = document.getElementById('campaignBannerIsActive');
const campaignBannerIdInput = document.getElementById('campaignBannerId');
const saveCampaignBannerButton = document.getElementById('saveCampaignBanner');
const resetCampaignBannerFormButton = document.getElementById('resetCampaignBannerForm');

// DOM Elements for Floating Icon
const enableFloatingIconToggle = document.getElementById('enableFloatingIcon');
const floatingIconDetailsDiv = document.getElementById('floatingIconDetails');
const floatingIconImageUrlInput = document.getElementById('floatingIconImageUrl');
const saveFloatingIconSettingsButton = document.getElementById('saveFloatingIconSettings');


// Initialize the application
document.addEventListener('DOMContentLoaded', async function() { // Changed to async
    console.log('Subscription Settings Admin loaded');
    await loadCampaigns(); // Ensure campaigns are loaded before settings
    loadSettings();
    loadStatistics();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Add change listeners to all toggle switches
    const toggles = document.querySelectorAll('input[type="checkbox"]');
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            console.log(`Setting ${this.id} changed to:`, this.checked);
        });
    });

    // Add change listener to campaign selector
    if (defaultCampaignSelector) {
        defaultCampaignSelector.addEventListener('change', async function() { // Changed to async
            console.log('Default campaign changed to:', this.value);
            await loadCampaignBanner(this.value); // Load banner for selected campaign
            campaignBannerSettingsDiv.style.display = this.value ? 'block' : 'none'; // Show/hide section
        });
    }

    // Add event listeners for campaign banner buttons
    if (saveCampaignBannerButton) {
        saveCampaignBannerButton.addEventListener('click', saveCampaignBanner);
    }
    if (resetCampaignBannerFormButton) {
        resetCampaignBannerFormButton.addEventListener('click', resetCampaignBannerForm);
    }

    // Add event listeners for floating icon settings
    if (enableFloatingIconToggle) {
        enableFloatingIconToggle.addEventListener('change', function() {
            floatingIconDetailsDiv.style.display = this.checked ? 'flex' : 'none';
        });
    }
    if (saveFloatingIconSettingsButton) {
        saveFloatingIconSettingsButton.addEventListener('click', saveFloatingIconSettings);
    }
}

// Load current settings
function loadSettings() {
    try {
        // Load settings from localStorage
        const showOnEntry = localStorage.getItem('showSubscriptionOnEntry') === 'true';
        const showBeforeDownload = localStorage.getItem('showSubscriptionBeforeDownload') === 'true';
        const useSubscriptionPage = localStorage.getItem('useSubscriptionPage') === 'true';
        const enableBanners = localStorage.getItem('enableSubscriptionBanners') === 'true';
        const defaultCampaign = localStorage.getItem('defaultSubscriptionCampaign') || '';
        const enableFloatingIcon = localStorage.getItem('enableFloatingIcon') === 'true';
        const floatingIconImageUrl = localStorage.getItem('floatingIconImageUrl') || '';


        // Update UI
        document.getElementById('showOnEntry').checked = showOnEntry;
        document.getElementById('showBeforeDownload').checked = showBeforeDownload;
        document.getElementById('useSubscriptionPage').checked = useSubscriptionPage;
        document.getElementById('enableBanners').checked = enableBanners;
        defaultCampaignSelector.value = defaultCampaign; // Use new DOM element reference
        enableFloatingIconToggle.checked = enableFloatingIcon;
        floatingIconImageUrlInput.value = floatingIconImageUrl;

        // Show/hide campaign banner settings based on defaultCampaign value
        campaignBannerSettingsDiv.style.display = defaultCampaign ? 'block' : 'none';
        if (defaultCampaign) {
            loadCampaignBanner(defaultCampaign); // Load banner for the default campaign
        }

        // Show/hide floating icon details based on enableFloatingIcon value
        floatingIconDetailsDiv.style.display = enableFloatingIcon ? 'flex' : 'none';


        // Store current settings
        currentSettings = {
            showOnEntry,
            showBeforeDownload,
            useSubscriptionPage,
            enableBanners,
            defaultCampaign,
            enableFloatingIcon,
            floatingIconImageUrl
        };

        console.log('Settings loaded:', currentSettings);

    } catch (error) {
        console.error('Error loading settings:', error);
        showError('حدث خطأ أثناء تحميل الإعدادات');
    }
}

// Load campaigns for selector
async function loadCampaigns() {
    try {
        const { data, error } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('id, title_ar, title_en, is_active')
            .eq('is_active', true)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error loading campaigns:', error);
            return;
        }

        campaigns = data || [];
        
        // Populate campaign selector
        if (defaultCampaignSelector) { // Use new DOM element reference
            // Clear existing options except the first one
            while (defaultCampaignSelector.children.length > 1) {
                defaultCampaignSelector.removeChild(defaultCampaignSelector.lastChild);
            }

            // Add campaign options
            campaigns.forEach(campaign => {
                const option = document.createElement('option');
                option.value = campaign.id;
                option.textContent = `${campaign.title_ar} (${campaign.title_en})`;
                defaultCampaignSelector.appendChild(option);
            });

            // Set current default campaign
            const defaultCampaign = localStorage.getItem('defaultSubscriptionCampaign');
            if (defaultCampaign) {
                defaultCampaignSelector.value = defaultCampaign;
            }
        }

        console.log(`Loaded ${campaigns.length} campaigns`);

    } catch (error) {
        console.error('Error loading campaigns:', error);
        showError('حدث خطأ أثناء تحميل الحملات');
    }
}

// Load statistics
async function loadStatistics() {
    try {
        // Load active campaigns count
        const { data: campaignsData, error: campaignsError } = await supabaseClient
            .from('free_subscription_campaigns')
            .select('id')
            .eq('is_active', true);

        if (!campaignsError) {
            document.getElementById('activeCampaigns').textContent = campaignsData?.length || 0;
        }

        // Load active subscribers count
        const { data: subscribersData, error: subscribersError } = await supabaseClient
            .from('user_subscriptions')
            .select('id')
            .eq('status', 'active')
            .gte('expires_at', new Date().toISOString());

        if (!subscribersError) {
            document.getElementById('activeSubscribers').textContent = subscribersData?.length || 0;
        }

        // Load total tasks count
        const { data: tasksData, error: tasksError } = await supabaseClient
            .from('campaign_tasks')
            .select('id')
            .eq('is_active', true);

        if (!tasksError) {
            document.getElementById('totalTasks').textContent = tasksData?.length || 0;
        }

    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Save settings
function saveSettings() {
    try {
        // Get current values from UI
        const showOnEntry = document.getElementById('showOnEntry').checked;
        const showBeforeDownload = document.getElementById('showBeforeDownload').checked;
        const useSubscriptionPage = document.getElementById('useSubscriptionPage').checked;
        const enableBanners = document.getElementById('enableSubscriptionBanners').checked;
        const defaultCampaign = document.getElementById('defaultCampaign').value;
        const enableFloatingIcon = enableFloatingIconToggle.checked;
        const floatingIconImageUrl = floatingIconImageUrlInput.value;

        // Save to localStorage
        localStorage.setItem('showSubscriptionOnEntry', showOnEntry.toString());
        localStorage.setItem('showSubscriptionBeforeDownload', showBeforeDownload.toString());
        localStorage.setItem('useSubscriptionPage', useSubscriptionPage.toString());
        localStorage.setItem('enableSubscriptionBanners', enableBanners.toString());
        localStorage.setItem('defaultSubscriptionCampaign', defaultCampaign);
        localStorage.setItem('enableFloatingIcon', enableFloatingIcon.toString());
        localStorage.setItem('floatingIconImageUrl', floatingIconImageUrl);


        // Update current settings
        currentSettings = {
            showOnEntry,
            showBeforeDownload,
            useSubscriptionPage,
            enableBanners,
            defaultCampaign,
            enableFloatingIcon,
            floatingIconImageUrl
        };

        console.log('Settings saved:', currentSettings);
        showSuccess('تم حفظ الإعدادات بنجاح!');

    } catch (error) {
        console.error('Error saving settings:', error);
        showError('حدث خطأ أثناء حفظ الإعدادات');
    }
}

// New function to save floating icon settings
function saveFloatingIconSettings() {
    try {
        const imageUrl = floatingIconImageUrlInput.value.trim();
        if (!imageUrl) {
            showError('يرجى إدخال رابط صورة الأيقونة العائمة.');
            return;
        }
        localStorage.setItem('floatingIconImageUrl', imageUrl);
        currentSettings.floatingIconImageUrl = imageUrl;
        showSuccess('تم حفظ إعدادات الأيقونة العائمة بنجاح!');
    } catch (error) {
        console.error('Error saving floating icon settings:', error);
        showError('حدث خطأ أثناء حفظ إعدادات الأيقونة العائمة.');
    }
}

// New function to load banner for a specific campaign
async function loadCampaignBanner(campaignId) {
    if (!campaignId) {
        resetCampaignBannerForm();
        return;
    }
    try {
        const { data, error } = await supabaseClient
            .from('banner_ads')
            .select('*')
            .eq('campaign_id', campaignId)
            .eq('display_type', 'banner')
            .eq('banner_type', 'subscription')
            .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
            console.error('Error fetching campaign banner:', error);
            showError('حدث خطأ أثناء تحميل بانر الحملة');
            resetCampaignBannerForm();
            return;
        }

        if (data) {
            currentCampaignBanner = data;
            campaignBannerTitleInput.value = data.title || '';
            campaignBannerDescriptionInput.value = data.description || '';
            campaignBannerImageUrlInput.value = data.image_url || '';
            campaignBannerTargetUrlInput.value = data.target_url || '';
            campaignBannerDisplayOrderInput.value = data.display_order || 1;
            campaignBannerIsActiveInput.checked = data.is_active;
            campaignBannerIdInput.value = data.id;
            saveCampaignBannerButton.textContent = 'تحديث بانر الحملة';
            showSuccess('تم تحميل بانر الحملة بنجاح.');
        } else {
            currentCampaignBanner = null;
            resetCampaignBannerForm();
            showError('لا يوجد بانر مرتبط بهذه الحملة. يمكنك إنشاء واحد.');
        }
    } catch (error) {
        console.error('Unexpected error loading campaign banner:', error);
        showError('حدث خطأ غير متوقع أثناء تحميل بانر الحملة');
        resetCampaignBannerForm();
    }
}

// New function to save campaign banner
async function saveCampaignBanner() {
    const campaignId = defaultCampaignSelector.value;
    if (!campaignId) {
        showError('يرجى اختيار حملة افتراضية أولاً لربط البانر بها.');
        return;
    }

    const title = campaignBannerTitleInput.value.trim();
    const description = campaignBannerDescriptionInput.value.trim();
    const imageUrl = campaignBannerImageUrlInput.value.trim();
    const targetUrl = campaignBannerTargetUrlInput.value.trim();
    const displayOrder = parseInt(campaignBannerDisplayOrderInput.value) || 1;
    const isActive = campaignBannerIsActiveInput.checked;
    const bannerId = campaignBannerIdInput.value;

    if (!imageUrl) {
        showError('يرجى إدخال رابط صورة البانر.');
        return;
    }

    try {
        let result;
        const bannerData = {
            title,
            description,
            image_url: imageUrl,
            target_url: targetUrl,
            display_order: displayOrder,
            is_active: isActive,
            campaign_id: campaignId,
            display_type: 'banner', // Fixed value
            banner_type: 'subscription', // Fixed value
        };

        if (bannerId) {
            // Update existing banner
            result = await supabaseClient
                .from('banner_ads')
                .update({ ...bannerData, updated_at: new Date() })
                .eq('id', bannerId);
        } else {
            // Insert new banner
            result = await supabaseClient
                .from('banner_ads')
                .insert({ ...bannerData, created_at: new Date() });
        }

        if (result.error) {
            console.error('Error saving campaign banner:', result.error);
            showError('حدث خطأ أثناء حفظ بانر الحملة: ' + result.error.message);
            return;
        }

        currentCampaignBanner = result.data ? result.data[0] : bannerData; // Update current banner data
        campaignBannerIdInput.value = currentCampaignBanner.id; // Ensure ID is set for new banners
        saveCampaignBannerButton.textContent = 'تحديث بانر الحملة';
        showSuccess(bannerId ? 'تم تحديث بانر الحملة بنجاح!' : 'تم إنشاء بانر الحملة بنجاح!');

    } catch (error) {
        console.error('Unexpected error saving campaign banner:', error);
        showError('حدث خطأ غير متوقع أثناء حفظ بانر الحملة');
    }
}

// New function to reset campaign banner form
function resetCampaignBannerForm() {
    campaignBannerTitleInput.value = '';
    campaignBannerDescriptionInput.value = '';
    campaignBannerImageUrlInput.value = '';
    campaignBannerTargetUrlInput.value = '';
    campaignBannerDisplayOrderInput.value = '1';
    campaignBannerIsActiveInput.checked = true;
    campaignBannerIdInput.value = '';
    saveCampaignBannerButton.textContent = 'حفظ بانر الحملة';
    currentCampaignBanner = null;
    showSuccess('تم إعادة تعيين نموذج بانر الحملة.');
}

// Reset settings to defaults
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
        try {
            // Clear localStorage
            localStorage.removeItem('showSubscriptionOnEntry');
            localStorage.removeItem('showSubscriptionBeforeDownload');
            localStorage.removeItem('useSubscriptionPage');
            localStorage.removeItem('enableSubscriptionBanners');
            localStorage.removeItem('defaultSubscriptionCampaign');
            localStorage.removeItem('enableFloatingIcon');
            localStorage.removeItem('floatingIconImageUrl');

            // Reset UI to defaults
            document.getElementById('showOnEntry').checked = false;
            document.getElementById('showBeforeDownload').checked = false;
            document.getElementById('useSubscriptionPage').checked = false;
            document.getElementById('enableSubscriptionBanners').checked = false;
            document.getElementById('defaultCampaign').value = '';
            enableFloatingIconToggle.checked = false;
            floatingIconImageUrlInput.value = '';
            floatingIconDetailsDiv.style.display = 'none';


            // Update current settings
            currentSettings = {
                showOnEntry: false,
                showBeforeDownload: false,
                useSubscriptionPage: false,
                enableBanners: false,
                defaultCampaign: '',
                enableFloatingIcon: false,
                floatingIconImageUrl: ''
            };

            console.log('Settings reset to defaults');
            showSuccess('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');

        } catch (error) {
            console.error('Error resetting settings:', error);
            showError('حدث خطأ أثناء إعادة تعيين الإعدادات');
        }
    }
}

// Test subscription page
function testSubscriptionPage() {
    try {
        const defaultCampaign = document.getElementById('defaultCampaign').value;
        
        if (!defaultCampaign) {
            showError('يرجى اختيار حملة افتراضية أولاً');
            return;
        }

        // Open subscription page in new tab
        const testUrl = `../subscription-page.html?campaign=${defaultCampaign}&test=true`;
        window.open(testUrl, '_blank');
        
        showSuccess('تم فتح صفحة الاشتراك للاختبار');

    } catch (error) {
        console.error('Error testing subscription page:', error);
        showError('حدث خطأ أثناء فتح صفحة الاختبار');
    }
}

// Clear user data for testing
function clearUserData() {
    if (confirm('هل أنت متأكد من مسح جميع بيانات المستخدم المحلية؟ هذا سيؤثر على تجربة الاختبار.')) {
        try {
            // Clear user-related localStorage items
            const keysToRemove = [
                'userId',
                'selectedLanguage',
                'languageSelected',
                'pendingDownload'
            ];

            // Remove user progress and subscription data
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('banner_view_count_') || 
                    key.startsWith('entry_ad_view_count_') || 
                    key.startsWith('entry_ad_last_shown_') || 
                    key.startsWith('entry_ad_shown_once_') ||
                    key.startsWith('downloaded_')) {
                    localStorage.removeItem(key);
                }
            });

            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
            });

            console.log('User data cleared for testing');
            showSuccess('تم مسح بيانات المستخدم بنجاح. يمكنك الآن اختبار التجربة الجديدة.');

        } catch (error) {
            console.error('Error clearing user data:', error);
            showError('حدث خطأ أثناء مسح بيانات المستخدم');
        }
    }
}

// Export settings as JSON
function exportSettings() {
    try {
        const settingsToExport = {
            ...currentSettings,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const dataStr = JSON.stringify(settingsToExport, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `subscription_settings_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showSuccess('تم تصدير الإعدادات بنجاح');

    } catch (error) {
        console.error('Error exporting settings:', error);
        showError('حدث خطأ أثناء تصدير الإعدادات');
    }
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    if (errorDiv) errorDiv.style.display = 'none';
    
    if (successDiv) {
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 5000);
    }
}

// Show error message
function showError(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    if (successDiv) successDiv.style.display = 'none';
    
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }
}
