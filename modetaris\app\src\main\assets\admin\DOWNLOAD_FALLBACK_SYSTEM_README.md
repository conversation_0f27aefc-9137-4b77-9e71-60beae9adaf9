# نظام التحميل الاحتياطي الذكي
# Smart Download Fallback System

## نظرة عامة | Overview

نظام التحميل الاحتياطي الذكي هو حل شامل لضمان استمرارية تحميل المودات حتى في حالة فشل الروابط الأصلية. يوفر النظام آليات متقدمة للكشف عن الأخطاء، التبديل التلقائي للروابط الاحتياطية، وإدارة شاملة لمشاكل التحميل.

The Smart Download Fallback System is a comprehensive solution to ensure continuous mod downloads even when original links fail. The system provides advanced error detection, automatic fallback switching, and comprehensive download issue management.

## المكونات الرئيسية | Main Components

### 1. نظام التحميل الاحتياطي | Download Fallback System
- **الملف**: `download-fallback-system.js`
- **الوظيفة**: إدارة عملية التحميل مع التبديل التلقائي للروابط الاحتياطية
- **المميزات**:
  - كشف تلقائي لفشل التحميل
  - تجربة روابط احتياطية متعددة
  - تسجيل مفصل للأخطاء
  - دعم Firebase Storage كمصدر احتياطي

### 2. لوحة إدارة أخطاء التحميل | Download Error Manager
- **الملفات**: 
  - `download-error-manager.html`
  - `download-error-manager.js`
  - `download-error-styles.css`
- **الوظيفة**: واجهة إدارية شاملة لمراقبة وإصلاح أخطاء التحميل
- **المميزات**:
  - عرض إحصائيات مفصلة للأخطاء
  - فلترة وتصنيف الأخطاء
  - إصلاح فردي وجماعي للروابط
  - تتبع تاريخ الإصلاحات

### 3. نظام مساعد فتح الملفات | File Opening Helper
- **الملف**: `file-opening-helper.js`
- **الوظيفة**: مساعدة المستخدمين في حل مشاكل فتح الملفات المحملة
- **المميزات**:
  - كشف مشاكل فتح الملفات
  - عرض خطوات حل المشاكل
  - تجميع تقارير المشاكل
  - دعم متعدد اللغات

## التثبيت والإعداد | Installation & Setup

### 1. إضافة الملفات المطلوبة | Add Required Files

```html
<!-- في ملف index.html -->
<!-- نظام التحميل الاحتياطي الذكي -->
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>
<script src="download-fallback-system.js"></script>

<!-- نظام مساعد فتح الملفات -->
<script src="file-opening-helper.js"></script>
```

### 2. إعداد قاعدة البيانات | Database Setup

```sql
-- جدول أخطاء التحميل
CREATE TABLE download_errors (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    mod_name TEXT,
    original_url TEXT,
    backup_url TEXT,
    error_type TEXT,
    error_message TEXT,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT NOW(),
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP,
    resolved_by TEXT,
    resolution_notes TEXT
);

-- جدول الروابط الاحتياطية
CREATE TABLE mod_backup_urls (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    backup_url TEXT NOT NULL,
    backup_type TEXT DEFAULT 'external',
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    last_tested TIMESTAMP,
    test_result BOOLEAN
);

-- جدول إحصائيات التحميل
CREATE TABLE download_statistics (
    id SERIAL PRIMARY KEY,
    date DATE DEFAULT CURRENT_DATE,
    total_attempts INTEGER DEFAULT 0,
    successful_downloads INTEGER DEFAULT 0,
    backup_downloads INTEGER DEFAULT 0,
    failed_downloads INTEGER DEFAULT 0,
    error_types JSONB
);

-- جدول تاريخ إصلاح الروابط
CREATE TABLE url_fix_history (
    id SERIAL PRIMARY KEY,
    mod_id INTEGER REFERENCES mods(id),
    error_id INTEGER REFERENCES download_errors(id),
    old_url TEXT,
    new_url TEXT,
    backup_url TEXT,
    fix_type TEXT,
    fix_description TEXT,
    fixed_by TEXT,
    fixed_at TIMESTAMP DEFAULT NOW(),
    fix_method TEXT,
    is_verified BOOLEAN DEFAULT FALSE
);
```

### 3. إعداد Firebase (اختياري) | Firebase Setup (Optional)

```javascript
// إعداد Firebase للروابط الاحتياطية
const firebaseConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};

// سيتم تهيئة Firebase تلقائياً في download-fallback-system.js
```

## الاستخدام | Usage

### 1. التحميل مع النظام الاحتياطي | Download with Fallback

```javascript
// استخدام النظام المحسن للتحميل
async function downloadMod(modId, modName, downloadUrl) {
    try {
        // استخدام النظام الاحتياطي إذا كان متاحاً
        if (window.downloadFallbackSystem) {
            const result = await window.downloadFallbackSystem.downloadMod(
                modId, 
                modName, 
                downloadUrl
            );
            
            if (result.success) {
                console.log(`تم التحميل من ${result.source}`);
                return true;
            }
        }
        
        // استخدام النظام القديم كـ fallback
        return downloadModLegacy(modId, modName, downloadUrl);
        
    } catch (error) {
        console.error('خطأ في التحميل:', error);
        return false;
    }
}
```

### 2. إدارة الأخطاء | Error Management

```javascript
// فتح لوحة إدارة الأخطاء
function openDownloadErrorManager() {
    window.open('admin/download-error-manager.html', '_blank');
}

// الحصول على إحصائيات الأخطاء
async function getErrorStats() {
    const stats = await window.downloadFallbackSystem.getErrorStatistics();
    console.log('إحصائيات الأخطاء:', stats);
}
```

### 3. إضافة روابط احتياطية | Add Backup URLs

```javascript
// إضافة رابط احتياطي لمود
async function addBackupUrl(modId, backupUrl, priority = 1) {
    const { error } = await supabaseClient
        .from('mod_backup_urls')
        .insert({
            mod_id: modId,
            backup_url: backupUrl,
            backup_type: 'external',
            priority: priority,
            is_active: true
        });
    
    if (!error) {
        console.log('تم إضافة الرابط الاحتياطي بنجاح');
    }
}
```

## المميزات المتقدمة | Advanced Features

### 1. الكشف الذكي للأخطاء | Smart Error Detection
- كشف انقطاع الاتصال
- كشف الروابط المعطلة
- كشف الملفات التالفة
- كشف انتهاء مهلة التحميل

### 2. التبديل التلقائي | Automatic Fallback
- ترتيب الروابط حسب الأولوية
- اختبار الروابط قبل الاستخدام
- تسجيل نتائج الاختبارات
- تحديث حالة الروابط

### 3. التقارير والإحصائيات | Reports & Analytics
- إحصائيات يومية للتحميل
- تحليل أنواع الأخطاء
- معدلات نجاح التحميل
- استخدام الروابط الاحتياطية

### 4. الإصلاح التلقائي | Auto-Repair
- إصلاح الروابط المعطلة تلقائياً
- تحديث قاعدة البيانات
- إشعارات الإدارة
- تتبع تاريخ الإصلاحات

## واجهة المستخدم | User Interface

### لوحة إدارة الأخطاء | Error Management Dashboard
- **الرابط**: `admin/download-error-manager.html`
- **المميزات**:
  - عرض جميع أخطاء التحميل
  - فلترة حسب النوع والتاريخ
  - إصلاح فردي وجماعي
  - تصدير التقارير
  - إحصائيات مفصلة

### نظام مساعدة المستخدم | User Help System
- عرض تلقائي لنصائح حل المشاكل
- دعم متعدد اللغات (عربي/إنجليزي)
- تجميع تقارير المشاكل
- خطوات حل مفصلة

## الصيانة والمراقبة | Maintenance & Monitoring

### 1. مراقبة الأداء | Performance Monitoring
```javascript
// مراقبة معدل نجاح التحميل
setInterval(async () => {
    const stats = await getDownloadStats();
    if (stats.successRate < 90) {
        notifyAdmin('معدل نجاح التحميل منخفض');
    }
}, 300000); // كل 5 دقائق
```

### 2. تنظيف البيانات | Data Cleanup
```javascript
// تنظيف الأخطاء القديمة المحلولة
async function cleanupOldErrors() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30);
    
    await supabaseClient
        .from('download_errors')
        .delete()
        .eq('is_resolved', true)
        .lt('resolved_at', cutoffDate.toISOString());
}
```

### 3. النسخ الاحتياطي | Backup
```javascript
// نسخ احتياطي لإعدادات النظام
async function backupSystemSettings() {
    const settings = {
        backupUrls: await getBackupUrls(),
        errorSettings: await getErrorSettings(),
        statistics: await getSystemStatistics()
    };
    
    // حفظ النسخة الاحتياطية
    localStorage.setItem('systemBackup', JSON.stringify(settings));
}
```

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

1. **عدم عمل النظام الاحتياطي**
   - تأكد من تحميل `download-fallback-system.js`
   - تحقق من إعدادات Firebase
   - راجع console للأخطاء

2. **عدم ظهور لوحة الإدارة**
   - تأكد من وجود ملفات HTML و CSS
   - تحقق من صلاحيات الوصول
   - راجع إعدادات المتصفح

3. **عدم حفظ الإحصائيات**
   - تأكد من إنشاء جداول قاعدة البيانات
   - تحقق من اتصال Supabase
   - راجع صلاحيات قاعدة البيانات

## التطوير المستقبلي | Future Development

### مميزات مخططة | Planned Features
- [ ] دعم CDN متعدد
- [ ] ضغط الملفات التلقائي
- [ ] تحليل ذكي للأخطاء
- [ ] إشعارات فورية للإدارة
- [ ] تكامل مع أنظمة مراقبة خارجية

### تحسينات الأداء | Performance Improvements
- [ ] تخزين مؤقت للروابط
- [ ] تحميل متوازي
- [ ] ضغط البيانات
- [ ] تحسين استعلامات قاعدة البيانات

## الدعم | Support

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: راجع ملفات README الأخرى
- **السجلات**: تحقق من console المتصفح

---

**ملاحظة**: هذا النظام قيد التطوير المستمر. يرجى مراجعة التحديثات بانتظام.
