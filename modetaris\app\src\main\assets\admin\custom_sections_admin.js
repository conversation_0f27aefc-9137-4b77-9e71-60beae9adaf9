// Custom Sections Admin JavaScript
// إدارة الأقسام المخصصة

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let allMods = [];
let customSections = [];
let selectedMods = new Set();
let editingSection = null;

// Available icons for sections
const AVAILABLE_ICONS = [
    'fas fa-star', 'fas fa-fire', 'fas fa-crown', 'fas fa-gem', 'fas fa-heart',
    'fas fa-bolt', 'fas fa-magic', 'fas fa-rocket', 'fas fa-trophy', 'fas fa-medal',
    'fas fa-gift', 'fas fa-diamond', 'fas fa-sparkles', 'fas fa-sun', 'fas fa-moon',
    'fas fa-leaf', 'fas fa-snowflake', 'fas fa-mountain', 'fas fa-tree', 'fas fa-flower',
    'fas fa-cube', 'fas fa-cubes', 'fas fa-puzzle-piece', 'fas fa-gamepad', 'fas fa-dice',
    'fas fa-sword', 'fas fa-shield', 'fas fa-hammer', 'fas fa-wrench', 'fas fa-cog',
    'fas fa-home', 'fas fa-castle', 'fas fa-building', 'fas fa-city', 'fas fa-globe',
    'fas fa-map', 'fas fa-compass', 'fas fa-flag', 'fas fa-bookmark', 'fas fa-tag'
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Custom Sections Admin loaded');
    initializePage();
    setupEventListeners();
});

// Initialize page
async function initializePage() {
    try {
        await loadMods();
        await loadCustomSections();
        setupIconSelector();
    } catch (error) {
        console.error('Error initializing page:', error);
        showError('حدث خطأ أثناء تحميل الصفحة');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Form submission
    const form = document.getElementById('sectionForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }

    // Icon selection
    const iconSelector = document.getElementById('iconSelector');
    if (iconSelector) {
        iconSelector.addEventListener('click', handleIconSelection);
    }
}

// Setup icon selector
function setupIconSelector() {
    const iconSelector = document.getElementById('iconSelector');
    if (!iconSelector) return;

    iconSelector.innerHTML = '';
    
    AVAILABLE_ICONS.forEach(iconClass => {
        const iconOption = document.createElement('div');
        iconOption.className = 'icon-option';
        iconOption.dataset.icon = iconClass;
        iconOption.innerHTML = `<i class="${iconClass}"></i>`;
        iconSelector.appendChild(iconOption);
    });

    // Select first icon by default
    if (AVAILABLE_ICONS.length > 0) {
        const firstIcon = iconSelector.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('selected');
            document.getElementById('selectedIcon').value = AVAILABLE_ICONS[0];
        }
    }
}

// Handle icon selection
function handleIconSelection(event) {
    const iconOption = event.target.closest('.icon-option');
    if (!iconOption) return;

    // Remove previous selection
    document.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selection to clicked icon
    iconOption.classList.add('selected');
    
    // Update input value
    const selectedIcon = iconOption.dataset.icon;
    document.getElementById('selectedIcon').value = selectedIcon;
}

// Load all mods
async function loadMods() {
    try {
        const { data: mods, error } = await supabaseClient
            .from('mods')
            .select('id, name, category, likes, downloads, created_at')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error loading mods:', error);
            showError('حدث خطأ أثناء تحميل المودات');
            return;
        }

        allMods = mods || [];
        renderModSelector();
        
        console.log(`Loaded ${allMods.length} mods`);

    } catch (error) {
        console.error('Error in loadMods:', error);
        showError('حدث خطأ أثناء تحميل المودات');
    }
}

// Render mod selector
function renderModSelector() {
    const modSelector = document.getElementById('modSelector');
    if (!modSelector || !allMods.length) {
        modSelector.innerHTML = '<div style="text-align: center; color: #ccc; padding: 20px;">لا توجد مودات متاحة</div>';
        return;
    }

    let modsHtml = '';
    
    allMods.forEach(mod => {
        const isSelected = selectedMods.has(mod.id);
        modsHtml += `
            <div class="mod-item ${isSelected ? 'selected' : ''}" data-mod-id="${mod.id}">
                <input type="checkbox" class="mod-checkbox" ${isSelected ? 'checked' : ''} 
                       onchange="toggleModSelection(${mod.id})">
                <div class="mod-info">
                    <div class="mod-name">${mod.name}</div>
                    <div class="mod-category">${mod.category} • ${mod.likes || 0} إعجاب • ${mod.downloads || 0} تحميل</div>
                </div>
            </div>
        `;
    });

    modSelector.innerHTML = modsHtml;
}

// Toggle mod selection
function toggleModSelection(modId) {
    if (selectedMods.has(modId)) {
        selectedMods.delete(modId);
    } else {
        selectedMods.add(modId);
    }

    // Update UI
    const modItem = document.querySelector(`[data-mod-id="${modId}"]`);
    if (modItem) {
        modItem.classList.toggle('selected', selectedMods.has(modId));
    }

    console.log(`Selected mods: ${Array.from(selectedMods).join(', ')}`);
}

// Load custom sections
async function loadCustomSections() {
    try {
        const { data: sections, error } = await supabaseClient
            .from('custom_sections')
            .select('*')
            .order('display_order', { ascending: true });

        if (error) {
            console.error('Error loading custom sections:', error);
            showError('حدث خطأ أثناء تحميل الأقسام');
            return;
        }

        customSections = sections || [];
        renderSectionsList();
        
        console.log(`Loaded ${customSections.length} custom sections`);

    } catch (error) {
        console.error('Error in loadCustomSections:', error);
        showError('حدث خطأ أثناء تحميل الأقسام');
    }
}

// Render sections list
function renderSectionsList() {
    const sectionsList = document.getElementById('sectionsList');
    if (!sectionsList) return;

    if (!customSections.length) {
        sectionsList.innerHTML = `
            <div style="text-align: center; color: #ccc; padding: 50px;">
                <i class="fas fa-layer-group" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                <p>لا توجد أقسام مخصصة حالياً</p>
                <p style="font-size: 0.9rem; margin-top: 10px;">ابدأ بإنشاء قسم جديد من النموذج المجاور</p>
            </div>
        `;
        return;
    }

    let sectionsHtml = '';
    
    customSections.forEach((section, index) => {
        const modCount = section.selected_mods ? JSON.parse(section.selected_mods).length : 0;
        
        sectionsHtml += `
            <div class="section-item" data-section-id="${section.id}">
                <div class="order-controls">
                    <button class="order-btn" onclick="moveSectionUp(${section.id})" 
                            ${index === 0 ? 'disabled style="opacity: 0.3;"' : ''}>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <button class="order-btn" onclick="moveSectionDown(${section.id})" 
                            ${index === customSections.length - 1 ? 'disabled style="opacity: 0.3;"' : ''}>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                
                <div class="section-header">
                    <div class="section-info">
                        <div class="section-icon">
                            <i class="${section.icon_class}"></i>
                        </div>
                        <div class="section-details">
                            <h3>${section.name_ar}</h3>
                            <p>${section.name_en}</p>
                        </div>
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="editSection(${section.id})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger" onclick="deleteSection(${section.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
                
                <div class="section-stats">
                    <div class="stat-item">
                        <div class="stat-value">${modCount}</div>
                        <div class="stat-label">مودات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${section.max_mods}</div>
                        <div class="stat-label">الحد الأقصى</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${section.is_active ? 'نشط' : 'غير نشط'}</div>
                        <div class="stat-label">الحالة</div>
                    </div>
                </div>
                
                ${section.description ? `<p style="color: #ccc; margin-top: 15px; font-style: italic;">${section.description}</p>` : ''}
            </div>
        `;
    });

    sectionsList.innerHTML = sectionsHtml;
}

// Handle form submission
async function handleFormSubmit(event) {
    event.preventDefault();
    
    try {
        const formData = {
            name_ar: document.getElementById('sectionNameAr').value.trim(),
            name_en: document.getElementById('sectionNameEn').value.trim(),
            icon_class: document.getElementById('selectedIcon').value,
            description: document.getElementById('sectionDescription').value.trim(),
            max_mods: parseInt(document.getElementById('maxMods').value),
            selected_mods: JSON.stringify(Array.from(selectedMods)),
            is_active: document.getElementById('isActive').value === 'true'
        };

        // Validation
        if (!formData.name_ar || !formData.name_en) {
            showError('يرجى إدخال اسم القسم باللغتين العربية والإنجليزية');
            return;
        }

        if (!formData.icon_class) {
            showError('يرجى اختيار أيقونة للقسم');
            return;
        }

        if (selectedMods.size === 0) {
            showError('يرجى اختيار مود واحد على الأقل للقسم');
            return;
        }

        if (editingSection) {
            // Update existing section
            await updateSection(editingSection, formData);
        } else {
            // Create new section
            await createSection(formData);
        }

    } catch (error) {
        console.error('Error handling form submission:', error);
        showError('حدث خطأ أثناء حفظ القسم');
    }
}

// Create new section
async function createSection(formData) {
    try {
        // Get next display order
        const maxOrder = customSections.length > 0 
            ? Math.max(...customSections.map(s => s.display_order || 0))
            : 0;
        
        formData.display_order = maxOrder + 1;
        formData.created_at = new Date().toISOString();

        const { data, error } = await supabaseClient
            .from('custom_sections')
            .insert([formData])
            .select()
            .single();

        if (error) {
            console.error('Error creating section:', error);
            showError('حدث خطأ أثناء إنشاء القسم');
            return;
        }

        showSuccess('تم إنشاء القسم بنجاح!');
        resetForm();
        await loadCustomSections();

    } catch (error) {
        console.error('Error in createSection:', error);
        showError('حدث خطأ أثناء إنشاء القسم');
    }
}

// Update existing section
async function updateSection(sectionId, formData) {
    try {
        formData.updated_at = new Date().toISOString();

        const { error } = await supabaseClient
            .from('custom_sections')
            .update(formData)
            .eq('id', sectionId);

        if (error) {
            console.error('Error updating section:', error);
            showError('حدث خطأ أثناء تحديث القسم');
            return;
        }

        showSuccess('تم تحديث القسم بنجاح!');
        resetForm();
        await loadCustomSections();

    } catch (error) {
        console.error('Error in updateSection:', error);
        showError('حدث خطأ أثناء تحديث القسم');
    }
}

// Edit section
function editSection(sectionId) {
    const section = customSections.find(s => s.id === sectionId);
    if (!section) return;

    editingSection = sectionId;
    
    // Fill form with section data
    document.getElementById('sectionId').value = section.id;
    document.getElementById('sectionNameAr').value = section.name_ar;
    document.getElementById('sectionNameEn').value = section.name_en;
    document.getElementById('selectedIcon').value = section.icon_class;
    document.getElementById('sectionDescription').value = section.description || '';
    document.getElementById('maxMods').value = section.max_mods;
    document.getElementById('isActive').value = section.is_active.toString();

    // Update form title
    document.getElementById('formTitle').textContent = 'تعديل القسم';

    // Select icon
    document.querySelectorAll('.icon-option').forEach(option => {
        option.classList.toggle('selected', option.dataset.icon === section.icon_class);
    });

    // Select mods
    selectedMods.clear();
    if (section.selected_mods) {
        const mods = JSON.parse(section.selected_mods);
        mods.forEach(modId => selectedMods.add(modId));
    }
    renderModSelector();

    // Scroll to form
    document.querySelector('.section-form').scrollIntoView({ behavior: 'smooth' });
}

// Delete section
async function deleteSection(sectionId) {
    if (!confirm('هل أنت متأكد من حذف هذا القسم؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }

    try {
        const { error } = await supabaseClient
            .from('custom_sections')
            .delete()
            .eq('id', sectionId);

        if (error) {
            console.error('Error deleting section:', error);
            showError('حدث خطأ أثناء حذف القسم');
            return;
        }

        showSuccess('تم حذف القسم بنجاح!');
        await loadCustomSections();

    } catch (error) {
        console.error('Error in deleteSection:', error);
        showError('حدث خطأ أثناء حذف القسم');
    }
}

// Move section up
async function moveSectionUp(sectionId) {
    await reorderSection(sectionId, 'up');
}

// Move section down
async function moveSectionDown(sectionId) {
    await reorderSection(sectionId, 'down');
}

// Reorder section
async function reorderSection(sectionId, direction) {
    try {
        const currentIndex = customSections.findIndex(s => s.id === sectionId);
        if (currentIndex === -1) return;

        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
        if (newIndex < 0 || newIndex >= customSections.length) return;

        // Swap display orders
        const currentSection = customSections[currentIndex];
        const targetSection = customSections[newIndex];

        const updates = [
            {
                id: currentSection.id,
                display_order: targetSection.display_order
            },
            {
                id: targetSection.id,
                display_order: currentSection.display_order
            }
        ];

        for (const update of updates) {
            const { error } = await supabaseClient
                .from('custom_sections')
                .update({ display_order: update.display_order })
                .eq('id', update.id);

            if (error) {
                console.error('Error updating section order:', error);
                showError('حدث خطأ أثناء إعادة ترتيب الأقسام');
                return;
            }
        }

        await loadCustomSections();
        showSuccess('تم إعادة ترتيب الأقسام بنجاح!');

    } catch (error) {
        console.error('Error in reorderSection:', error);
        showError('حدث خطأ أثناء إعادة ترتيب الأقسام');
    }
}

// Reset form
function resetForm() {
    editingSection = null;
    selectedMods.clear();
    
    document.getElementById('sectionForm').reset();
    document.getElementById('sectionId').value = '';
    document.getElementById('maxMods').value = '10';
    document.getElementById('isActive').value = 'true';
    document.getElementById('formTitle').textContent = 'إضافة قسم جديد';

    // Reset icon selection
    document.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    if (AVAILABLE_ICONS.length > 0) {
        const firstIcon = document.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('selected');
            document.getElementById('selectedIcon').value = AVAILABLE_ICONS[0];
        }
    }

    renderModSelector();
}

// Show success message
function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    if (errorDiv) errorDiv.style.display = 'none';
    
    if (successDiv) {
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 5000);
    }
}

// Show error message
function showError(message) {
    const successDiv = document.getElementById('successMessage');
    const errorDiv = document.getElementById('errorMessage');
    
    if (successDiv) successDiv.style.display = 'none';
    
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }
}
