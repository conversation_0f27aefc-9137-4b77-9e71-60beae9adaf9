@echo off
chcp 65001 >nul
title نظام النسخ الاحتياطي الاحترافي - Professional Backup System

echo.
echo ===============================================================================
echo 🚀 نظام النسخ الاحتياطي الاحترافي للمودات
echo    Professional Backup System for Mods Database
echo ===============================================================================
echo.

cd /d "%~dp0"

:MENU
echo 📋 اختر العملية المطلوبة:
echo.
echo 1. إنشاء نسخة احتياطية سريعة (Quick Backup)
echo 2. نقل البيانات بين قواعد البيانات (Transfer Data)
echo 3. تبديل قاعدة البيانات النشطة (Switch Database)
echo 4. مقارنة قواعد البيانات (Compare Databases)
echo 5. عرض حالة النظام (System Status)
echo 6. بدء المراقبة التلقائية (Start Monitoring)
echo 7. بدء النسخ الاحتياطي التلقائي (Auto Backup)
echo 8. إعداد النظام (Setup System)
echo 9. خروج (Exit)
echo.

set /p choice="أدخل رقم الخيار (1-9): "

if "%choice%"=="1" goto BACKUP
if "%choice%"=="2" goto TRANSFER
if "%choice%"=="3" goto SWITCH
if "%choice%"=="4" goto COMPARE
if "%choice%"=="5" goto STATUS
if "%choice%"=="6" goto MONITOR
if "%choice%"=="7" goto AUTO_BACKUP
if "%choice%"=="8" goto SETUP
if "%choice%"=="9" goto EXIT

echo ❌ خيار غير صحيح، حاول مرة أخرى
pause
goto MENU

:BACKUP
echo.
echo 💾 إنشاء نسخة احتياطية...
python database_backup_manager.py backup
echo.
pause
goto MENU

:TRANSFER
echo.
echo 📊 نقل البيانات بين قواعد البيانات
echo.
echo قواعد البيانات المتاحة:
echo - main (الرئيسية)
echo - backup1 (الاحتياطية 1)
echo - backup2 (الاحتياطية 2)
echo.
set /p source="قاعدة البيانات المصدر: "
set /p target="قاعدة البيانات المستهدفة: "
set /p activate="تفعيل قاعدة البيانات الجديدة؟ (y/n): "

if /i "%activate%"=="y" (
    python database_backup_manager.py transfer %source% %target% --activate
) else (
    python database_backup_manager.py transfer %source% %target%
)
echo.
pause
goto MENU

:SWITCH
echo.
echo 🔄 تبديل قاعدة البيانات النشطة
echo.
echo قواعد البيانات المتاحة:
echo - main (الرئيسية)
echo - backup1 (الاحتياطية 1)
echo - backup2 (الاحتياطية 2)
echo.
set /p target="قاعدة البيانات المستهدفة: "
set /p backup="إنشاء نسخة احتياطية قبل التبديل؟ (y/n): "

if /i "%backup%"=="n" (
    python database_backup_manager.py switch %target% --no-backup
) else (
    python database_backup_manager.py switch %target%
)
echo.
pause
goto MENU

:COMPARE
echo.
echo 📊 مقارنة قواعد البيانات
echo.
set /p db1="قاعدة البيانات الأولى: "
set /p db2="قاعدة البيانات الثانية: "
python database_backup_manager.py compare %db1% %db2%
echo.
pause
goto MENU

:STATUS
echo.
echo 📈 حالة النظام
python database_backup_manager.py status
echo.
pause
goto MENU

:MONITOR
echo.
echo 🔍 بدء المراقبة التلقائية...
echo اضغط Ctrl+C لإيقاف المراقبة
python database_monitor.py start
echo.
pause
goto MENU

:AUTO_BACKUP
echo.
echo 🕐 بدء النسخ الاحتياطي التلقائي...
echo اضغط Ctrl+C لإيقاف النسخ التلقائي
python database_backup_manager.py auto
echo.
pause
goto MENU

:SETUP
echo.
echo ⚙️ إعداد النظام...
python quick_start.py
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 شكراً لاستخدام نظام النسخ الاحتياطي الاحترافي
echo    Thank you for using Professional Backup System
echo.
pause
exit

:ERROR
echo.
echo ❌ حدث خطأ في تنفيذ العملية
echo تأكد من:
echo - تثبيت Python 3.8+
echo - تثبيت المتطلبات: pip install -r requirements.txt
echo - وجود ملف backup_config.json
echo - صحة إعدادات قواعد البيانات
echo.
pause
goto MENU
