-- ========================================
-- إنشاء جدول الأقسام المخصصة - Create Custom Sections Table
-- انسخ والصق هذا الكود في Supabase SQL Editor
-- Copy and paste this code in Supabase SQL Editor
-- ========================================

-- 1. إنشاء جدول custom_sections
-- Create custom_sections table
CREATE TABLE IF NOT EXISTS custom_sections (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    section_type VARCHAR(50) DEFAULT 'custom',
    mod_selection_type VARCHAR(50) DEFAULT 'manual', -- 'manual', 'auto_popular', 'auto_recent', 'auto_category'
    auto_criteria JSONB, -- For automatic selection criteria
    max_mods_display INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول custom_section_mods (ربط الأقسام بالمودات)
-- Create custom_section_mods table (linking sections to mods)
CREATE TABLE IF NOT EXISTS custom_section_mods (
    id SERIAL PRIMARY KEY,
    section_id INTEGER REFERENCES custom_sections(id) ON DELETE CASCADE,
    mod_id INTEGER REFERENCES mods(id) ON DELETE CASCADE,
    display_order INTEGER DEFAULT 0,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(section_id, mod_id)
);

-- 3. تفعيل Row Level Security
-- Enable Row Level Security
ALTER TABLE custom_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_section_mods ENABLE ROW LEVEL SECURITY;

-- 4. إنشاء سياسات الأمان
-- Create security policies
DROP POLICY IF EXISTS "Enable read access for all users" ON custom_sections;
CREATE POLICY "Enable read access for all users" ON custom_sections
FOR SELECT USING (TRUE);

DROP POLICY IF EXISTS "Enable read access for all users" ON custom_section_mods;
CREATE POLICY "Enable read access for all users" ON custom_section_mods
FOR SELECT USING (TRUE);

-- 5. إنشاء فهارس لتحسين الأداء
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_custom_sections_active ON custom_sections(is_active);
CREATE INDEX IF NOT EXISTS idx_custom_sections_order ON custom_sections(display_order);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_section ON custom_section_mods(section_id);
CREATE INDEX IF NOT EXISTS idx_custom_section_mods_mod ON custom_section_mods(mod_id);

-- 6. إضافة بيانات تجريبية (اختياري)
-- Add sample data (optional)
INSERT INTO custom_sections (name_ar, name_en, description_ar, description_en, display_order, is_active) 
VALUES 
    ('الأكثر شعبية', 'Most Popular', 'أفضل المودات الأكثر شعبية', 'Best most popular mods', 1, true),
    ('الجديد والمميز', 'New & Featured', 'أحدث المودات المميزة', 'Latest featured mods', 2, true)
ON CONFLICT DO NOTHING;

-- 7. إنشاء دالة لتحديث updated_at تلقائياً
-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. إنشاء triggers لتحديث updated_at
-- Create triggers to update updated_at
DROP TRIGGER IF EXISTS update_custom_sections_updated_at ON custom_sections;
CREATE TRIGGER update_custom_sections_updated_at
    BEFORE UPDATE ON custom_sections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- تم إنشاء جداول الأقسام المخصصة بنجاح
-- Custom sections tables created successfully
-- ========================================
