# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاحات Supabase
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_supabase_basic():
    """اختبار أساسي لـ Supabase"""
    print("🔍 اختبار أساسي لـ Supabase...")
    
    try:
        # اختبار تحميل الوحدة
        from supabase_config import supabase_config, get_supabase_client
        print("✅ تم تحميل وحدة supabase_config بنجاح")
        
        # اختبار فحص الشبكة
        if supabase_config.check_network_connectivity():
            print("✅ الاتصال بالإنترنت متاح")
        else:
            print("❌ لا يوجد اتصال بالإنترنت")
            return False
        
        # اختبار الاتصال
        print("🔄 محاولة الاتصال بـ Supabase...")
        if supabase_config.connect():
            print("✅ نجح الاتصال بـ Supabase")
            
            # اختبار الحصول على العميل
            client = get_supabase_client()
            if client:
                print("✅ تم الحصول على عميل Supabase")
                
                # اختبار استعلام بسيط
                try:
                    response = client.table('mods').select('id').limit(1).execute()
                    print("✅ نجح الاستعلام الأساسي")
                    print(f"📊 عدد النتائج: {len(response.data) if response.data else 0}")
                    return True
                except Exception as query_e:
                    print(f"❌ فشل الاستعلام: {query_e}")
                    return False
            else:
                print("❌ فشل في الحصول على عميل Supabase")
                return False
        else:
            print("❌ فشل الاتصال بـ Supabase")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_publish_simple():
    """اختبار نشر بسيط"""
    print("\n🔍 اختبار النشر البسيط...")
    
    try:
        from supabase_config import publish_to_supabase
        
        # بيانات اختبار بسيطة (متوافقة مع هيكل قاعدة البيانات الفعلي)
        test_data = {
            'name': 'Test Mod - اختبار سريع',  # استخدام 'name' بدلاً من 'title'
            'description': 'هذا مود اختبار للتحقق من إصلاحات SSL timeout',
            'category': 'Test',
            'download_url': 'https://example.com/test.mcpack',
            'image_urls': ['https://example.com/test.jpg'],  # استخدام 'image_urls' كمصفوفة
            'size': '1.0 MB',
            'version': '1.0.0',
            'creator_name': 'Test Creator'
        }
        
        print("🔄 محاولة نشر البيانات الاختبارية...")
        if publish_to_supabase(test_data, 'mods'):
            print("✅ نجح النشر الاختباري")
            
            # محاولة حذف البيانات الاختبارية
            try:
                from supabase_config import get_supabase_client
                client = get_supabase_client()
                if client:
                    client.table('mods').delete().eq('name', 'Test Mod - اختبار سريع').execute()
                    print("🗑️ تم حذف البيانات الاختبارية")
            except:
                print("⚠️ لم يتم حذف البيانات الاختبارية")
            
            return True
        else:
            print("❌ فشل النشر الاختباري")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النشر: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار السريع لإصلاحات Supabase SSL timeout")
    print("=" * 60)
    
    # اختبار أساسي
    basic_test = test_supabase_basic()
    
    if basic_test:
        # اختبار النشر
        publish_test = test_publish_simple()
        
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار:")
        print("=" * 60)
        print(f"✅ الاختبار الأساسي: {'نجح' if basic_test else 'فشل'}")
        print(f"✅ اختبار النشر: {'نجح' if publish_test else 'فشل'}")
        
        if basic_test and publish_test:
            print("\n🎉 جميع الاختبارات نجحت! إصلاحات SSL timeout تعمل بشكل صحيح.")
            print("💡 يمكنك الآن استخدام الأداة لنشر المودات بدون مشاكل SSL timeout.")
        elif basic_test:
            print("\n⚠️ الاختبار الأساسي نجح لكن النشر فشل. قد تحتاج لفحص صلاحيات قاعدة البيانات.")
        else:
            print("\n❌ الاختبارات فشلت. تحقق من:")
            print("   1. اتصال الإنترنت")
            print("   2. مفاتيح Supabase في api_keys.json")
            print("   3. إعدادات جدار الحماية")
    else:
        print("\n❌ فشل الاختبار الأساسي. لا يمكن المتابعة.")
        print("💡 تحقق من:")
        print("   1. وجود ملف api_keys.json")
        print("   2. صحة مفتاح SUPABASE_KEY")
        print("   3. اتصال الإنترنت")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
