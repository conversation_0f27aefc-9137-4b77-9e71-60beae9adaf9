{"firebase": {"credentials_path": "app/src/main/assets/admin/download-e33a2-firebase-adminsdk-fbsvc-01f65407db.json", "storage_bucket": "download-e33a2.firebasestorage.app"}, "supabase": {"main": {"url": "https://ytqxxodyecdeosnqoure.supabase.co", "key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4", "description": "قاعدة البيانات الرئيسية الحالية"}, "backup1": {"url": "", "key": "", "description": "قاعدة البيانات الاحتياطية الأولى - يجب إضافة البيانات"}, "backup2": {"url": "", "key": "", "description": "قاعدة البيانات الاحتياطية الثانية - يجب إضافة البيانات"}}, "backup_settings": {"auto_backup_interval": 6, "max_backup_files": 50, "compress_backups": true, "include_images": true, "include_user_data": true, "backup_tables": ["mods", "user_languages", "banner_ads", "featured_addons", "free_addons", "suggested_mods", "custom_mod_dialogs", "custom_dialog_mods", "custom_copyright_mods", "entry_subscription_ads", "custom_sections", "update_notifications", "app_announcements", "drawer_links"], "critical_tables": ["mods", "user_languages", "featured_addons"]}, "monitoring": {"health_check_interval": 300, "max_failed_checks": 3, "auto_failover_enabled": true, "notification_webhook": "", "email_notifications": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}}, "security": {"encrypt_backups": false, "encryption_key": "", "backup_retention_days": 30, "verify_backup_integrity": true}, "performance": {"batch_size": 100, "max_concurrent_uploads": 3, "connection_timeout": 30, "retry_attempts": 3, "retry_delay": 5}}