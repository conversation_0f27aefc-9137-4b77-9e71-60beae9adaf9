// Unified Subscription Banner Creator JavaScript
// منشئ الحملات والبانرات الموحد

// Supabase configuration
const SUPABASE_URL = 'https://ytqxxodyecdeosnqoure.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4';
const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Global variables
let currentStep = 1;
let totalSteps = 4;
let bannerImageFile = null;
let popupImageFile = null;
let createdCampaignId = null;
let createdBannerId = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Unified Subscription Banner Creator loaded');
    setupEventListeners();
    updateProgress();
});

// Setup event listeners
function setupEventListeners() {
    // Image upload handlers
    document.getElementById('bannerImage').addEventListener('change', handleBannerImageUpload);
    document.getElementById('popupImage').addEventListener('change', handlePopupImageUpload);
    
    // Auto-fill banner title from campaign title
    document.getElementById('campaignTitleAr').addEventListener('input', function() {
        if (!document.getElementById('bannerTitle').value) {
            document.getElementById('bannerTitle').value = this.value;
        }
    });
}

// Handle banner image upload
function handleBannerImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!validateImageFile(file, 'banner')) return;
    
    bannerImageFile = file;
    showImagePreview(file, 'banner');
}

// Handle popup image upload
function handlePopupImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!validateImageFile(file, 'popup')) return;
    
    popupImageFile = file;
    showImagePreview(file, 'popup');
}

// Validate image file
function validateImageFile(file, type) {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    
    if (file.size > maxSize) {
        showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
        return false;
    }
    
    if (!allowedTypes.includes(file.type)) {
        showNotification('نوع الصورة غير مدعوم. يرجى اختيار JPG, PNG, GIF أو WebP', 'error');
        return false;
    }
    
    return true;
}

// Show image preview
function showImagePreview(file, type) {
    const container = document.getElementById('imagePreviewContainer');
    const existingPreview = container.querySelector(`[data-type="${type}"]`);
    
    if (existingPreview) {
        existingPreview.remove();
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewDiv = document.createElement('div');
        previewDiv.className = 'preview-item';
        previewDiv.setAttribute('data-type', type);
        
        previewDiv.innerHTML = `
            <img src="${e.target.result}" alt="${type} preview">
            <div class="preview-label">${type === 'banner' ? 'صورة البانر' : 'صورة النافذة المنبثقة'}</div>
        `;
        
        container.appendChild(previewDiv);
    };
    
    reader.readAsDataURL(file);
}

// Next step
function nextStep() {
    if (!validateCurrentStep()) return;
    
    if (currentStep < totalSteps) {
        currentStep++;
        updateStepDisplay();
        updateProgress();
        
        if (currentStep === 4) {
            generateSummary();
        }
    }
}

// Previous step
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
        updateProgress();
    }
}

// Update step display
function updateStepDisplay() {
    // Hide all step contents
    for (let i = 1; i <= totalSteps; i++) {
        document.getElementById(`step${i}Content`).style.display = 'none';
        document.getElementById(`step${i}`).classList.remove('active', 'completed');
    }
    
    // Show current step content
    document.getElementById(`step${currentStep}Content`).style.display = 'block';
    document.getElementById(`step${currentStep}`).classList.add('active');
    
    // Mark completed steps
    for (let i = 1; i < currentStep; i++) {
        document.getElementById(`step${i}`).classList.add('completed');
    }
    
    // Update buttons
    document.getElementById('prevBtn').style.display = currentStep > 1 ? 'block' : 'none';
    document.getElementById('nextBtn').style.display = currentStep < totalSteps ? 'block' : 'none';
    document.getElementById('createBtn').style.display = currentStep === totalSteps ? 'block' : 'none';
}

// Update progress
function updateProgress() {
    const progress = (currentStep / totalSteps) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
}

// Validate current step
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return validateStep1();
        case 2:
            return validateStep2();
        case 3:
            return validateStep3();
        case 4:
            return true; // Review step doesn't need validation
        default:
            return true;
    }
}

// Validate step 1
function validateStep1() {
    const titleAr = document.getElementById('campaignTitleAr').value.trim();
    const titleEn = document.getElementById('campaignTitleEn').value.trim();
    const descAr = document.getElementById('campaignDescAr').value.trim();
    const descEn = document.getElementById('campaignDescEn').value.trim();
    
    if (!titleAr || !titleEn) {
        showNotification('يرجى إدخال عنوان الحملة بكلا اللغتين', 'error');
        return false;
    }
    
    if (!descAr || !descEn) {
        showNotification('يرجى إدخال وصف الحملة بكلا اللغتين', 'error');
        return false;
    }
    
    return true;
}

// Validate step 2
function validateStep2() {
    const duration = document.getElementById('subscriptionDuration').value;
    
    if (!duration || duration < 1) {
        showNotification('يرجى إدخال مدة اشتراك صالحة', 'error');
        return false;
    }
    
    return true;
}

// Validate step 3
function validateStep3() {
    const bannerTitle = document.getElementById('bannerTitle').value.trim();
    
    if (!bannerTitle) {
        showNotification('يرجى إدخال عنوان البانر', 'error');
        return false;
    }
    
    if (!bannerImageFile) {
        showNotification('يرجى اختيار صورة البانر', 'error');
        return false;
    }
    
    return true;
}

// Generate summary
function generateSummary() {
    const summary = document.getElementById('campaignSummary');
    
    const titleAr = document.getElementById('campaignTitleAr').value;
    const titleEn = document.getElementById('campaignTitleEn').value;
    const duration = document.getElementById('subscriptionDuration').value;
    const maxUsers = document.getElementById('maxUsers').value || 'غير محدود';
    const bannerTitle = document.getElementById('bannerTitle').value;
    const endDate = document.getElementById('campaignEndDate').value || 'غير محدد';
    
    summary.innerHTML = `
        <div class="summary-item">
            <span class="summary-label">عنوان الحملة (عربي):</span>
            <span class="summary-value">${titleAr}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">عنوان الحملة (إنجليزي):</span>
            <span class="summary-value">${titleEn}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">مدة الاشتراك:</span>
            <span class="summary-value">${duration} يوم</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">الحد الأقصى للمستخدمين:</span>
            <span class="summary-value">${maxUsers}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">تاريخ انتهاء الحملة:</span>
            <span class="summary-value">${endDate}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">عنوان البانر:</span>
            <span class="summary-value">${bannerTitle}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">صورة البانر:</span>
            <span class="summary-value">${bannerImageFile ? 'تم اختيارها' : 'لم يتم اختيارها'}</span>
        </div>
        <div class="summary-item">
            <span class="summary-label">صورة النافذة المنبثقة:</span>
            <span class="summary-value">${popupImageFile ? 'تم اختيارها' : 'لم يتم اختيارها'}</span>
        </div>
    `;
}

// Create unified campaign
async function createUnifiedCampaign() {
    showLoading('جاري إنشاء الحملة والبانر...');
    
    try {
        // Step 1: Create campaign
        const campaignData = await createCampaign();
        createdCampaignId = campaignData.id;
        
        // Step 2: Upload images
        const bannerImageUrl = await uploadImage(bannerImageFile, 'banners');
        let popupImageUrl = null;
        if (popupImageFile) {
            popupImageUrl = await uploadImage(popupImageFile, 'popups');
        }
        
        // Step 3: Update campaign with image URLs
        await updateCampaignImages(createdCampaignId, bannerImageUrl, popupImageUrl);
        
        // Step 4: Create banner
        const bannerData = await createBanner(createdCampaignId, bannerImageUrl);
        createdBannerId = bannerData.id;
        
        hideLoading();
        showSuccessAnimation();
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الحملة والبانر:', error);
        hideLoading();
        showNotification('فشل في إنشاء الحملة والبانر: ' + error.message, 'error');
    }
}

// Create campaign
async function createCampaign() {
    const campaignData = {
        title_ar: document.getElementById('campaignTitleAr').value.trim(),
        title_en: document.getElementById('campaignTitleEn').value.trim(),
        description_ar: document.getElementById('campaignDescAr').value.trim(),
        description_en: document.getElementById('campaignDescEn').value.trim(),
        subscription_duration_days: parseInt(document.getElementById('subscriptionDuration').value),
        max_users: document.getElementById('maxUsers').value ? parseInt(document.getElementById('maxUsers').value) : null,
        end_date: document.getElementById('campaignEndDate').value ? new Date(document.getElementById('campaignEndDate').value).toISOString() : null,
        priority: parseInt(document.getElementById('campaignPriority').value),
        is_active: document.getElementById('campaignActive').checked,
        banner_image_url: '', // Will be updated later
        popup_image_url: null, // Will be updated later
        created_at: new Date().toISOString()
    };
    
    const { data, error } = await supabaseClient
        .from('free_subscription_campaigns')
        .insert([campaignData])
        .select()
        .single();
    
    if (error) throw error;
    
    return data;
}

// Upload image
async function uploadImage(file, bucket) {
    const fileName = `${bucket}_${Date.now()}_${file.name}`;
    
    const { data, error } = await supabaseClient.storage
        .from(bucket)
        .upload(fileName, file);
    
    if (error) throw error;
    
    // Get public URL
    const { data: urlData } = supabaseClient.storage
        .from(bucket)
        .getPublicUrl(fileName);
    
    return urlData.publicUrl;
}

// Update campaign images
async function updateCampaignImages(campaignId, bannerImageUrl, popupImageUrl) {
    const updateData = {
        banner_image_url: bannerImageUrl
    };
    
    if (popupImageUrl) {
        updateData.popup_image_url = popupImageUrl;
    }
    
    const { error } = await supabaseClient
        .from('free_subscription_campaigns')
        .update(updateData)
        .eq('id', campaignId);
    
    if (error) throw error;
}

// Create banner
async function createBanner(campaignId, imageUrl) {
    const bannerData = {
        title: document.getElementById('bannerTitle').value.trim(),
        description: document.getElementById('bannerDescription').value.trim(),
        image_url: imageUrl,
        display_order: parseInt(document.getElementById('bannerDisplayOrder').value),
        is_active: document.getElementById('bannerActive').checked,
        banner_type: 'subscription',
        campaign_id: campaignId,
        created_at: new Date().toISOString()
    };
    
    const { data, error } = await supabaseClient
        .from('banner_ads')
        .insert([bannerData])
        .select()
        .single();
    
    if (error) throw error;
    
    return data;
}

// Show success animation
function showSuccessAnimation() {
    // Hide all step contents
    for (let i = 1; i <= totalSteps; i++) {
        document.getElementById(`step${i}Content`).style.display = 'none';
    }
    
    // Show success animation
    document.getElementById('successAnimation').style.display = 'block';
    
    // Update buttons
    document.getElementById('prevBtn').style.display = 'none';
    document.getElementById('nextBtn').style.display = 'none';
    document.getElementById('createBtn').style.display = 'none';
    document.getElementById('finishBtn').style.display = 'block';
    
    // Mark all steps as completed
    for (let i = 1; i <= totalSteps; i++) {
        document.getElementById(`step${i}`).classList.add('completed');
        document.getElementById(`step${i}`).classList.remove('active');
    }
    
    // Update progress to 100%
    document.getElementById('progressFill').style.width = '100%';
    
    showNotification('تم إنشاء الحملة والبانر بنجاح!', 'success');
}

// Finish creation
function finishCreation() {
    window.location.href = 'index.html';
}

// Utility functions
function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notificationText');
    
    notificationText.textContent = message;
    notification.className = `notification ${type}`;
    notification.style.display = 'block';
    
    setTimeout(() => {
        hideNotification();
    }, 5000);
}

function hideNotification() {
    document.getElementById('notification').style.display = 'none';
}
